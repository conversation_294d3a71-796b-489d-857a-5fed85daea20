# Fund Manager Dockerfile
# 多阶段构建：构建阶段 + 运行阶段

# 构建阶段
FROM rust:1.75-slim as builder

# 安装构建依赖
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制 Cargo 文件
COPY Cargo.toml Cargo.lock ./

# 复制源代码
COPY src ./src
COPY config ./config
COPY templates ./templates
COPY assets ./assets

# 构建应用
RUN cargo build --release

# 运行阶段
FROM debian:bookworm-slim

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN useradd -r -s /bin/false fund_manager

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/target/release/fund_manager ./

# 复制配置文件和资源
COPY --from=builder /app/config ./config
COPY --from=builder /app/templates ./templates
COPY --from=builder /app/assets ./assets

# 创建必要的目录
RUN mkdir -p logs tmp/upload upload && \
    chown -R fund_manager:fund_manager /app

# 切换到应用用户
USER fund_manager

# 暴露端口
EXPOSE 6310

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:6310/health || exit 1

# 启动应用
CMD ["./fund_manager"]
