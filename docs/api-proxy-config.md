# API代理配置说明

## 概述

本项目采用基于环境变量的API代理配置方案，实现开发环境和生产环境的统一管理。

## 环境变量配置

### 开发环境 (.env.development)

```bash
# API代理前缀配置（开发环境）
VITE_API_PROXY_PREFIX = /server

# 后端服务地址（开发环境）
VITE_API_TARGET = http://127.0.0.1:6310
```

### 生产环境 (.env.production)

```bash
# API代理前缀配置（生产环境不需要代理前缀）
VITE_API_PROXY_PREFIX = 

# 后端服务地址（生产环境）
VITE_API_TARGET = 
```

## 工作原理

### 开发环境

1. **API调用**: `http.get("/api/user/info")`
2. **自动添加前缀**: `/server/api/user/info`
3. **Vite代理处理**: 匹配 `/server` 规则
4. **重写路径**: 移除 `/server` 前缀
5. **最终请求**: `http://127.0.0.1:6310/api/user/info`

### 生产环境

1. **API调用**: `http.get("/api/user/info")`
2. **无前缀处理**: `/api/user/info`
3. **直接请求**: `/api/user/info`（由Nginx/Caddy处理）

## 使用示例

```typescript
import { http } from "@/utils/http";

// 认证接口
export const loginApi = (data: any) => {
  return http.post("/api/login", { data });
};

// 公共接口
export const getCaptcha = () => {
  return http.get("/public/captcha");
};
```

## 生产环境Nginx配置

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://backend:6310/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 公共接口代理
    location /public/ {
        proxy_pass http://backend:6310/public/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 优势

1. **环境隔离**: 不同环境使用不同的配置文件
2. **灵活配置**: 可以轻松修改代理前缀和目标地址
3. **代码统一**: API调用代码在所有环境中保持一致
4. **部署友好**: 生产环境无需特殊处理
5. **团队协作**: 团队成员可以使用不同的本地配置

## 自定义配置

如果需要使用不同的代理前缀，只需修改对应环境的 `.env` 文件：

```bash
# 使用 /dev-api 作为代理前缀
VITE_API_PROXY_PREFIX = /dev-api
VITE_API_TARGET = http://localhost:3000
```

这样配置后，开发环境的API请求会自动添加 `/dev-api` 前缀。
