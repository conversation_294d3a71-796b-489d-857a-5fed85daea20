# 还款利润计算监控指南

## 概述

本文档描述了为还款利润计算逻辑添加的数据检测点，用于持续监测订单利润计算逻辑的正确运行。

## 数据检测点分类

### 1. 主要计算流程监控 (`repayment_calculation`)

**目标**: 监控 `RepaymentBmc::get_by_id` 方法中的利润计算流程

**关键检测点**:
- 函数调用开始和原始数据状态
- 计算触发条件判断
- 利润计算结果
- 数据累加计算
- 数据库更新操作结果

**日志示例**:
```
INFO repayment_calculation: 开始获取还款记录并执行利润计算 repayment_id="repayment:abc123" execution_timestamp=1705123456789
INFO repayment_calculation: 获取到原始还款记录数据 repayment_id="repayment:abc123" status="processing" original_profit_amount="1000.00"
INFO repayment_calculation: 状态满足计算条件，开始执行期间计算 repayment_id="repayment:abc123" status="processing"
INFO repayment_calculation: 利润计算完成 repayment_id="repayment:abc123" profit_count="50.00" is_penalty=false
```

### 2. 内部计算逻辑监控 (`repayment_calculation_internal`)

**目标**: 监控 `cal_repayment_internal` 方法的详细计算过程

**关键检测点**:
- 计算参数验证
- 时间戳转换
- 计算基数获取
- 是否需要计算的判断
- 利润类型判断（正常/逾期）
- 最终计算结果

**日志示例**:
```
INFO repayment_calculation_internal: 开始内部利润计算 repayment_id="repayment:abc123" calc_time=1705123456789 profit_calc_fee=0.05
INFO repayment_calculation_internal: 时间戳转换成功 repayment_id="repayment:abc123" current_date="2024-01-13"
INFO repayment_calculation_internal: 获取计算基数 repayment_id="repayment:abc123" principal_remain="10000.00"
```

### 3. 正常利润计算监控 (`repayment_normal_profit`)

**目标**: 监控正常利润的详细计算过程

**关键检测点**:
- 日期解析和验证
- 计算周期确定
- 时间差计算
- 利率转换
- 最终利润计算

**日志示例**:
```
INFO repayment_normal_profit: 开始计算正常利润 repayment_id="repayment:abc123" principal_remain="10000.00" current_date="2024-01-13"
INFO repayment_normal_profit: 日期计算完成 repayment_id="repayment:abc123" profit_start_date="2024-01-01" profit_end_date="2024-01-31"
INFO repayment_normal_profit: 正常利润计算完成 repayment_id="repayment:abc123" calculated_profit="50.00"
```

### 4. 逾期利润计算监控 (`repayment_penalty_profit`)

**目标**: 监控逾期利润的详细计算过程

**关键检测点**:
- 逾期开始日期计算
- 逾期天数计算
- 逾期利率应用
- 最终逾期利润计算

**日志示例**:
```
INFO repayment_penalty_profit: 开始计算逾期利润 repayment_id="repayment:abc123" principal_remain="10000.00" penalty_calc_fee=0.001
INFO repayment_penalty_profit: 逾期日期计算完成 repayment_id="repayment:abc123" penalty_start_date="2024-02-01"
INFO repayment_penalty_profit: 逾期利润计算完成 repayment_id="repayment:abc123" calculated_penalty_profit="100.00"
```

### 5. 还款审核处理监控 (`repayment_approval_process`)

**目标**: 监控还款审核通过后的金额计算和更新过程

**关键检测点**:
- 还款金额解析
- 原始剩余金额获取
- 还款计算逻辑（利润优先扣除）
- 数据库更新操作

**日志示例**:
```
INFO repayment_approval_process: 开始处理还款审核通过逻辑 parent_id="repayment:abc123" repayment_amount="500.00"
INFO repayment_approval_process: 获取还款计划原始剩余金额 original_profit_remain="200.00" original_principal_remain="10000.00"
INFO repayment_approval_process: 还款金额大于利润剩余，先扣完利润再扣本金 calculation_type="profit_and_principal"
INFO repayment_approval_process: 还款计算完成 final_profit_remain="0.00" final_principal_remain="9700.00"
```

## 监控配置

### 日志级别设置

在 `config/config.yml` 中设置日志级别为 `info` 或 `debug`:

```yaml
log:
  filter_level: "info"  # 或 "debug" 获取更详细的日志
  with_ansi: true
  to_stdout: true
  directory: "./logs"
  file_name: "repayment-monitoring.log"
  rolling: "daily"
```

### 目标过滤

可以通过环境变量设置特定目标的日志级别：

```bash
# 只显示还款计算相关的日志
export RUST_LOG="repayment_calculation=info,repayment_calculation_internal=info,repayment_normal_profit=info,repayment_penalty_profit=info,repayment_approval_process=info"

# 或者显示所有日志
export RUST_LOG="info"
```

## 监控要点

### 1. 数据一致性检查

监控以下关键数据点的一致性：
- `principal_remain` 在计算前后的变化
- `profit_amount` 的累加是否正确
- `total_remain` 是否等于 `profit_remain + principal_remain`

### 2. 计算逻辑验证

验证以下计算逻辑：
- 时间差计算是否正确
- 利率转换是否准确
- 正常利润和逾期利润的切换时机

### 3. 异常情况监控

关注以下异常情况：
- 日期格式解析失败
- 金额格式解析失败
- 数据库更新失败
- 计算结果为负数

### 4. 性能监控

监控计算性能：
- 单次计算耗时
- 数据库查询和更新耗时
- 高频调用情况

## 故障排查

### 常见问题

1. **利润计算结果为零**
   - 检查 `should_calculate` 的判断逻辑
   - 验证 `update_time` 和计算周期设置
   - 确认 `principal_remain` 不为零

2. **日期计算错误**
   - 检查 `begin_date`、`end_date`、`grace_period` 格式
   - 验证时间戳转换是否正确
   - 确认时区设置

3. **还款金额扣除错误**
   - 检查利润优先扣除逻辑
   - 验证 `profit_payment` 计算
   - 确认剩余金额更新逻辑

### 日志分析工具

可以使用以下命令分析日志：

```bash
# 查看特定还款记录的所有计算日志
grep "repayment:abc123" logs/repayment-monitoring.log

# 查看所有计算错误
grep "ERROR.*repayment_" logs/repayment-monitoring.log

# 统计计算频率
grep "开始内部利润计算" logs/repayment-monitoring.log | wc -l

# 查看计算结果分布
grep "calculated_profit" logs/repayment-monitoring.log | awk '{print $NF}' | sort -n
```

## 性能优化建议

1. **减少不必要的计算**
   - 优化 `should_calculate` 逻辑
   - 缓存计算结果
   - 批量处理多个记录

2. **数据库优化**
   - 使用事务批量更新
   - 添加适当的索引
   - 优化查询语句

3. **日志优化**
   - 在生产环境中适当调整日志级别
   - 使用异步日志记录
   - 定期清理旧日志文件

## 总结

通过这些数据检测点，您可以：
- 实时监控还款利润计算的正确性
- 快速定位计算错误和异常
- 分析计算性能和优化机会
- 确保数据一致性和业务逻辑正确性

建议在开发和测试环境中启用详细日志，在生产环境中根据需要调整日志级别。
