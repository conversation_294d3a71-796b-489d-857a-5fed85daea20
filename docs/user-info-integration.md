# 用户信息获取集成说明

## 概述

已成功集成用户信息获取功能到setToken函数中，实现登录后自动获取并存储用户详细信息。

## 实现方案

### 1. API接口添加

在 `src/api/user.ts` 中添加了获取当前用户信息的接口：

```typescript
/** 获取当前用户信息 */
export const getCurrentUser = () => {
  return http.post<{ code: number; data: UserInfo; msg: string }, any>("/api/user/current", {});
};
```

### 2. setToken函数改造

将 `setToken` 函数改为异步函数，在设置token后自动获取用户信息：

```typescript
export async function setToken(data: DataInfo<Date>) {
  // ... 设置token和cookie的逻辑

  // 获取当前用户信息
  try {
    const { getCurrentUser } = await import("@/api/user");
    const userResponse = await getCurrentUser();
    
    if (userResponse.code === 200) {
      const userData = userResponse.data;
      // 处理用户数据...
    }
  } catch (error) {
    // 错误处理...
  }
}
```

## 数据流程

### 1. 登录流程

```
用户登录 → 后端返回token → setToken(tokenData) → 自动调用getCurrentUser() → 存储用户信息
```

### 2. 用户信息处理

从后端获取的用户信息会被处理并存储到：

| 后端字段 | 存储位置 | 用途 |
|---------|---------|------|
| `username` | localStorage: `cms-username` | 用户名显示 |
| `realname` | localStorage: `cms-realname` | 真实姓名显示 |
| `is_admin` | localStorage: `cms-is_admin` | 管理员权限判断 |
| `username` | userStore: `username` | 全局用户状态 |
| `realname` | userStore: `nickname` | 用户昵称显示 |
| `is_admin` | userStore: `roles` | 权限角色数组 |

### 3. 权限映射

```typescript
// 根据is_admin字段设置权限
const roles = userData.is_admin ? ["admin"] : ["common"];
const permissions = userData.is_admin ? ["*:*:*"] : ["user:*:*"];
```

## 错误处理

### 1. 网络错误处理

如果获取用户信息失败，会使用默认值：

```typescript
catch (error) {
  console.error("获取用户信息失败:", error);
  // 使用默认值
  const avatar = "https://avatars.githubusercontent.com/u/44761321?v=4&s=60";
  const username = "admin";
  const nickname = "用户";
  const roles = ["common"];
  const permissions = ["user:*:*"];
}
```

### 2. 后端响应错误处理

如果后端返回错误码，也会使用默认值确保系统正常运行。

## 使用方式

### 1. 登录时调用

```typescript
// 在登录成功后调用
const loginResponse = await getLogin(loginData);
if (loginResponse.code === 200) {
  await setToken({
    accessToken: loginResponse.data.token,
    expires: new Date(loginResponse.data.exp * 1000),
    refreshToken: loginResponse.data.token // 如果有单独的refreshToken
  });
}
```

### 2. 获取用户信息

```typescript
// 从localStorage获取
const username = storageLocal().getItem("cms-username");
const realname = storageLocal().getItem("cms-realname");
const isAdmin = storageLocal().getItem("cms-is_admin");

// 从userStore获取
const userStore = useUserStoreHook();
const currentUsername = userStore.username;
const currentNickname = userStore.nickname;
const currentRoles = userStore.roles;
```

## 兼容性说明

### 1. 向后兼容

- 保持了原有的setToken接口
- 如果获取用户信息失败，会使用默认值
- 不会影响现有的登录流程

### 2. 异步处理

- setToken现在是异步函数，调用时需要使用await
- 动态导入API模块，避免循环依赖

## 测试验证

### 1. 登录测试

1. 使用正确的用户名密码登录
2. 检查localStorage中是否存储了用户信息
3. 检查userStore中是否设置了正确的用户状态

### 2. 权限测试

1. 使用管理员账户登录，检查roles是否为["admin"]
2. 使用普通用户登录，检查roles是否为["common"]
3. 验证权限控制是否正常工作

### 3. 错误处理测试

1. 模拟网络错误，检查是否使用默认值
2. 模拟后端错误响应，检查错误处理逻辑

## 后续优化建议

### 1. 缓存策略

考虑添加用户信息缓存，避免重复请求：

```typescript
// 检查是否已有用户信息缓存
const cachedUserInfo = storageLocal().getItem("user-cache");
if (cachedUserInfo && !isExpired(cachedUserInfo)) {
  // 使用缓存的用户信息
  return;
}
```

### 2. 权限细化

根据实际业务需求，可以进一步细化权限控制：

```typescript
// 根据用户角色设置更详细的权限
const permissions = userData.is_admin 
  ? ["*:*:*"] 
  : [`user:${userData.id}:*`, "public:*:read"];
```

### 3. 用户信息更新

添加用户信息更新机制：

```typescript
// 定期刷新用户信息
setInterval(async () => {
  await refreshUserInfo();
}, 30 * 60 * 1000); // 每30分钟刷新一次
```

## 注意事项

1. **异步调用**: setToken现在是异步函数，调用时必须使用await
2. **错误处理**: 确保网络错误不会影响登录流程
3. **权限同步**: 用户信息变更后需要同步更新权限状态
4. **安全性**: 敏感信息不要存储在localStorage中

通过以上改造，系统现在能够在用户登录后自动获取并存储完整的用户信息，为后续的权限控制和用户体验提供了基础。
