# 后端菜单数据整合说明

## 概述

本文档说明如何将后端返回的菜单数据整合到前端权限管理系统中。

## 后端数据格式

后端通过 `/api/menu/current` 接口返回以下格式的数据：

```json
{
  "code": 200,
  "data": [
    {
      "id": "menu:19jte1tgkwaxzvvzzatl",
      "name": "warehouseDetail",
      "order": 1,
      "path": "/warehouse/detail",
      "component": "pages/warehouse/detail",
      "redirect": "",
      "active": "yes",
      "title": "WarehouseDetail",
      "icon": "group",
      "keep_alive": "yes",
      "hidden": "yes",
      "is_link": "no",
      "parent": "WarehouseList",
      "remark": "仓库详情",
      "children": null
    }
  ],
  "msg": "success"
}
```

## 数据转换逻辑

### 1. 字段映射

| 后端字段 | 前端字段 | 转换逻辑 |
|---------|---------|---------|
| `name` | `name` | 直接映射 |
| `path` | `path` | 直接映射 |
| `component` | `component` | 直接映射 |
| `title` | `meta.title` | 直接映射 |
| `icon` | `meta.icon` | 直接映射 |
| `order` | `meta.rank` | 直接映射 |
| `hidden` | `meta.showLink` | `hidden !== "yes"` |
| `keep_alive` | `meta.keepAlive` | `keep_alive === "yes"` |
| `parent` | 父子关系 | 构建树形结构 |

### 2. 树形结构构建

1. **识别根节点**: `parent` 为空、`"''"`或`""`的节点
2. **建立映射**: 使用 `Map` 存储所有节点
3. **构建关系**: 根据 `parent` 字段建立父子关系
4. **排序**: 按 `order` 字段排序

### 3. 权限处理

- 默认为所有路由分配 `["admin", "common"]` 角色
- 可根据实际需求调整权限逻辑

## 核心文件

### src/api/routes.ts

```typescript
// 数据转换和API调用
export const getAsyncRoutes = () => {
  return http.request<Result>("post", "/api/menu/current").then(response => {
    if (response.code === 200) {
      const transformedData = transformBackendMenuToRoutes(response.data);
      return {
        ...response,
        data: transformedData
      };
    }
    return response;
  });
};
```

### src/router/utils.ts

```typescript
// 路由初始化逻辑
function initRouter() {
  return new Promise(resolve => {
    getAsyncRoutes().then(response => {
      if (response.code === 200) {
        handleAsyncRoutes(cloneDeep(response.data));
      }
      resolve(router);
    });
  });
}
```

### src/store/modules/permission.ts

```typescript
// 权限状态管理
export const usePermissionStore = defineStore("pure-permission", {
  actions: {
    handleWholeMenus(routes: any[]) {
      this.wholeMenus = filterNoPermissionTree(
        filterTree(ascending(this.constantMenus.concat(routes)))
      );
    }
  }
});
```

## 使用流程

1. **用户登录** → 调用 `initRouter()`
2. **获取菜单** → 调用 `getAsyncRoutes()`
3. **数据转换** → `transformBackendMenuToRoutes()`
4. **路由处理** → `handleAsyncRoutes()`
5. **权限过滤** → `filterNoPermissionTree()`
6. **菜单渲染** → `usePermissionStore.wholeMenus`

## 测试验证

使用 `src/api/routes-test.ts` 文件验证数据转换的正确性：

```typescript
import { testDataTransformation } from "@/api/routes-test";

// 在开发环境中调用测试函数
testDataTransformation();
```

## 注意事项

1. **组件路径**: 确保 `component` 字段对应的组件文件存在
2. **路由唯一性**: 确保 `name` 和 `path` 在整个应用中唯一
3. **权限控制**: 根据实际业务需求调整角色权限
4. **缓存策略**: 可配置是否缓存动态路由到本地存储

## 扩展功能

### 1. 角色权限

可以在后端数据中添加 `roles` 字段，前端据此设置权限：

```typescript
// 如果后端提供角色信息
if (item.roles) {
  route.meta.roles = item.roles.split(',');
} else {
  route.meta.roles = ["admin", "common"];
}
```

### 2. 按钮权限

可以在后端数据中添加 `auths` 字段，用于按钮级权限控制：

```typescript
if (item.auths) {
  route.meta.auths = item.auths.split(',');
}
```

### 3. 外链处理

根据 `is_link` 字段处理外链菜单：

```typescript
if (item.is_link === "yes") {
  route.meta.frameSrc = item.path;
}
```
