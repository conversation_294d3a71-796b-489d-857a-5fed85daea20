# 用户管理功能实现说明

## 概述

已成功迁移用户管理功能，包括完整的CRUD操作和基于PureTable的数据展示。

## 实现的功能

### 1. API接口 (src/api/user.ts)

已添加完整的用户管理CRUD方法：

```typescript
// 获取用户列表
export const getUserList = (params: UserListParams) => {
  return http.post<UserListResult, UserListParams>("/api/user/list", params);
};

// 创建用户
export const createUser = (data: UserCreateRequest) => {
  return http.post<{ code: number; msg: string }, UserCreateRequest>("/api/user", { data });
};

// 更新用户
export const updateUser = (data: UserUpdateRequest) => {
  return http.request<{ code: number; msg: string }>("put", "/api/user", { data });
};

// 删除用户
export const deleteUser = (id: string) => {
  return http.request<{ code: number; msg: string }>("delete", `/api/user/${id}`);
};

// 获取单个用户信息
export const getUser = (id: string) => {
  return http.get<{ code: number; data: UserInfo; msg: string }, any>(`/api/user/${id}`);
};
```

### 2. 数据类型定义

```typescript
export type UserInfo = {
  id: string;
  username: string;
  realname: string;
  is_admin: boolean;
  is_active: boolean;
  created_at: string;
};

export type UserListParams = {
  options: {
    order_by: string;
    desc: boolean;
  };
  page: {
    page: number;
    limit: number;
  };
  params?: Array<{
    var: string;
    val: string;
  }>;
};
```

### 3. 用户管理页面 (src/pages/user/index.vue)

#### 功能特性：

- **搜索功能**: 支持按用户名和真实姓名搜索
- **分页展示**: 支持分页大小调整和页码跳转
- **CRUD操作**: 新增、编辑、删除用户
- **数据验证**: 表单验证规则
- **状态管理**: 加载状态和提交状态

#### 表格列配置：

```typescript
const columns = computed(() => [
  {
    label: "ID",
    prop: "id",
    width: 200,
    cellRenderer: ({ row }: { row: UserInfo }) => {
      return row.id.length > 8 ? row.id.slice(-8) : row.id;
    }
  },
  {
    label: "用户名",
    prop: "username",
    width: 150
  },
  {
    label: "真实姓名",
    prop: "realname",
    width: 150
  },
  {
    label: "管理员",
    prop: "is_admin",
    width: 100,
    cellRenderer: ({ row }: { row: UserInfo }) => {
      return row.is_admin ? "是" : "否";
    }
  },
  {
    label: "状态",
    prop: "is_active",
    width: 100,
    cellRenderer: ({ row }: { row: UserInfo }) => {
      return row.is_active ? "启用" : "停用";
    }
  },
  {
    label: "创建时间",
    prop: "created_at",
    width: 180,
    cellRenderer: ({ row }: { row: UserInfo }) => {
      return formatDateTime(row.created_at);
    }
  },
  {
    label: "操作",
    fixed: "right",
    width: 160,
    slot: "operation"
  }
]);
```

### 4. 工具函数 (src/utils/formatTime.ts)

创建了时间格式化工具函数：

```typescript
export function formatDateTime(
  dateTime: string | Date | number,
  format: string = "YYYY-MM-DD HH:mm:ss"
): string;

export function formatDate(date: string | Date | number): string;

export function formatTime(time: string | Date | number): string;

export function formatRelativeTime(dateTime: string | Date | number): string;
```

## 页面结构

### 搜索区域
- 用户名搜索框
- 真实姓名搜索框
- 搜索、重置、新增按钮

### 数据表格
- 使用PureTable组件
- 支持排序、分页
- 自定义列渲染
- 操作按钮（编辑、删除）

### 用户表单对话框
- 用户名输入（必填）
- 真实姓名输入（必填）
- 密码输入（新增时必填）
- 管理员开关
- 状态开关

## 使用方式

### 1. 安装依赖

确保已安装PureTable：

```bash
npm install @pureadmin/table
```

### 2. 页面访问

访问路径：`/user/list`

### 3. 功能操作

1. **查看用户列表**: 页面加载时自动获取
2. **搜索用户**: 输入搜索条件后点击搜索
3. **新增用户**: 点击"新增用户"按钮
4. **编辑用户**: 点击表格中的"编辑"按钮
5. **删除用户**: 点击表格中的"删除"按钮

## API接口说明

### 请求格式

所有API请求都会自动添加环境变量配置的代理前缀（开发环境为`/server`）。

### 响应格式

```json
{
  "code": 200,
  "data": {
    "data": [...],
    "total": 100
  },
  "msg": "success"
}
```

## 注意事项

1. **权限控制**: 需要确保用户有相应的权限访问用户管理功能
2. **数据验证**: 前端和后端都需要进行数据验证
3. **错误处理**: 已添加完整的错误处理和用户提示
4. **响应式设计**: 表格支持响应式布局

## 扩展功能

可以根据需要添加以下功能：

1. **批量操作**: 批量删除、批量启用/禁用
2. **导入导出**: Excel导入导出功能
3. **角色管理**: 与角色系统集成
4. **操作日志**: 记录用户操作历史
5. **头像上传**: 用户头像管理
