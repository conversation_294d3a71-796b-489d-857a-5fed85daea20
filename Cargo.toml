[workspace]
members = [".", "entity", "migration"]
resolver = "2"

[package]
name = "payermax"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "payermax"
path = "src/main.rs"

[[bin]]
name = "batch_import"
path = "src/bin/batch_import.rs"

[[bin]]
name = "check_excel_columns"
path = "src/bin/check_excel_columns.rs"

[[bin]]
name = "check_data"
path = "src/bin/check_data.rs"

[[bin]]
name = "clean_partitions"
path = "src/bin/clean_partitions.rs"

[[bin]]
name = "batch_import_conservative"
path = "src/bin/batch_import_conservative.rs"

[[bin]]
name = "batch_import_resume"
path = "src/bin/batch_import_resume.rs"

[[bin]]
name = "validate_data"
path = "src/bin/validate_data.rs"

[[bin]]
name = "data_quality_check"
path = "src/bin/data_quality_check.rs"

[[bin]]
name = "quick_quality_check"
path = "src/bin/quick_quality_check.rs"

[[bin]]
name = "analyze_file"
path = "src/bin/analyze_file.rs"

[[bin]]
name = "check_balance_field"
path = "src/bin/check_balance_field.rs"

[[bin]]
name = "generate_sum_transactions"
path = "src/bin/generate_sum_transactions.rs"

[[bin]]
name = "query_sum_transactions"
path = "src/bin/query_sum_transactions.rs"

[dependencies]
# Local crates
entity = { path = "entity" }
migration = { path = "migration" }

# Web framework
salvo = { version = "0.82", features = [
    "serve-static",
    "cors",
    "compression",
    "websocket",
    "oapi",
] }
tokio = { version = "1.0", features = ["full"] }

# Database ORM
sea-orm = { version = "1.1.14", features = [
    "sqlx-postgres",
    "runtime-tokio-rustls",
    "macros",
    "with-chrono",
    "with-rust_decimal",
] }
sqlx = "0.8.6"

# CSV/Excel processing
csv = "1.3"
calamine = "0.30.0"

# Data types
rust_decimal = { version = "1.32", features = ["serde"] }
chrono = { version = "0.4", features = ["serde"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Error handling and utilities
anyhow = "1.0"
thiserror = "2.0"
uuid = { version = "1.0", features = ["v4", "serde"] }

# Logging
tracing = "0.1"
tracing-subscriber = "0.3"

# Environment variables
dotenvy = "0.15"
