// 系统字典定义
// 用于存储各种枚举值和状态字典

/// 还款计划状态字典
/// 按照业务流程顺序排列，支持状态流转
pub const REPAYMENT_STATUS: [(&str, &str); 7] = [
    ("draft", "草稿"),
    ("new", "待审核"),
    ("processing", "已审核"),
    ("pending", "待还款"),
    ("partial", "部分还款"),
    ("completed", "已完成"),
    ("overdue", "逾期"),
];

/// 还款日志类型字典
pub const REPAYMENT_LOG_TYPE: [(&str, &str); 3] = [
    ("REPAY", "还款记录"),
    ("REVIEW", "审核记录"),
    ("SYSTEM", "系统记录"),
];

/// 还款日志状态字典
pub const REPAYMENT_LOG_STATUS: [(&str, &str); 7] = [
    ("new", "待审核"),
    ("approved", "通过"),
    ("rejected", "拒绝"),
    ("completed", "完成"),
    ("confirmed", "确认"),
    ("pending", "待处理"),
    ("failed", "失败"),
];

/// 计算周期字典
pub const CALC_PERIOD: [(&str, &str); 5] = [
    ("ONCE", "单次"),
    ("DAY", "按天"),
    ("MONTH", "按月"),
    ("QUARTER", "按季度"),
    ("YEAR", "按年"),
];

/// 用户状态字典
pub const USER_STATUS: [(&str, &str); 2] = [("active", "激活"), ("inactive", "停用")];

/// 合同状态字典
pub const CONTRACT_STATUS: [(&str, &str); 5] = [
    ("draft", "草稿"),
    ("active", "生效"),
    ("suspended", "暂停"),
    ("completed", "完成"),
    ("cancelled", "取消"),
];

/// 金融合同状态字典
/// 按照业务流程顺序排列，支持状态流转
pub const FINANCIAL_CONTRACT_STATUS: [(&str, &str); 5] = [
    ("draft", "草稿"),
    ("new", "待审核"),
    ("processing", "已审核"),
    ("done", "已完成"),
    ("expired", "已过期"),
];

/// 订单状态字典
pub const ORDER_STATUS: [(&str, &str); 6] = [
    ("pending", "待处理"),
    ("confirmed", "已确认"),
    ("processing", "处理中"),
    ("shipped", "已发货"),
    ("delivered", "已送达"),
    ("cancelled", "已取消"),
];

// 辅助函数：根据值获取标签
impl DictHelper {
    /// 根据值获取还款状态标签
    pub fn get_repayment_status_label(value: &str) -> Option<&'static str> {
        REPAYMENT_STATUS
            .iter()
            .find(|(v, _)| *v == value)
            .map(|(_, label)| *label)
    }

    /// 根据值获取还款日志类型标签
    pub fn get_repayment_log_type_label(value: &str) -> Option<&'static str> {
        REPAYMENT_LOG_TYPE
            .iter()
            .find(|(v, _)| *v == value)
            .map(|(_, label)| *label)
    }

    /// 根据值获取还款日志状态标签
    pub fn get_repayment_log_status_label(value: &str) -> Option<&'static str> {
        REPAYMENT_LOG_STATUS
            .iter()
            .find(|(v, _)| *v == value)
            .map(|(_, label)| *label)
    }

    /// 根据值获取计算周期标签
    pub fn get_calc_period_label(value: &str) -> Option<&'static str> {
        CALC_PERIOD
            .iter()
            .find(|(v, _)| *v == value)
            .map(|(_, label)| *label)
    }

    /// 获取还款状态在字典中的索引
    pub fn get_repayment_status_index(value: &str) -> Option<usize> {
        REPAYMENT_STATUS.iter().position(|(v, _)| *v == value)
    }

    /// 根据索引获取还款状态
    pub fn get_repayment_status_by_index(index: usize) -> Option<(&'static str, &'static str)> {
        REPAYMENT_STATUS.get(index).copied()
    }

    /// 获取下一个还款状态（用于状态推进）
    pub fn get_next_repayment_status(current: &str) -> Option<(&'static str, &'static str)> {
        let current_index = Self::get_repayment_status_index(current)?;
        Self::get_repayment_status_by_index(current_index + 1)
    }

    /// 获取上一个还款状态（用于状态回退）
    pub fn get_prev_repayment_status(current: &str) -> Option<(&'static str, &'static str)> {
        let current_index = Self::get_repayment_status_index(current)?;
        if current_index == 0 {
            None
        } else {
            Self::get_repayment_status_by_index(current_index - 1)
        }
    }

    /// 验证还款状态是否有效
    pub fn is_valid_repayment_status(value: &str) -> bool {
        REPAYMENT_STATUS.iter().any(|(v, _)| *v == value)
    }

    /// 根据值获取金融合同状态标签
    pub fn get_financial_contract_status_label(value: &str) -> Option<&'static str> {
        FINANCIAL_CONTRACT_STATUS
            .iter()
            .find(|(v, _)| *v == value)
            .map(|(_, label)| *label)
    }

    /// 获取金融合同状态在字典中的索引
    pub fn get_financial_contract_status_index(value: &str) -> Option<usize> {
        FINANCIAL_CONTRACT_STATUS
            .iter()
            .position(|(v, _)| *v == value)
    }

    /// 根据索引获取金融合同状态
    pub fn get_financial_contract_status_by_index(
        index: usize,
    ) -> Option<(&'static str, &'static str)> {
        FINANCIAL_CONTRACT_STATUS.get(index).copied()
    }

    /// 获取下一个金融合同状态（用于状态推进）
    pub fn get_next_financial_contract_status(
        current: &str,
    ) -> Option<(&'static str, &'static str)> {
        let current_index = Self::get_financial_contract_status_index(current)?;
        Self::get_financial_contract_status_by_index(current_index + 1)
    }

    /// 获取上一个金融合同状态（用于状态回退）
    pub fn get_prev_financial_contract_status(
        current: &str,
    ) -> Option<(&'static str, &'static str)> {
        let current_index = Self::get_financial_contract_status_index(current)?;
        if current_index == 0 {
            None
        } else {
            Self::get_financial_contract_status_by_index(current_index - 1)
        }
    }

    /// 验证金融合同状态是否有效
    pub fn is_valid_financial_contract_status(value: &str) -> bool {
        FINANCIAL_CONTRACT_STATUS.iter().any(|(v, _)| *v == value)
    }

    /// 获取所有还款状态值
    pub fn get_all_repayment_status_values() -> Vec<&'static str> {
        REPAYMENT_STATUS.iter().map(|(v, _)| *v).collect()
    }

    /// 获取所有还款状态标签
    pub fn get_all_repayment_status_labels() -> Vec<&'static str> {
        REPAYMENT_STATUS.iter().map(|(_, label)| *label).collect()
    }
}

/// 字典辅助结构体
pub struct DictHelper;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_repayment_status_operations() {
        // 测试获取标签
        assert_eq!(
            DictHelper::get_repayment_status_label("draft"),
            Some("草稿")
        );
        assert_eq!(DictHelper::get_repayment_status_label("invalid"), None);

        // 测试获取索引
        assert_eq!(DictHelper::get_repayment_status_index("draft"), Some(0));
        assert_eq!(DictHelper::get_repayment_status_index("new"), Some(1));

        // 测试状态推进
        assert_eq!(
            DictHelper::get_next_repayment_status("draft"),
            Some(("new", "待审核"))
        );
        assert_eq!(DictHelper::get_next_repayment_status("overdue"), None);

        // 测试状态回退
        assert_eq!(
            DictHelper::get_prev_repayment_status("new"),
            Some(("draft", "草稿"))
        );
        assert_eq!(DictHelper::get_prev_repayment_status("draft"), None);

        // 测试状态验证
        assert!(DictHelper::is_valid_repayment_status("draft"));
        assert!(!DictHelper::is_valid_repayment_status("invalid"));
    }

    #[test]
    fn test_financial_contract_status_operations() {
        // 测试获取标签
        assert_eq!(
            DictHelper::get_financial_contract_status_label("draft"),
            Some("草稿")
        );
        assert_eq!(
            DictHelper::get_financial_contract_status_label("invalid"),
            None
        );

        // 测试获取索引
        assert_eq!(
            DictHelper::get_financial_contract_status_index("draft"),
            Some(0)
        );
        assert_eq!(
            DictHelper::get_financial_contract_status_index("new"),
            Some(1)
        );

        // 测试状态推进
        assert_eq!(
            DictHelper::get_next_financial_contract_status("draft"),
            Some(("new", "待审核"))
        );
        assert_eq!(
            DictHelper::get_next_financial_contract_status("expired"),
            None
        );

        // 测试状态回退
        assert_eq!(
            DictHelper::get_prev_financial_contract_status("new"),
            Some(("draft", "草稿"))
        );
        assert_eq!(
            DictHelper::get_prev_financial_contract_status("draft"),
            None
        );

        // 测试状态验证
        assert!(DictHelper::is_valid_financial_contract_status("draft"));
        assert!(!DictHelper::is_valid_financial_contract_status("invalid"));
    }
}
