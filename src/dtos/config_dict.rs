use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::default_now;
use salvo::{macros::Extractible, oapi::ToSchema};
use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Default, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct ConfigDictCreate {
    pub stable: bool,
    pub status: Option<String>,
    pub creator_id: Option<String>,
    pub creator_name: Option<String>,
    pub updater_id: Option<String>,
    pub updater_name: Option<String>,
    pub memo: Option<String>,
    pub config_type: Option<String>,
    pub config_item: Option<String>,
    pub item_default: Option<String>,
    pub item_custom: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for ConfigDictCreate {}

#[derive(Default, Debug, Clone, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct ConfigDictUpdate {
    pub id: String,
    pub stable: bool,
    pub status: Option<String>,
    pub creator_id: Option<String>,
    pub creator_name: Option<String>,
    pub updater_id: Option<String>,
    pub updater_name: Option<String>,
    pub memo: Option<String>,
    pub config_type: Option<String>,
    pub config_item: Option<String>,
    pub item_default: Option<String>,
    pub item_custom: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for ConfigDictUpdate {}

#[derive(Debug, Deserialize, Serialize, ToSchema, Default, Clone)]
pub struct ConfigDictResponse {
    pub id: String,
    pub stable: bool,
    pub status: Option<String>,
    pub creator_id: Option<String>,
    pub creator_name: Option<String>,
    pub updater_id: Option<String>,
    pub updater_name: Option<String>,
    pub memo: Option<String>,
    pub config_type: Option<String>,
    pub config_item: Option<String>,
    pub item_default: Option<String>,
    pub item_custom: Option<String>,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for ConfigDictResponse {}
