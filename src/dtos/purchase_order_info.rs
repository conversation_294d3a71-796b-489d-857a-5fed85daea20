use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::default_now;
use rust_decimal::Decimal;
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Default, <PERSON>lone, Deserialize, Ser<PERSON>ize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct PurchaseOrderInfoCreate {
    pub order_serial: String,
    pub product_serial: String,
    pub product_name: Option<String>,
    pub product_model: Option<String>,
    pub product_type: Option<String>,
    pub price: Decimal,
    pub platform_fee: Decimal,
    pub discount: Decimal,
    pub quantity: Decimal,
    pub amount: Decimal,
    pub express_company: Option<String>,
    pub express_order: Option<String>,
    pub system_remark: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for PurchaseOrderInfoCreate {}

#[derive(Default, <PERSON>lone, Deserialize, Serialize, Debug, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct PurchaseOrderInfoUpdate {
    pub id: String,
    pub order_serial: String,
    pub product_serial: String,
    pub product_name: Option<String>,
    pub product_model: Option<String>,
    pub product_type: Option<String>,
    pub price: Decimal,
    pub platform_fee: Decimal,
    pub discount: Decimal,
    pub quantity: Decimal,
    pub amount: Decimal,
    pub express_company: Option<String>,
    pub express_order: Option<String>,
    pub system_remark: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for PurchaseOrderInfoUpdate {}

#[derive(Deserialize, Serialize, Debug, Clone, Default, ToSchema)]
pub struct PurchaseOrderInfoResponse {
    pub id: String,
    pub order_serial: String,
    pub product_serial: String,
    pub product_name: Option<String>,
    pub product_model: Option<String>,
    pub product_type: Option<String>,
    pub price: Decimal,
    pub platform_fee: Decimal,
    pub discount: Decimal,
    pub quantity: Decimal,
    pub amount: Decimal,
    pub express_company: Option<String>,
    pub express_order: Option<String>,
    pub system_remark: Option<String>,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for PurchaseOrderInfoResponse {}
