use salvo::oapi::ToSchema;
use serde::{Deserialize, Serialize};

#[derive(Deserialize, <PERSON><PERSON><PERSON>, Debug, To<PERSON>che<PERSON>, <PERSON><PERSON>ult)]
pub struct CaptchaResponse {
    pub captcha_id: String,
    pub captcha_img: String,
}

#[derive(Deserialize, <PERSON><PERSON><PERSON>, Debug, To<PERSON>che<PERSON>, <PERSON><PERSON><PERSON>)]
pub struct TestInput {
    pub name: String,
    pub value: String,
}

#[derive(Debug, Deserialize, Serialize, ToSchema, <PERSON>lone)]
pub struct CommonParams {
    pub status: Option<String>,
    pub confirm: Option<bool>,
    pub remark: Option<String>,
}
