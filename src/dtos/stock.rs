use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::default_now;
use rust_decimal::Decimal;
use salvo::{macros::Extractible, oapi::ToSchema};
use serde::{Deserialize, Serialize};

#[derive(Default, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct StockCreate {
    pub sku_serial: String,
    pub warehouse: String,
    pub position: String,
    pub company: Option<String>,
    pub quantity: Decimal,
    pub location: Option<Vec<String>>,
    pub remark: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for StockCreate {}

#[derive(Default, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct StockUpdate {
    pub id: String,
    pub sku_serial: String,
    pub warehouse: String,
    pub position: String,
    pub company: Option<String>,
    pub quantity: Decimal,
    pub location: Option<Vec<String>>,
    pub remark: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for StockUpdate {}

#[derive(Default, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct StockResponse {
    pub id: String,
    pub sku_serial: String,
    pub warehouse: String,
    pub position: String,
    pub company: Option<String>,
    pub quantity: Decimal,
    pub location: Option<Vec<String>>,
    pub remark: Option<String>,
    pub created_at: i64,
    pub updated_at: i64,
}
impl Castable for StockResponse {}
