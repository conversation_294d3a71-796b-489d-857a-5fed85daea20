use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::default_now;
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use validator::Validate;

// 合同日志信息
#[derive(Default, Clone, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct ContractLogCreate {
    pub parent_id: String,            // 合同ID
    pub log_type: String,             // 日志类型，审核、状态变更等
    pub log_value: Option<String>,    // 日志记录值，根据类型来做解析
    pub log_date: Option<String>,     // 额外补充日期
    pub log_status: Option<String>,   // 日志状态，通过、拒绝、完成、确认等
    pub remark: Option<String>,       // 操作备注
    pub creater_id: Option<String>,   // 操作人ID
    pub creater_name: Option<String>, // 操作人姓名
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for ContractLogCreate {}

#[derive(Default, Clone, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct ContractLogUpdate {
    #[salvo(extract(source(from = "param")))]
    pub id: String,
    pub parent_id: Option<String>,    // 合同ID
    pub log_type: Option<String>,     // 日志类型，审核、状态变更等
    pub log_value: Option<String>,    // 日志记录值，根据类型来做解析
    pub log_date: Option<String>,     // 额外补充日期
    pub log_status: Option<String>,   // 日志状态，通过、拒绝、完成、确认等
    pub remark: Option<String>,       // 操作备注
    pub creater_id: Option<String>,   // 操作人ID
    pub creater_name: Option<String>, // 操作人姓名
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for ContractLogUpdate {}

#[derive(Debug, Deserialize, Serialize, ToSchema, Default, Clone)]
pub struct ContractLogResponse {
    pub id: String,
    pub parent_id: String,            // 合同ID
    pub log_type: String,             // 日志类型，审核、状态变更等
    pub log_value: Option<String>,    // 日志记录值，根据类型来做解析
    pub log_date: Option<String>,     // 额外补充日期
    pub log_status: Option<String>,   // 日志状态，通过、拒绝、完成、确认等
    pub remark: Option<String>,       // 操作备注
    pub creater_id: Option<String>,   // 操作人ID
    pub creater_name: Option<String>, // 操作人姓名
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for ContractLogResponse {}
