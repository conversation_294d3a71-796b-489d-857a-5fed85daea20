use crate::db::{Castable, Creatable, Patchable};
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Default, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct RoleCreate {
    pub name: String,
    pub code: Option<String>,
    pub desc: Option<String>,
    #[serde(default)]
    pub stable: bool,
    pub order: i32,
}

impl Creatable for RoleCreate {}

#[derive(Default, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct RoleUpdate {
    #[salvo(extract(source(from = "param")))]
    pub id: String,
    pub name: String,
    pub code: Option<String>,
    pub desc: Option<String>,
    #[serde(default)]
    pub stable: bool,
    pub order: i32,
}

impl Patchable for RoleUpdate {}

#[derive(Debug, Deserialize, Serialize, ToSche<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct RoleResponse {
    pub id: String,
    pub name: String,
    pub code: Option<String>,
    pub desc: Option<String>,
    pub stable: bool,
    pub order: i32,
}

impl Castable for RoleResponse {}
