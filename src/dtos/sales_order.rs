use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::default_now;
use rust_decimal::Decimal;
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Default, Clone, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct SalesOrderCreate {
    pub status: Option<String>,
    pub creator_id: Option<String>,
    pub updater_id: Option<String>,
    pub serial: String,
    // 关联合同
    pub contract_id: Option<String>,
    // 关联款项使用批次
    pub repayment_id: Option<String>,
    // 导入批次
    pub import_record: Option<String>,
    // 下单时间
    pub purchase_time: Option<String>,
    // 支付时间
    pub pay_time: Option<String>,
    pub pay_type: Option<String>,
    pub pay_info: Option<String>,
    pub customer: Option<String>,
    pub receive_phone: Option<String>,
    pub customer_phone: Option<String>,
    pub address: Option<String>,
    pub express_type: Option<String>,
    pub express_company: Option<String>,
    pub express_order: Option<String>,
    pub platform_name: Option<String>,
    pub platform_serial: Option<String>,
    pub platform_order_serial: Option<String>,
    pub platform_fee_total: Decimal,
    pub amount: Decimal,
    pub express_fee: Decimal,
    pub total_payment: Decimal,
    // 发货时间
    pub delivery_time: Option<String>,
    // 签收时间
    pub sign_time: Option<String>,
    // 订单完成时间
    pub complete_time: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for SalesOrderCreate {}
impl Patchable for SalesOrderCreate {}

#[derive(Default, Clone, Deserialize, Serialize, Debug, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct SalesOrderUpdate {
    pub id: String,
    pub status: Option<String>,
    pub creator_id: Option<String>,
    pub updater_id: Option<String>,
    pub serial: String,
    pub contract_id: Option<String>,
    pub repayment_id: Option<String>,
    // 导入批次
    pub import_record: Option<String>,
    pub purchase_time: Option<String>,
    pub pay_time: Option<String>,
    pub pay_type: Option<String>,
    pub pay_info: Option<String>,
    pub customer: Option<String>,
    pub receive_phone: Option<String>,
    pub customer_phone: Option<String>,
    pub address: Option<String>,
    pub express_type: Option<String>,
    pub express_company: Option<String>,
    pub express_order: Option<String>,
    pub platform_name: Option<String>,
    pub platform_serial: Option<String>,
    pub platform_order_serial: Option<String>,
    pub platform_fee_total: Decimal,
    pub amount: Decimal,
    pub express_fee: Decimal,
    pub total_payment: Decimal,
    // 发货时间
    pub delivery_time: Option<String>,
    // 签收时间
    pub sign_time: Option<String>,
    // 订单完成时间
    pub complete_time: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for SalesOrderUpdate {}

#[derive(Deserialize, Serialize, Debug, Clone, Default, ToSchema)]
pub struct SalesOrderResponse {
    pub id: String,
    pub status: Option<String>,
    pub creator_id: Option<String>,
    pub updater_id: Option<String>,
    pub serial: String,
    pub contract_id: Option<String>,
    pub repayment_id: Option<String>,
    // 导入批次
    pub import_record: Option<String>,
    pub purchase_time: Option<String>,
    pub pay_time: Option<String>,
    pub pay_type: Option<String>,
    pub pay_info: Option<String>,
    pub customer: Option<String>,
    pub receive_phone: Option<String>,
    pub customer_phone: Option<String>,
    pub address: Option<String>,
    pub express_type: Option<String>,
    pub express_company: Option<String>,
    pub express_order: Option<String>,
    pub platform_name: Option<String>,
    pub platform_serial: Option<String>,
    pub platform_order_serial: Option<String>,
    pub platform_fee_total: Decimal,
    pub amount: Decimal,
    pub express_fee: Decimal,
    pub total_payment: Decimal,
    // 发货时间
    pub delivery_time: Option<String>,
    // 签收时间
    pub sign_time: Option<String>,
    // 订单完成时间
    pub complete_time: Option<String>,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for SalesOrderResponse {}


#[derive(Deserialize, Serialize, Default, Debug, Clone)]
pub struct SalesOrderSummary {
    pub total_amount: Decimal,
    pub unpay_amount: Decimal,
    pub paid_amount: Decimal,
}