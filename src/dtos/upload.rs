use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::default_now;
use salvo::oapi::ToSchema;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, ToSchema)]
pub struct SingleResult {
    pub filename: String,
    pub file_type: String,
    pub path: String,
    pub url: String,
    pub md5: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, ToSchema)]
pub struct MultiResult {
    pub path: String,
    pub files: Vec<SingleResult>,
}

#[derive(Default, Clone, Deserialize, Serialize, Debug, ToSchema)]
pub struct UploadFileCreate {
    pub t_id: String,
    pub owner: String,
    pub url: String,
    pub name: String,
    pub oss_path: String,
    pub file_type: String,
    pub is_temp: bool,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for UploadFileCreate {}

#[derive(Default, Clone, Deserialize, Serialize, Debug, ToSchema)]
pub struct UploadFileUpdate {
    pub id: String,
    pub t_id: String,
    pub owner: String,
    pub url: String,
    pub name: String,
    pub oss_path: String,
    pub file_type: String,
    pub is_temp: bool,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for UploadFileUpdate {}

#[derive(Debug, Serialize, Deserialize, Default, Clone, ToSchema)]
pub struct UploadFileResponse {
    pub id: String,
    pub t_id: String,
    pub owner: String,
    pub url: String,
    pub name: String,
    pub oss_path: String,
    pub file_type: String,
    pub is_temp: bool,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for UploadFileResponse {}
