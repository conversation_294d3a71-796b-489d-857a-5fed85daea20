use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::default_now;
use rust_decimal::Decimal;
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Default, Clone, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct PurchaseOrderCreate {
    pub status: Option<String>,
    pub creator_id: Option<String>,
    pub updater_id: Option<String>,
    pub serial: String,
    pub contract_serial: Option<String>,
    pub purchase_time: Option<String>,
    pub pay_time: Option<String>,
    pub pay_type: Option<String>,
    pub pay_info: Option<String>,
    pub customer: Option<String>,
    pub receive_phone: Option<String>,
    pub customer_phone: Option<String>,
    pub address: Option<String>,
    pub express_type: Option<String>,
    pub express_company: Option<String>,
    pub express_order: Option<String>,
    pub platform_name: Option<String>,
    pub platform_serial: Option<String>,
    pub platform_order_serial: Option<String>,
    pub platform_fee_total: Decimal,
    pub amount: Decimal,
    pub express_fee: Decimal,
    pub total_payment: Decimal,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for PurchaseOrderCreate {}

#[derive(Default, Clone, Deserialize, Serialize, Debug, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct PurchaseOrderUpdate {
    pub id: String,
    pub status: Option<String>,
    pub creator_id: Option<String>,
    pub updater_id: Option<String>,
    pub serial: String,
    pub contract_serial: Option<String>,
    pub purchase_time: Option<String>,
    pub pay_time: Option<String>,
    pub pay_type: Option<String>,
    pub pay_info: Option<String>,
    pub customer: Option<String>,
    pub receive_phone: Option<String>,
    pub customer_phone: Option<String>,
    pub address: Option<String>,
    pub express_type: Option<String>,
    pub express_company: Option<String>,
    pub express_order: Option<String>,
    pub platform_name: Option<String>,
    pub platform_serial: Option<String>,
    pub platform_order_serial: Option<String>,
    pub platform_fee_total: Decimal,
    pub amount: Decimal,
    pub express_fee: Decimal,
    pub total_payment: Decimal,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for PurchaseOrderUpdate {}

#[derive(Deserialize, Serialize, Debug, Clone, Default, ToSchema)]
pub struct PurchaseOrderResponse {
    pub id: String,
    pub status: Option<String>,
    pub creator_id: Option<String>,
    pub updater_id: Option<String>,
    pub serial: String,
    pub contract_serial: Option<String>,
    pub purchase_time: Option<String>,
    pub pay_time: Option<String>,
    pub pay_type: Option<String>,
    pub pay_info: Option<String>,
    pub customer: Option<String>,
    pub receive_phone: Option<String>,
    pub customer_phone: Option<String>,
    pub address: Option<String>,
    pub express_type: Option<String>,
    pub express_company: Option<String>,
    pub express_order: Option<String>,
    pub platform_name: Option<String>,
    pub platform_serial: Option<String>,
    pub platform_order_serial: Option<String>,
    pub platform_fee_total: Decimal,
    pub amount: Decimal,
    pub express_fee: Decimal,
    pub total_payment: Decimal,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for PurchaseOrderResponse {}
