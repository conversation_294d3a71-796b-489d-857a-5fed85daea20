use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::default_now;
use rust_decimal::Decimal;
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Default, Clone, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct SalesOrderInfoCreate {
    pub order_serial: String,
    pub product_serial: String,
    pub product_name: Option<String>,
    pub product_model: Option<String>,
    pub product_type: Option<String>,
    pub sales_price: Decimal,
    pub cost_price: Decimal,
    pub platform_fee: Decimal,
    pub discount: Decimal,
    pub quantity: Decimal,
    pub total_sales_price: Decimal,
    pub total_cost_price: Decimal,
    pub express_company: Option<String>,
    pub express_order: Option<String>,
    pub system_remark: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for SalesOrderInfoCreate {}

#[derive(Default, Clone, Deserialize, Serialize, Debug, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct SalesOrderInfoUpdate {
    pub id: String,
    pub order_serial: String,
    pub product_serial: String,
    pub product_name: Option<String>,
    pub product_model: Option<String>,
    pub product_type: Option<String>,
    pub sales_price: Decimal,
    pub cost_price: Decimal,
    pub platform_fee: Decimal,
    pub discount: Decimal,
    pub quantity: Decimal,
    pub total_sales_price: Decimal,
    pub total_cost_price: Decimal,
    pub express_company: Option<String>,
    pub express_order: Option<String>,
    pub system_remark: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for SalesOrderInfoUpdate {}

#[derive(Deserialize, Serialize, Debug, Clone, Default, ToSchema)]
pub struct SalesOrderInfoResponse {
    pub id: String,
    pub order_serial: String,
    pub product_serial: String,
    pub product_name: Option<String>,
    pub product_model: Option<String>,
    pub product_type: Option<String>,
    pub sales_price: Decimal,
    pub cost_price: Decimal,
    pub platform_fee: Decimal,
    pub discount: Decimal,
    pub quantity: Decimal,
    pub total_sales_price: Decimal,
    pub total_cost_price: Decimal,
    pub express_company: Option<String>,
    pub express_order: Option<String>,
    pub system_remark: Option<String>,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for SalesOrderInfoResponse {}
