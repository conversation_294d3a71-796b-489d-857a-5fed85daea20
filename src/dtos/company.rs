use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::default_now;
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Default, Clone, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct CompanyCreate {
    pub creator: Option<String>,
    pub manager: Option<String>,
    pub name: String,
    pub serial: String,
    pub company_type: Option<String>,
    pub registered_address: Option<String>,
    pub corporate: Option<String>,
    pub social_code: Option<String>,
    pub industry_type: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for CompanyCreate {}

#[derive(Default, Clone, Deserialize, Serialize, Debug, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct CompanyUpdate {
    pub id: String,
    pub creator: Option<String>,
    pub manager: Option<String>,
    pub name: String,
    pub serial: String,
    pub company_type: Option<String>,
    pub registered_address: Option<String>,
    pub corporate: Option<String>,
    pub social_code: Option<String>,
    pub industry_type: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for CompanyUpdate {}

#[derive(Deserialize, Serialize, Debug, Default, ToSchema, Clone)]
pub struct CompanyResponse {
    pub id: String,
    pub creator: Option<String>,
    pub manager: Option<String>,
    pub name: String,
    pub serial: String,
    pub company_type: Option<String>,
    pub registered_address: Option<String>,
    pub corporate: Option<String>,
    pub social_code: Option<String>,
    pub industry_type: Option<String>,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for CompanyResponse {}
