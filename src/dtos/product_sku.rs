use crate::db::{Castable, Creatable, Patchable};
use rust_decimal::Decimal;
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use validator::Validate;

use super::upload::UploadFileResponse;

#[derive(Default, Clone, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct ProductSkuCreate {
    pub sort: i64,
    pub status: String,
    pub serial: String,
    pub product_serial: Option<String>,
    pub external_serial: Option<String>,
    pub product_type: Option<String>,
    pub sales_price: Decimal,
    pub market_price: Decimal,
    pub cost_price: Decimal,
    pub name: Option<String>,
    pub brand: Option<String>,
    pub barcode: Option<String>,
    pub model: Option<String>,
    pub specification: Option<String>,
    pub size: Option<String>,
    pub package: Option<String>,
    pub unit: Option<String>,
    pub source_area: Option<String>,
    pub desc: Option<String>,
    pub content: Option<String>,
    pub supplier_id: Option<String>,
    pub created_at: Option<i64>,
    pub updated_at: Option<i64>,
}

impl Creatable for ProductSkuCreate {}

#[derive(Default, Clone, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct ProductSkuUpdate {
    pub id: String,
    pub sort: i64,
    pub status: String,
    pub serial: String,
    pub product_serial: Option<String>,
    pub external_serial: Option<String>,
    pub product_type: Option<String>,
    pub sales_price: Decimal,
    pub market_price: Decimal,
    pub cost_price: Decimal,
    pub name: Option<String>,
    pub brand: Option<String>,
    pub barcode: Option<String>,
    pub model: Option<String>,
    pub specification: Option<String>,
    pub size: Option<String>,
    pub package: Option<String>,
    pub unit: Option<String>,
    pub source_area: Option<String>,
    pub desc: Option<String>,
    pub content: Option<String>,
    pub supplier_id: Option<String>,
    pub created_at: Option<i64>,
    pub updated_at: Option<i64>,
}

impl Patchable for ProductSkuUpdate {}

#[derive(Debug, Deserialize, Serialize, ToSchema, Default, Clone)]
pub struct ProductSkuResponse {
    pub id: String,
    pub sort: i64,
    pub status: String,
    pub serial: String,
    pub product_serial: Option<String>,
    pub external_serial: Option<String>,
    pub product_type: Option<String>,
    pub sales_price: Decimal,
    pub market_price: Decimal,
    pub cost_price: Decimal,
    pub name: Option<String>,
    pub brand: Option<String>,
    pub barcode: Option<String>,
    pub model: Option<String>,
    pub specification: Option<String>,
    pub size: Option<String>,
    pub package: Option<String>,
    pub unit: Option<String>,
    pub source_area: Option<String>,
    pub desc: Option<String>,
    pub content: Option<String>,
    pub supplier_id: Option<String>,
    pub created_at: Option<i64>,
    pub updated_at: Option<i64>,
    pub image: Option<Vec<UploadFileResponse>>,
    pub attachment: Option<Vec<UploadFileResponse>>,
}

impl Castable for ProductSkuResponse {}
