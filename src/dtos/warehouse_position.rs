use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::default_now;
use rust_decimal::Decimal;
use salvo::{macros::Extractible, oapi::ToSchema};
use serde::{Deserialize, Serialize};

#[derive(Default, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct WarehousePositionCreate {
    pub serial: String,
    pub warehouse: String,
    pub area: Option<String>,
    pub name: Option<String>,
    pub location_x: Option<String>,
    pub location_y: Option<String>,
    pub location_z: Option<String>,
    pub max_space: Decimal,
    pub position_type: String,
    pub acreage: Decimal,
    pub height: Decimal,
    pub remark: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for WarehousePositionCreate {}

#[derive(Default, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct WarehousePositionUpdate {
    pub id: String,
    pub serial: String,
    pub warehouse: String,
    pub area: Option<String>,
    pub name: Option<String>,
    pub location_x: Option<String>,
    pub location_y: Option<String>,
    pub location_z: Option<String>,
    pub max_space: Decimal,
    pub position_type: String,
    pub acreage: Decimal,
    pub height: Decimal,
    pub remark: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for WarehousePositionUpdate {}

#[derive(Default, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct WarehousePositionResponse {
    pub id: String,
    pub serial: String,
    pub warehouse: String,
    pub area: Option<String>,
    pub name: Option<String>,
    pub location_x: Option<String>,
    pub location_y: Option<String>,
    pub location_z: Option<String>,
    pub max_space: Decimal,
    pub position_type: String,
    pub acreage: Decimal,
    pub height: Decimal,
    pub remark: Option<String>,
    pub created_at: i64,
    pub updated_at: i64,
}
impl Castable for WarehousePositionResponse {}
