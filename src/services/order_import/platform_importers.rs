use super::OrderImporter;
use crate::dtos::sales_order::SalesOrderCreate;
use crate::dtos::sales_order_info::SalesOrderInfoCreate;
use crate::utils::order_sdk::{gk_order::GkOrder, vip_order::VipOrder};

/// VIP平台订单导入实现
pub struct VipOrderImporter;

impl OrderImporter for VipOrderImporter {
    type OrderType = VipOrder;

    fn get_platform_name(&self) -> String {
        "唯品会".to_string()
    }

    fn to_sales_order_create(&self, order: &Self::OrderType) -> SalesOrderCreate {
        VipOrder::to_sales_order_create(order)
    }

    fn to_sales_order_info_create(&self, order: &Self::OrderType) -> SalesOrderInfoCreate {
        VipOrder::to_sales_order_info_create(order)
    }

    fn get_order_id(&self, order: &Self::OrderType) -> String {
        order.order_id.clone()
    }
}

/// 广垦平台订单导入实现
pub struct GkOrderImporter;

impl OrderImporter for GkOrderImporter {
    type OrderType = GkOrder;

    fn get_platform_name(&self) -> String {
        "广垦".to_string()
    }

    fn to_sales_order_create(&self, order: &Self::OrderType) -> SalesOrderCreate {
        GkOrder::to_sales_order_create(order)
    }

    fn to_sales_order_info_create(&self, order: &Self::OrderType) -> SalesOrderInfoCreate {
        GkOrder::to_sales_order_info_create(order)
    }

    fn get_order_id(&self, order: &Self::OrderType) -> String {
        order.order_id.clone()
    }
}
