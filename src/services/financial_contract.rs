use crate::{
    app_writer::App<PERSON><PERSON>ult,
    db::{CreateParams, ListParams, UpdateParams, WhereOptions},
    dtos::{
        contract_log::ContractLogCreate,
        dict::<PERSON><PERSON><PERSON><PERSON><PERSON>,
        financial_contract::{FinancialContractCreate, FinancialContractUpdate},
        repayment_log::RepaymentLogCreate,
    },
    entities::financial_contract::{FinancialContract, FinancialContractBmc},
    services::{
        contract_log::ContractLogService, repayment_log::RepaymentLogService, user::UserService,
    },
};
use anyhow::anyhow;

pub struct FinancialContractService;
impl FinancialContractService {
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match FinancialContractBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    pub async fn get_list(req: ListParams) -> AppResult<Vec<FinancialContract>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = FinancialContractBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<FinancialContract> {
        match FinancialContractBmc::get_by_query(params).await {
            Ok(group) => {
                if let Some(group) = group {
                    Ok(group)
                } else {
                    Err(anyhow!("FinancialContract not found").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn get_by_id(id: String) -> AppResult<FinancialContract> {
        match FinancialContractBmc::get_by_id(&id).await {
            Ok(group) => {
                if let Some(group) = group {
                    Ok(group)
                } else {
                    Err(anyhow!("FinancialContract not found").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn create(req: CreateParams<FinancialContractCreate>) -> AppResult<String> {
        let res = FinancialContractBmc::create(req.data).await?;
        Ok(res)
    }

    pub async fn update(req: UpdateParams<FinancialContractUpdate>) -> AppResult<String> {
        FinancialContractBmc::update(req.data).await?;
        Ok("FinancialContract updated".to_string())
    }

    pub async fn delete(id: String) -> AppResult<String> {
        FinancialContractBmc::delete(id).await?;
        Ok("FinancialContract deleted".to_string())
    }

    /// 更新金融合同状态
    /// # 参数
    /// * `id` - 金融合同ID
    /// * `u_id` - 操作用户ID
    /// * `confirm` - 是否确认（true: 审核通过，false: 审核拒绝）
    /// * `remark` - 备注
    pub async fn update_status(
        id: String,
        u_id: String,
        confirm: bool,
        remark: Option<String>,
    ) -> AppResult<String> {
        // 1. 通过id获取金融合同信息
        let financial_contract = Self::get_by_id(id.clone()).await?;

        // 2. 通过u_id获取User的信息，检查权限
        let user = UserService::get_by_id(u_id.clone()).await?;
        if !user.is_admin {
            return Err(anyhow!("权限不足，只有管理员可以审核金融合同").into());
        }

        // 3. 获取当前状态
        let current_status = financial_contract.status.as_str();

        // 验证当前状态是否有效
        if !DictHelper::is_valid_financial_contract_status(current_status) {
            return Err(anyhow!("无效的当前状态: {}", current_status).into());
        }

        // 4. 根据confirm决定新状态
        let (new_status, new_status_label) = if confirm {
            // 审核通过，向后推一个状态
            match DictHelper::get_next_financial_contract_status(current_status) {
                Some((status, label)) => (status, label),
                None => {
                    return Err(
                        anyhow!("当前状态 {} 已是最终状态，无法继续推进", current_status).into(),
                    );
                }
            }
        } else {
            // 审核拒绝，向前推一个状态
            match DictHelper::get_prev_financial_contract_status(current_status) {
                Some((status, label)) => (status, label),
                None => {
                    return Err(
                        anyhow!("当前状态 {} 已是初始状态，无法回退", current_status).into(),
                    );
                }
            }
        };

        // 5. 更新金融合同状态
        let update_data = FinancialContractUpdate {
            id: id.clone(),
            name: financial_contract.name.clone(),
            status: Some(new_status.to_string()),
            product_category: Vec::new(), // 使用空向量作为默认值
            ..Default::default()
        };

        FinancialContractBmc::update(update_data).await?;

        // 6. 记录审核日志
        let log_status = if confirm { "approved" } else { "rejected" };
        let log_value = format!(
            "{{\"from_status\": \"{}\", \"to_status\": \"{}\", \"action\": \"{}\"}}",
            current_status,
            new_status,
            if confirm { "approve" } else { "reject" }
        );

        // 获取当前状态标签
        let current_status_label =
            DictHelper::get_financial_contract_status_label(current_status).unwrap_or("未知状态");

        let log_data = ContractLogCreate {
            parent_id: id,
            log_type: "audit".to_string(),
            log_value: Some(log_value),
            log_date: remark,
            log_status: Some(log_status.to_string()),
            remark: Some(format!(
                "管理员{}了金融合同状态变更：{} -> {}",
                if confirm {
                    "审核通过"
                } else {
                    "审核拒绝"
                },
                current_status_label,
                new_status_label
            )),
            creater_id: Some(u_id),
            creater_name: Some(user.username),
            ..Default::default()
        };

        ContractLogService::create(None, log_data).await?;

        Ok(format!(
            "金融合同状态已更新：{} -> {}",
            current_status_label, new_status_label
        ))
    }
}
