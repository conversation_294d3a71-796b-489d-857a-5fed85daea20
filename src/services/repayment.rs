use std::str::FromStr;

use super::{
    repayment_log::RepaymentLogService, sales_order::SalesOrderService, user::UserService,
};
use crate::{
    app_writer::AppResult,
    db::{IdParams, ListParams, RelateParams, UpdateOptions, WhereOptions},
    dtos::{
        dict::Di<PERSON><PERSON><PERSON><PERSON>,
        repayment::{RelateOrders, RepaymentCreate, RepaymentUpdate},
        repayment_log::RepaymentLogCreate,
    },
    entities::{
        repayment::{Repayment, RepaymentBmc},
        sales_order::SalesOrder,
    },
};
use anyhow::anyhow;
use rust_decimal::Decimal;

pub struct RepaymentService;

impl RepaymentService {
    /// 获取还款计划总数
    /// # 参数
    /// * `req` - 查询条件，可选
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match RepaymentBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    /// 获取还款计划列表
    /// # 参数
    /// * `req` - 列表查询参数
    pub async fn get_list(req: ListParams) -> AppResult<Vec<Repayment>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = RepaymentBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    /// 根据查询条件获取单个还款计划
    /// # 参数
    /// * `params` - 查询条件
    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Repayment> {
        match RepaymentBmc::get_by_query(params).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("Repayment not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    /// 根据ID获取还款计划
    /// # 参数
    /// * `id` - 还款计划ID
    pub async fn get_by_id(id: String) -> AppResult<Repayment> {
        match RepaymentBmc::get_by_id(&id).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("Repayment not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    /// 创建新的还款计划
    /// # 参数
    /// * `req` - 创建还款计划的数据
    pub async fn create(req: RepaymentCreate) -> AppResult<String> {
        let res = RepaymentBmc::create(req).await?;
        Ok(res)
    }

    /// 更新还款计划
    /// # 参数
    /// * `req` - 更新还款计划的数据
    pub async fn update(req: RepaymentUpdate) -> AppResult<String> {
        println!("更新的请求req: {:?}", req);
        RepaymentBmc::update(req).await?;
        Ok("Repayment updated".to_string())
    }

    /// 删除还款计划
    /// # 参数
    /// * `id` - 要删除的还款计划ID
    pub async fn delete(id: String) -> AppResult<String> {
        let res = RepaymentBmc::delete(id.clone()).await?;
        let params = vec![WhereOptions::new("repayment_id".to_string(), id.clone())];
        // 更新关联的订单信息
        SalesOrderService::update_field(params, "repayment_id", "repayment:None").await?;
        Ok(res)
    }

    // 关联还款计划和订单
    pub async fn relate_orders(req: RelateOrders) -> AppResult<String> {
        // 使用Surreal DB的关联特性，不修改sales_order的字段了，这样后续可以方便删除(?)
        RepaymentBmc::relate_to_item(
            RelateParams {
                from: req.id.clone(),
                to: req.ids.clone(),
            },
            "has_order",
        )
        .await?;

        // 暂时先保留字段的更新
        let order_ids = req.ids.clone();
        SalesOrderService::update_batch_by_ids(
            order_ids.clone(),
            vec![UpdateOptions::new(
                "repayment_id".to_string(),
                req.id.clone(),
            )],
        )
        .await?;

        // 根据订单ID统计订单总额
        let sum_params = vec![WhereOptions::new(
            "ids".to_string(),
            order_ids.clone().join(","),
        )];
        let total_payment =
            SalesOrderService::sum_string_field(sum_params, "total_payment").await?;
        RepaymentBmc::update_field(&req.id, "target_amount", &total_payment).await?;
        Ok("Repayment relate orders".to_string())
    }

    // 解除还款计划和订单的关联
    pub async fn unrelate_order(req: IdParams) -> AppResult<String> {
        let order: SalesOrder = SalesOrderService::get_by_id(req.id.clone()).await?;
        let payment_id = order.repayment_id.unwrap().to_string();
        let payment: Repayment = RepaymentService::get_by_id(payment_id.clone()).await?;
        SalesOrderService::update_field(
            vec![WhereOptions::new("id".to_string(), req.id.clone())],
            "repayment_id",
            "repayment:None",
        )
        .await?;
        RepaymentBmc::unrelate_to_item("has_order", &payment.id.unwrap().to_string(), &req.id)
            .await?;
        // 把payment的target_amount转换成Decimal
        let payment_amount = Decimal::from_str(&payment.target_amount.unwrap()).unwrap();
        let new_amount = payment_amount - order.total_payment;
        RepaymentBmc::update_field(&payment_id, "target_amount", &new_amount.to_string()).await?;

        Ok("Repayment unrelate orders".to_string())
    }

    // 更新还款计划状态，进行审核
    pub async fn update_status(
        id: String,
        u_id: String,
        confirm: bool,
        remark: Option<String>,
    ) -> AppResult<String> {
        // 1. 通过id获取repayment信息
        let repayment = Self::get_by_id(id.clone()).await?;

        // 2. 通过u_id获取User的信息，检查权限
        let user = UserService::get_by_id(u_id.clone()).await?;
        if !user.is_admin {
            return Err(anyhow!("权限不足，只有管理员可以审核还款计划").into());
        }

        // 3. 获取当前状态
        let current_status = repayment.status.as_deref().unwrap_or("draft");

        // 验证当前状态是否有效
        if !DictHelper::is_valid_repayment_status(current_status) {
            return Err(anyhow!("无效的当前状态: {}", current_status).into());
        }

        // 4. 根据confirm决定新状态
        let (new_status, new_status_label) = if confirm {
            // 审核通过，向后推一个状态
            match DictHelper::get_next_repayment_status(current_status) {
                Some((status, label)) => (status, label),
                None => {
                    return Err(
                        anyhow!("当前状态 {} 已是最终状态，无法继续推进", current_status).into(),
                    );
                }
            }
        } else {
            // 审核拒绝，向前推一个状态
            match DictHelper::get_prev_repayment_status(current_status) {
                Some((status, label)) => (status, label),
                None => {
                    return Err(
                        anyhow!("当前状态 {} 已是初始状态，无法回退", current_status).into(),
                    );
                }
            }
        };

        // 5. 更新还款计划状态
        let mut update_data = RepaymentUpdate {
            id: id.clone(),
            status: Some(new_status.to_string()),
            updater_id: Some(u_id.clone()),
            ..Default::default()
        };

        // 特殊处理：当状态从 new -> processing 时，初始化资金字段
        println!(
            "状态检查: current_status='{}', new_status='{}'",
            current_status, new_status
        );
        if current_status == "new" && new_status == "processing" {
            println!("触发资金初始化逻辑");
            // 初始化剩余本金和剩余总额为本金金额
            if let Some(principal_amount) = repayment.principal_amount.as_ref() {
                println!("设置资金字段: principal_amount={}", principal_amount);
                update_data.principal_remain = Some(principal_amount.clone());
                update_data.total_remain = Some(principal_amount.clone());
            } else {
                println!("警告: principal_amount 为空");
            }
        } else {
            println!("不满足资金初始化条件");
        }

        RepaymentBmc::update(update_data).await?;

        // 6. 记录审核日志
        let log_status = if confirm { "approved" } else { "rejected" };
        // let log_value = format!(
        //     "{{\"from_status\": \"{}\", \"to_status\": \"{}\", \"action\": \"{}\"}}",
        //     current_status,
        //     new_status,
        //     if confirm { "approve" } else { "reject" }
        // );

        // 获取当前状态标签
        let current_status_label =
            DictHelper::get_repayment_status_label(current_status).unwrap_or("未知状态");

        let log_data = RepaymentLogCreate {
            parent_id: id,
            log_type: "REVIEW".to_string(),
            log_value: Some(format!(
                "管理员{}了还款计划状态变更：{} -> {}",
                if confirm {
                    "审核通过"
                } else {
                    "审核拒绝"
                },
                current_status_label,
                new_status_label
            )),
            log_date: None,
            log_status: Some(log_status.to_string()),
            remark: remark,
            creater_id: Some(u_id),
            creater_name: Some(user.username),
            ..Default::default()
        };

        RepaymentLogService::create(None, log_data).await?;

        Ok(format!(
            "还款计划状态已更新：{} -> {}",
            current_status_label, new_status_label
        ))
    }
}
