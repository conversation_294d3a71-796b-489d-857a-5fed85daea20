use crate::{
    app_writer::AppResult,
    db::{ListParams, WhereOptions},
    dtos::import_record::{ImportRecordCreate, ImportRecordUpdate},
    entities::import_record::{ImportRecord, ImportRecordBmc},
};
use anyhow::anyhow;

pub struct ImportRecordService;
impl ImportRecordService {
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match ImportRecordBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    pub async fn get_list(req: ListParams) -> AppResult<Vec<ImportRecord>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = ImportRecordBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<ImportRecord> {
        match ImportRecordBmc::get_by_query(params).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("ImportRecord not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn get_by_id(id: String) -> AppResult<ImportRecord> {
        match ImportRecordBmc::get_by_id(&id).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("ImportRecord not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn create(req: ImportRecordCreate) -> AppResult<String> {
        ImportRecordBmc::create(req).await?;
        Ok("ImportRecord created".to_string())
    }

    pub async fn update(req: ImportRecordUpdate) -> AppResult<String> {
        ImportRecordBmc::update(req).await?;
        Ok("ImportRecord updated".to_string())
    }

    pub async fn delete(id: String) -> AppResult<String> {
        ImportRecordBmc::delete(id).await?;
        Ok("ImportRecord deleted".to_string())
    }
}
