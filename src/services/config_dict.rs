use crate::{
    app_writer::AppResult,
    db::{CreateParams, ListParams, UpdateParams, WhereOptions},
    dtos::config_dict::{ConfigDictCreate, ConfigDictUpdate},
    entities::config_dict::{ConfigDict, ConfigDictBmc},
};

use anyhow::anyhow;

pub struct ConfigDictService;
impl ConfigDictService {
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match ConfigDictBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    pub async fn get_list(req: ListParams) -> AppResult<Vec<ConfigDict>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = ConfigDictBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<ConfigDict> {
        match ConfigDictBmc::get_by_query(params).await {
            Ok(msg) => {
                if let Some(msg) = msg {
                    Ok(msg)
                } else {
                    Err(anyhow!("Group Message Not Found").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn get_by_id(id: String) -> AppResult<ConfigDict> {
        match ConfigDictBmc::get_by_id(&id).await {
            Ok(msg) => {
                if let Some(msg) = msg {
                    Ok(msg)
                } else {
                    Err(anyhow!("Group Message Not Found").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn create(req: ConfigDictCreate) -> AppResult<String> {
        ConfigDictBmc::create(req).await?;
        Ok("Group created".to_string())
    }

    pub async fn update(req: ConfigDictUpdate) -> AppResult<String> {
        ConfigDictBmc::update(req.clone()).await?;
        Ok("Group Message Has Been Updated".to_string())
    }

    pub async fn delete(id: String) -> AppResult<String> {
        ConfigDictBmc::delete(id).await?;
        Ok("Group deleted".to_string())
    }
}
