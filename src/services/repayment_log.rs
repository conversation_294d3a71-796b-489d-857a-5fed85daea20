use crate::{
    app_writer::AppResult,
    db::{ListParams, WhereOptions},
    dtos::{
        attachment::AttachmentCreate,
        dict::{REPAYMENT_LOG_STATUS, REPAYMENT_LOG_TYPE},
        repayment_log::{RepaymentLogCreate, RepaymentLogUpdate},
    },
    entities::repayment_log::{RepaymentLog, RepaymentLogBmc},
    services::{attachment::AttachmentService, repayment::RepaymentService},
    utils::upload::{generate_oss_path, init_oss, UploadMethod},
};
use anyhow::anyhow;
use rust_decimal::Decimal;
use salvo::http::form::FilePart;
use std::str::FromStr;

pub struct RepaymentLogService;

impl RepaymentLogService {
    /// 获取还款记录总数
    /// # 参数
    /// * `req` - 查询条件，可选
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match RepaymentLogBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    /// 获取还款记录列表
    /// # 参数
    /// * `req` - 列表查询参数
    pub async fn get_list(req: ListParams) -> AppResult<Vec<RepaymentLog>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = RepaymentLogBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    /// 根据查询条件获取单个还款记录
    /// # 参数
    /// * `params` - 查询条件
    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<RepaymentLog> {
        match RepaymentLogBmc::get_by_query(params).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("RepaymentLog not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    /// 根据ID获取还款记录
    /// # 参数
    /// * `id` - 还款记录ID
    pub async fn get_by_id(id: String) -> AppResult<RepaymentLog> {
        match RepaymentLogBmc::get_by_id(&id).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("RepaymentLog not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    /// 创建新的还款记录
    /// # 参数
    /// * `file` - 可选的附件文件
    /// * `obj` - 创建还款记录的数据
    pub async fn create(file: Option<FilePart>, obj: RepaymentLogCreate) -> AppResult<String> {
        // 1. 验证 log_type 是否有效
        let valid_log_types: Vec<&str> =
            REPAYMENT_LOG_TYPE.iter().map(|(value, _)| *value).collect();
        if !valid_log_types.contains(&obj.log_type.as_str()) {
            return Err(anyhow!(
                "无效的日志类型: {}，有效值为: {:?}",
                obj.log_type,
                valid_log_types
            )
            .into());
        }

        // 2. 如果是还款记录，验证 log_value 是否为有效的金额
        if obj.log_type == "REPAY" {
            if let Some(ref log_value) = obj.log_value {
                match Decimal::from_str(log_value) {
                    Ok(_) => {
                        // 金额格式有效
                    }
                    Err(_) => {
                        return Err(anyhow!(
                            "还款记录的 log_value 必须是有效的金额格式，当前值: {}",
                            log_value
                        )
                        .into());
                    }
                }
            } else {
                return Err(anyhow!("还款记录必须提供 log_value（金额）").into());
            }
        }

        // 3. 验证 log_status 是否有效（如果提供了）
        if let Some(ref log_status) = obj.log_status {
            let valid_log_statuses: Vec<&str> = REPAYMENT_LOG_STATUS
                .iter()
                .map(|(value, _)| *value)
                .collect();
            if !valid_log_statuses.contains(&log_status.as_str()) {
                return Err(anyhow!(
                    "无效的日志状态: {}，有效值为: {:?}",
                    log_status,
                    valid_log_statuses
                )
                .into());
            }
        }

        // 4. 创建还款日志记录
        let log_id = RepaymentLogBmc::create(obj).await?;

        // 5. 如果有文件，处理文件上传和附件创建
        if let Some(file) = file {
            // 生成上传路径
            let oss_path = generate_oss_path("repayment_log");

            // 上传文件
            let upload_method = init_oss();
            match upload_method.add_single(file, &oss_path).await {
                Ok(upload_result) => {
                    // 创建附件记录
                    let attachment_data = AttachmentCreate {
                        title: Some(upload_result.filename.clone()),
                        entity_type: "repayment_log".to_string(),
                        entity_id: Some(log_id.clone()),
                        attachment_type: Some("repayment_log_attachment".to_string()),
                        file_type: Some(upload_result.file_type),
                        save_dir: upload_result.path,
                        file_name: upload_result.filename,
                        file_link: upload_result.url,
                        thumb_name: None,
                        thumb_link: None,
                        status: "success".to_string(),
                        sort: 0, // 使用默认值
                        ..Default::default()
                    };

                    // 保存附件记录
                    match AttachmentService::create(attachment_data).await {
                        Ok(_) => {
                            // 附件创建成功
                        }
                        Err(e) => {
                            // 附件创建失败，记录错误但不影响主流程
                            eprintln!("附件创建失败: {}", e);
                        }
                    }
                }
                Err(e) => {
                    // 文件上传失败，记录错误但不影响主流程
                    eprintln!("文件上传失败: {}", e);
                }
            }
        }

        Ok(log_id)
    }

    /// 更新还款记录
    /// # 参数
    /// * `req` - 更新还款记录的数据
    pub async fn update(mut req: RepaymentLogUpdate) -> AppResult<String> {
        // 1. 验证 log_type 是否有效（如果提供了）
        if let Some(ref log_type) = req.log_type {
            let valid_log_types: Vec<&str> =
                REPAYMENT_LOG_TYPE.iter().map(|(value, _)| *value).collect();
            if !valid_log_types.contains(&log_type.as_str()) {
                return Err(anyhow!(
                    "无效的日志类型: {}，有效值为: {:?}",
                    log_type,
                    valid_log_types
                )
                .into());
            }
        }

        // 2. 如果是还款记录，验证 log_value 是否为有效的金额
        if let Some(ref log_type) = req.log_type {
            if log_type == "REPAY" {
                if let Some(ref log_value) = req.log_value {
                    match Decimal::from_str(log_value) {
                        Ok(_) => {
                            // 金额格式有效
                        }
                        Err(_) => {
                            return Err(anyhow!(
                                "还款记录的 log_value 必须是有效的金额格式，当前值: {}",
                                log_value
                            )
                            .into());
                        }
                    }
                } else {
                    return Err(anyhow!("还款记录必须提供 log_value（金额）").into());
                }
            }
        }

        // 3. 验证 log_status 是否有效（如果提供了）
        if let Some(ref log_status) = req.log_status {
            let valid_log_statuses: Vec<&str> = REPAYMENT_LOG_STATUS
                .iter()
                .map(|(value, _)| *value)
                .collect();
            if !valid_log_statuses.contains(&log_status.as_str()) {
                return Err(anyhow!(
                    "无效的日志状态: {}，有效值为: {:?}",
                    log_status,
                    valid_log_statuses
                )
                .into());
            }
        }

        // 4. 获取当前的还款日志记录以获取必要信息
        let current_log = Self::get_by_id(req.id.clone()).await?;

        // 5. 检查是否需要执行还款逻辑
        let should_process_repayment = {
            // 检查 log_type 是否为 REPAY（优先使用更新值，否则使用当前值）
            let log_type = req.log_type.as_ref().unwrap_or(&current_log.log_type);
            // 检查 log_status 是否为 approved（优先使用更新值，否则使用当前值）
            let log_status = req.log_status.as_ref().or(current_log.log_status.as_ref());

            log_type == "REPAY" && log_status == Some(&"approved".to_string())
        };

        if should_process_repayment {
            // 执行还款处理逻辑
            Self::process_repayment_approval(&current_log, &req).await?;

            // 强制设置 log_status 为 "completed"
            req.log_status = Some("completed".to_string());
        }

        // 6. 更新还款日志记录
        RepaymentLogBmc::update(req).await?;
        Ok("RepaymentLog updated".to_string())
    }

    /// 处理还款审核通过的逻辑
    /// # 参数
    /// * `current_log` - 当前的还款日志记录
    /// * `req` - 更新请求数据
    async fn process_repayment_approval(
        current_log: &RepaymentLog,
        req: &RepaymentLogUpdate,
    ) -> AppResult<()> {
        // 数据检测点：记录还款审核处理开始
        tracing::info!(
            target: "repayment_approval_process",
            log_id = ?current_log.id,
            parent_id = %current_log.parent_id,
            log_type = %current_log.log_type,
            current_log_value = ?current_log.log_value,
            update_log_value = ?req.log_value,
            "开始处理还款审核通过逻辑"
        );

        // 获取 log_value（优先使用更新值，否则使用当前值）
        let log_value_str = req
            .log_value
            .as_ref()
            .or(current_log.log_value.as_ref())
            .ok_or_else(|| {
                tracing::error!(
                    target: "repayment_approval_process",
                    log_id = ?current_log.id,
                    parent_id = %current_log.parent_id,
                    "还款记录缺少 log_value（金额）"
                );
                anyhow!("还款记录必须提供 log_value（金额）")
            })?;

        // 数据检测点：记录获取到的金额字符串
        tracing::info!(
            target: "repayment_approval_process",
            log_id = ?current_log.id,
            parent_id = %current_log.parent_id,
            log_value_str = %log_value_str,
            "获取到还款金额字符串"
        );

        // 解析还款金额
        let repayment_amount = Decimal::from_str(log_value_str).map_err(|e| {
            tracing::error!(
                target: "repayment_approval_process",
                log_id = ?current_log.id,
                parent_id = %current_log.parent_id,
                log_value_str = %log_value_str,
                error = %e,
                "log_value 金额格式解析失败"
            );
            anyhow!("log_value 必须是有效的金额格式: {}", log_value_str)
        })?;

        // 数据检测点：记录解析后的金额
        tracing::info!(
            target: "repayment_approval_process",
            log_id = ?current_log.id,
            parent_id = %current_log.parent_id,
            repayment_amount = %repayment_amount,
            "还款金额解析成功"
        );

        // 通过 parent_id 查询 Repayment 信息
        let mut repayment = RepaymentService::get_by_id(current_log.parent_id.clone())
            .await
            .map_err(|e| {
                tracing::error!(
                    target: "repayment_approval_process",
                    log_id = ?current_log.id,
                    parent_id = %current_log.parent_id,
                    error = %e,
                    "查询还款计划失败"
                );
                anyhow!("查询还款计划失败，parent_id: {}", current_log.parent_id)
            })?;

        // 解析 repayment 的剩余金额字段
        let profit_remain = repayment
            .profit_remain
            .as_ref()
            .and_then(|s| Decimal::from_str(s).ok())
            .unwrap_or(Decimal::ZERO);

        let principal_remain = repayment
            .principal_remain
            .as_ref()
            .and_then(|s| Decimal::from_str(s).ok())
            .unwrap_or(Decimal::ZERO);

        let total_remain = repayment
            .total_remain
            .as_ref()
            .and_then(|s| Decimal::from_str(s).ok())
            .unwrap_or(Decimal::ZERO);

        // 数据检测点：记录还款计划的原始剩余金额
        tracing::info!(
            target: "repayment_approval_process",
            log_id = ?current_log.id,
            parent_id = %current_log.parent_id,
            original_profit_remain = %profit_remain,
            original_principal_remain = %principal_remain,
            original_total_remain = %total_remain,
            repayment_amount = %repayment_amount,
            "获取还款计划原始剩余金额"
        );

        // 计算还款逻辑
        let profit_payment = repayment_amount - profit_remain;

        // 数据检测点：记录利润支付计算
        tracing::info!(
            target: "repayment_approval_process",
            log_id = ?current_log.id,
            parent_id = %current_log.parent_id,
            repayment_amount = %repayment_amount,
            profit_remain = %profit_remain,
            profit_payment = %profit_payment,
            "计算利润支付金额"
        );

        let (new_profit_remain, new_principal_remain, new_total_remain) =
            if profit_payment <= Decimal::ZERO {
                // 还款金额 <= 利润剩余，只扣除利润
                let new_profit = profit_remain - repayment_amount;
                let new_total = total_remain - repayment_amount;

                // 数据检测点：记录只扣除利润的情况
                tracing::info!(
                    target: "repayment_approval_process",
                    log_id = ?current_log.id,
                    parent_id = %current_log.parent_id,
                    calculation_type = "profit_only",
                    original_profit_remain = %profit_remain,
                    repayment_amount = %repayment_amount,
                    new_profit_remain = %new_profit,
                    unchanged_principal_remain = %principal_remain,
                    new_total_remain = %new_total,
                    "还款金额小于等于利润剩余，只扣除利润"
                );

                (new_profit, principal_remain, new_total)
            } else {
                // 还款金额 > 利润剩余，先扣完利润，再扣本金
                let new_principal = principal_remain - profit_payment;
                let new_total = new_principal; // total_remain = 新的 principal_remain

                // 数据检测点：记录扣除利润和本金的情况
                tracing::info!(
                    target: "repayment_approval_process",
                    log_id = ?current_log.id,
                    parent_id = %current_log.parent_id,
                    calculation_type = "profit_and_principal",
                    original_profit_remain = %profit_remain,
                    original_principal_remain = %principal_remain,
                    profit_payment = %profit_payment,
                    new_profit_remain = "0",
                    new_principal_remain = %new_principal,
                    new_total_remain = %new_total,
                    "还款金额大于利润剩余，先扣完利润再扣本金"
                );

                (Decimal::ZERO, new_principal, new_total)
            };

        // 数据检测点：记录最终计算结果
        tracing::info!(
            target: "repayment_approval_process",
            log_id = ?current_log.id,
            parent_id = %current_log.parent_id,
            repayment_amount = %repayment_amount,
            original_profit_remain = %profit_remain,
            original_principal_remain = %principal_remain,
            original_total_remain = %total_remain,
            final_profit_remain = %new_profit_remain,
            final_principal_remain = %new_principal_remain,
            final_total_remain = %new_total_remain,
            "还款计算完成"
        );

        // 更新 repayment 的剩余金额
        repayment.profit_remain = Some(new_profit_remain.to_string());
        repayment.principal_remain = Some(new_principal_remain.to_string());
        repayment.total_remain = Some(new_total_remain.to_string());

        // 构建更新请求
        use crate::dtos::repayment::RepaymentUpdate;
        let update_req = RepaymentUpdate {
            id: current_log.parent_id.clone(),
            profit_remain: Some(new_profit_remain.to_string()),
            principal_remain: Some(new_principal_remain.to_string()),
            total_remain: Some(new_total_remain.to_string()),
            ..Default::default()
        };

        // 数据检测点：记录数据库更新操作
        tracing::info!(
            target: "repayment_approval_process",
            log_id = ?current_log.id,
            parent_id = %current_log.parent_id,
            update_profit_remain = %new_profit_remain,
            update_principal_remain = %new_principal_remain,
            update_total_remain = %new_total_remain,
            "开始更新还款计划到数据库"
        );

        // 保存 repayment
        RepaymentService::update(update_req).await.map_err(|e| {
            tracing::error!(
                target: "repayment_approval_process",
                log_id = ?current_log.id,
                parent_id = %current_log.parent_id,
                error = %e,
                "更新还款计划失败"
            );
            anyhow!("更新还款计划失败: {}", e)
        })?;

        // 数据检测点：记录处理完成
        tracing::info!(
            target: "repayment_approval_process",
            log_id = ?current_log.id,
            parent_id = %current_log.parent_id,
            "还款审核处理完成"
        );

        Ok(())
    }

    /// 删除还款记录
    /// # 参数
    /// * `id` - 要删除的还款记录ID
    pub async fn delete(id: String) -> AppResult<String> {
        RepaymentLogBmc::delete(id).await?;
        Ok("RepaymentLog deleted".to_string())
    }
}
