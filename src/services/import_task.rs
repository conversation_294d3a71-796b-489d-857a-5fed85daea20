use std::path::Path;

use crate::{
    app_writer::AppResult,
    db::WhereOptions,
    dtos::import_record::{ImportRecordCreate, ImportRecordUpdate},
    services::{
        import_record::ImportRecordService,
        order_import::{
            platform_importers::{GkOrderImporter, VipOrderImporter},
            OrderImportService,
        },
    },
    utils::order_sdk::{gk_order, vip_order},
};
use anyhow::anyhow;
use chrono::Local;

/// 异步导入任务参数
#[derive(Debug, Clone)]
pub struct AsyncImportParams {
    pub serial: String,
    pub platform: String,
    pub file_path: String,
    pub sheet_name: Option<String>,
    pub contract_id: String,
}

/// 异步导入任务处理服务
pub struct ImportTaskService;

impl ImportTaskService {
    /// 执行异步导入任务
    ///
    /// # 参数
    /// * `params` - 异步导入参数
    ///
    /// # 功能
    /// 1. 根据平台类型解析文件并导入数据
    /// 2. 更新对应的 ImportRecord 状态和统计信息
    /// 3. 处理导入过程中的错误
    pub async fn execute_async_import(params: AsyncImportParams) -> AppResult<()> {
        println!("开始执行异步导入任务，serial: {}", params.serial);

        // 查找对应的 ImportRecord
        let query_params = vec![WhereOptions::new(
            "serial".to_string(),
            params.serial.clone(),
        )];
        let import_record = match ImportRecordService::get_by_query(query_params).await {
            Ok(record) => record,
            Err(e) => {
                eprintln!("查找导入记录失败: {}", e);
                return Err(e);
            }
        };

        // 执行实际的导入操作
        let import_result = Self::perform_import(&params).await;

        // 更新 ImportRecord 状态
        Self::update_import_record_status(import_record, import_result).await?;

        println!("异步导入任务完成，serial: {}", params.serial);
        Ok(())
    }

    /// 执行实际的导入操作
    async fn perform_import(
        params: &AsyncImportParams,
    ) -> Result<ImportRecordCreate, anyhow::Error> {
        // 使用新的异步包装函数
        OrderImportService::import_async(
            Path::new(&params.file_path),
            params.sheet_name.clone(),
            Some(params.contract_id.clone()),
            &params.platform,
            Some(params.serial.clone()),
        )
        .await
        .map_err(|e| anyhow!("导入失败: {}", e))
    }

    /// 更新 ImportRecord 的状态和统计信息
    async fn update_import_record_status(
        import_record: crate::entities::import_record::ImportRecord,
        import_result: Result<ImportRecordCreate, anyhow::Error>,
    ) -> AppResult<()> {
        let record_id = import_record.id.unwrap().to_string();
        let final_id = if record_id.contains(':') {
            record_id
                .split(':')
                .nth(1)
                .unwrap_or(&record_id)
                .to_string()
        } else {
            record_id
        };

        let update_data = match import_result {
            Ok(result) => {
                // 导入成功，更新统计信息
                let status = if result.fail_count > 0 {
                    "partial_success".to_string()
                } else {
                    "success".to_string()
                };

                ImportRecordUpdate {
                    id: final_id,
                    serial: Some(import_record.serial),
                    contract_id: import_record.contract_id,
                    contract_name: import_record.contract_name,
                    source: import_record.source,
                    file_type: import_record.file_type,
                    save_dir: import_record.save_dir,
                    file_name: import_record.file_name,
                    file_link: import_record.file_link,
                    total_count: result.total_count,
                    new_count: result.new_count,
                    update_count: result.update_count,
                    no_change_count: result.no_change_count,
                    success_count: result.success_count,
                    fail_count: result.fail_count,
                    status,
                    created_at: import_record.created_at,
                    updated_at: Local::now().timestamp_millis(),
                }
            }
            Err(e) => {
                // 导入失败，设置失败状态
                eprintln!("导入失败: {}", e);
                ImportRecordUpdate {
                    id: final_id,
                    serial: Some(import_record.serial),
                    contract_id: import_record.contract_id,
                    contract_name: import_record.contract_name,
                    source: import_record.source,
                    file_type: import_record.file_type,
                    save_dir: import_record.save_dir,
                    file_name: import_record.file_name,
                    file_link: import_record.file_link,
                    total_count: 0,
                    new_count: 0,
                    update_count: 0,
                    no_change_count: 0,
                    success_count: 0,
                    fail_count: 0,
                    status: "failed".to_string(),
                    created_at: import_record.created_at,
                    updated_at: Local::now().timestamp_millis(),
                }
            }
        };

        // 执行更新
        ImportRecordService::update(update_data).await?;
        Ok(())
    }

    /// 启动异步导入任务
    ///
    /// # 参数
    /// * `params` - 异步导入参数
    ///
    /// # 功能
    /// 使用 tokio::spawn 启动异步任务，不阻塞当前线程
    pub fn spawn_async_import(params: AsyncImportParams) {
        tokio::spawn(async move {
            if let Err(e) = Self::execute_async_import(params).await {
                eprintln!("异步导入任务执行失败: {}", e);
            }
        });
    }
}
