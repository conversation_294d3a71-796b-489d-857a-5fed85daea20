use crate::{
    app_writer::AppResult,
    db::{ListParams, WhereOptions},
    dtos::{
        attachment::AttachmentCreate,
        contract_log::{ContractLogCreate, ContractLogUpdate},
    },
    entities::contract_log::{ContractLog, ContractLogBmc},
    services::attachment::AttachmentService,
    utils::upload::{generate_oss_path, init_oss, UploadMethod},
};
use anyhow::anyhow;
use salvo::http::form::FilePart;

pub struct ContractLogService;

impl ContractLogService {
    /// 获取合同日志总数
    /// # 参数
    /// * `req` - 查询条件，可选
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match ContractLogBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    /// 获取合同日志列表
    /// # 参数
    /// * `req` - 列表查询参数
    pub async fn get_list(req: ListParams) -> AppResult<Vec<ContractLog>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = ContractLogBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    /// 根据查询条件获取单个合同日志
    /// # 参数
    /// * `params` - 查询条件
    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<ContractLog> {
        match ContractLogBmc::get_by_query(params).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("ContractLog not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    /// 根据ID获取合同日志
    /// # 参数
    /// * `id` - 合同日志ID
    pub async fn get_by_id(id: String) -> AppResult<ContractLog> {
        match ContractLogBmc::get_by_id(&id).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("ContractLog not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    /// 创建新的合同日志
    /// 与repayment_log不一样的地方在于，合同日志的附件是关联到financial_contract的，而不是contract_log
    /// # 参数
    /// * `file` - 可选的附件文件
    /// * `obj` - 创建合同日志的数据
    pub async fn create(file: Option<FilePart>, obj: ContractLogCreate) -> AppResult<String> {
        // 创建合同日志记录
        let log_id = ContractLogBmc::create(obj.clone()).await?;

        // 如果有文件，处理文件上传和附件创建
        if let Some(file) = file {
            // 生成上传路径
            let oss_path = generate_oss_path("financial_contract");

            // 上传文件
            let upload_method = init_oss();
            match upload_method.add_single(file, &oss_path).await {
                Ok(upload_result) => {
                    // 创建附件记录
                    let attachment_data = AttachmentCreate {
                        title: Some(upload_result.filename.clone()),
                        entity_type: "financial_contract".to_string(),
                        entity_id: Some(obj.parent_id),
                        attachment_type: Some("financial_contract_attachment".to_string()),
                        file_type: Some(upload_result.file_type),
                        save_dir: upload_result.path,
                        file_name: upload_result.filename,
                        file_link: upload_result.url,
                        thumb_name: None,
                        thumb_link: None,
                        status: "success".to_string(),
                        sort: 0, // 使用默认值
                        ..Default::default()
                    };

                    // 保存附件记录
                    match AttachmentService::create(attachment_data).await {
                        Ok(_) => {
                            // 附件创建成功
                        }
                        Err(e) => {
                            // 附件创建失败，记录错误但不影响主流程
                            eprintln!("附件创建失败: {}", e);
                        }
                    }
                }
                Err(e) => {
                    // 文件上传失败，记录错误但不影响主流程
                    eprintln!("文件上传失败: {}", e);
                }
            }
        }

        Ok(log_id)
    }

    /// 更新合同日志
    /// # 参数
    /// * `req` - 更新合同日志的数据
    pub async fn update(req: ContractLogUpdate) -> AppResult<String> {
        // 更新合同日志记录
        ContractLogBmc::update(req).await?;
        Ok("ContractLog updated".to_string())
    }

    /// 删除合同日志
    /// # 参数
    /// * `id` - 要删除的合同日志ID
    pub async fn delete(id: String) -> AppResult<String> {
        ContractLogBmc::delete(id).await?;
        Ok("ContractLog deleted".to_string())
    }
}
