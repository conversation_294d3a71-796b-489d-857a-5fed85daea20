use anyhow::anyhow;

use crate::{
    app_writer::AppResult,
    db::{ListParams, WhereOptions},
    dtos::purchase_order_info::{PurchaseOrderInfoCreate, PurchaseOrderInfoUpdate},
    entities::purchase_order_info::{PurchaseOrderInfo, PurchaseOrderInfoBmc},
};

pub struct PurchaseOrderInfoService;
impl PurchaseOrderInfoService {
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match PurchaseOrderInfoBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    pub async fn get_list(req: ListParams) -> AppResult<Vec<PurchaseOrderInfo>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = PurchaseOrderInfoBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<PurchaseOrderInfo> {
        match PurchaseOrderInfoBmc::get_by_query(params).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("PurchaseOrderInfo not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn get_by_id(id: String) -> AppResult<PurchaseOrderInfo> {
        match PurchaseOrderInfoBmc::get_by_id(id).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("PurchaseOrderInfo not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn create(req: PurchaseOrderInfoCreate) -> AppResult<String> {
        PurchaseOrderInfoBmc::create(req).await?;
        Ok("PurchaseOrderInfo created".to_string())
    }

    pub async fn update(req: PurchaseOrderInfoUpdate) -> AppResult<String> {
        PurchaseOrderInfoBmc::update(req).await?;
        Ok("PurchaseOrderInfo updated".to_string())
    }

    pub async fn delete(id: String) -> AppResult<String> {
        PurchaseOrderInfoBmc::delete(id).await?;
        Ok("PurchaseOrderInfo deleted".to_string())
    }
}
