use crate::{
    app_writer::AppResult,
    db::{
        CreateParams, FlexibleParams, ListParams, Page, UpdateOptions, UpdateParams, WhereOptions,
    },
    dtos::user::{UserCreate, UserUpdate},
    entities::user::{User, UserBmc},
    utils::rand_utils,
};
use anyhow::anyhow;

use super::role::RoleService;

pub struct UserService;
impl UserService {
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match UserBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => return Ok(0),
        }
    }

    pub async fn get_list(req: ListParams) -> AppResult<Vec<User>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = UserBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_list_by_role(req: FlexibleParams) -> AppResult<Vec<User>> {
        if req.id.is_none() {
            return Err(anyhow!("Id Params is bad").into());
        }
        let check = match RoleService::get_by_id(req.id.unwrap().id.clone()).await {
            Ok(role) => role,
            Err(e) => return Err(e),
        };
        if check.code.is_none() {
            return Err(anyhow!("Role Code is bad").into());
        }
        let mut page = Page::default();
        if req.page.is_some() {
            page = req.page.unwrap();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        where_options.push(WhereOptions {
            var: "role_id".to_string(),
            val: check.id.to_string(),
            operator: "=".to_string(),
        });
        let res =
            UserBmc::get_list(page.get_offset(), page.limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_id(id: String) -> AppResult<User> {
        match UserBmc::get_by_id(&id).await {
            Ok(user) => {
                if let Some(user) = user {
                    Ok(user)
                } else {
                    println!("用户不存在1");
                    Err(anyhow!("用户不存在。").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn get_by_login_name(login_name: String) -> AppResult<User> {
        let mut where_options = Vec::new();
        where_options.push(WhereOptions {
            var: "login_name".to_string(),
            val: login_name.clone(),
            operator: "=".to_string(),
        });
        match UserBmc::get_by_query(where_options).await {
            Ok(user) => {
                if let Some(user) = user {
                    Ok(user)
                } else {
                    println!("用户不存在3");
                    Err(anyhow!("用户不存在。").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn create(mut req: CreateParams<UserCreate>) -> AppResult<String> {
        let check = UserBmc::get_by_query(vec![WhereOptions::new(
            "login_name".to_string(),
            req.data.login_name.clone(),
        )])
        .await?;
        if check.is_some() {
            return Err(anyhow!("用户名已存在。").into());
        }
        let hash_password = rand_utils::hash_password(req.data.login_pwd.clone()).await?;
        req.data.login_pwd = hash_password;
        UserBmc::create(req.data).await?;

        Ok("User created".to_string())
    }

    pub async fn update(mut req: UpdateParams<UserUpdate>) -> AppResult<String> {
        if req.data.login_pwd.is_some() {
            if req.data.login_pwd.clone().unwrap() != "******" {
                let hash_password =
                    rand_utils::hash_password(req.data.login_pwd.clone().unwrap()).await?;
                req.data.login_pwd = Some(hash_password);
            }
        }
        UserBmc::update(req.data).await?;
        Ok("User updated".to_string())
    }

    pub async fn update_fields(id: String, fields: Vec<UpdateOptions>) -> AppResult<String> {
        UserBmc::update_fields(id, fields).await?;
        Ok("User updated".to_string())
    }

    pub async fn delete(id: String) -> AppResult<String> {
        UserBmc::delete(id).await?;
        Ok("User deleted".to_string())
    }
}
