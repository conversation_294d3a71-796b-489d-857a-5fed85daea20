use anyhow::anyhow;

use crate::{
    app_writer::AppResult,
    db::{ListParams, WhereOptions},
    dtos::product_attribute::{ProductAttributeCreate, ProductAttributeUpdate},
    entities::product_attribute::{ProductAttribute, ProductAttributeBmc},
};

pub struct ProductAttributeService;
impl ProductAttributeService {
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match ProductAttributeBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    pub async fn get_list(req: ListParams) -> AppResult<Vec<ProductAttribute>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = ProductAttributeBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<ProductAttribute> {
        match ProductAttributeBmc::get_by_query(params).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("ProductAttribute not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn get_by_id(id: String) -> AppResult<ProductAttribute> {
        match ProductAttributeBmc::get_by_id(id).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("ProductAttribute not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn create(req: ProductAttributeCreate) -> AppResult<String> {
        ProductAttributeBmc::create(req).await?;
        Ok("ProductAttribute created".to_string())
    }

    pub async fn update(req: ProductAttributeUpdate) -> AppResult<String> {
        ProductAttributeBmc::update(req).await?;
        Ok("ProductAttribute updated".to_string())
    }

    pub async fn delete(id: String) -> AppResult<String> {
        ProductAttributeBmc::delete(id).await?;
        Ok("ProductAttribute deleted".to_string())
    }
}
