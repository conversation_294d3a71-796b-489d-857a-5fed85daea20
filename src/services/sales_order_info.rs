use anyhow::anyhow;

use crate::{
    app_writer::AppResult,
    db::{ListParams, WhereOptions},
    dtos::sales_order_info::{SalesOrderInfoCreate, SalesOrderInfoUpdate},
    entities::sales_order_info::{SalesOrderInfo, SalesOrderInfoBmc},
};

pub struct SalesOrderInfoService;
impl SalesOrderInfoService {
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match SalesOrderInfoBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    pub async fn get_list(req: ListParams) -> AppResult<Vec<SalesOrderInfo>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = SalesOrderInfoBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<SalesOrderInfo> {
        match SalesOrderInfoBmc::get_by_query(params).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("SalesOrderInfo not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn get_by_id(id: String) -> AppResult<SalesOrderInfo> {
        match SalesOrderInfoBmc::get_by_id(id).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("SalesOrderInfo not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn create(req: SalesOrderInfoCreate) -> AppResult<String> {
        SalesOrderInfoBmc::create(req).await?;
        Ok("SalesOrderInfo created".to_string())
    }

    pub async fn update(req: SalesOrderInfoUpdate) -> AppResult<String> {
        SalesOrderInfoBmc::update(req).await?;
        Ok("SalesOrderInfo updated".to_string())
    }

    pub async fn delete(id: String) -> AppResult<String> {
        SalesOrderInfoBmc::delete(id).await?;
        Ok("SalesOrderInfo deleted".to_string())
    }
}
