use crate::{
    app_writer::AppResult,
    db::{ListParams, WhereOptions},
    dtos::cron_job::{CronJobCreate, CronJobUpdate, JobParams},
    entities::{
        cron_job::{CronJob, CronJobBmc},
        cron_job_log::{CronJobLog, CronJobLogBmc},
    },
    utils::order_sdk::{gk_order, vip_order},
};
use anyhow::anyhow;
use tokio_cron_scheduler::{Job, JobScheduler};
use uuid::Uuid; // Added import

use super::order_import::{platform_importers::GkOrderImporter, OrderImportService};

pub struct CronJobService;
impl CronJobService {
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match CronJobBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    pub async fn get_list(req: ListParams) -> AppResult<Vec<CronJob>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = CronJobBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<CronJob> {
        match CronJobBmc::get_by_query(params).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("CronJob not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn get_by_id(id: String) -> AppResult<CronJob> {
        match CronJobBmc::get_by_id(id).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("CronJob not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn create(req: CronJobCreate) -> AppResult<String> {
        CronJobBmc::create(req).await?;
        Ok("CronJob created".to_string())
    }

    pub async fn update(req: CronJobUpdate) -> AppResult<String> {
        CronJobBmc::update(req).await?;
        Ok("CronJob updated".to_string())
    }

    pub async fn delete(id: String) -> AppResult<String> {
        CronJobBmc::delete(id).await?;
        Ok("CronJob deleted".to_string())
    }

    pub async fn start(id: String) -> AppResult<String> {
        let job = CronJobBmc::get_by_id(id).await?;
        if job.is_none() {
            return Err(anyhow!("CronJob not found.").into());
        }
        let job = job.unwrap();
        if job.job_status == "running" {
            return Err(anyhow!("CronJob is running.").into());
        }
        let sched = JobScheduler::new().await?;
        let job_id = job.id.clone().unwrap().to_string();
        let params = serde_json::from_str::<JobParams>(&job.job_params).unwrap();
        let cron_job = Job::new_async(job.cron_expr, move |uuid, _l| {
            let job_id = job_id.clone();
            // SERIAL使用job_id 加上当前时间戳
            let serial = format!("{}:{}", job_id, chrono::Utc::now().timestamp());
            let contract_id = job.contract_id.clone();
            let params = params.clone();
            Box::pin(async move {
                // 创建执行记录
                let mut log = CronJobLog::new(job_id, uuid.to_string());
                match params.platform.as_str() {
                    "GK" => {
                        let list = gk_order::req_import_orders(params.builder_id, contract_id)
                            .await
                            .unwrap();
                        let res = OrderImportService::import(list, GkOrderImporter, Some(serial)).await;
                        match res {
                            Ok(res) => log = log.complete(Some(res.status)),
                            Err(e) => log = log.fail(e.to_string()),
                        }
                    }
                    // "VIP" => vip_order::req_import_orders(params.builder_id).await?,
                    _ => log = log.fail("Platform not supported".to_string()),
                };
                let _ = CronJobLogBmc::create(log).await;
            })
        })?;
        let uuid = sched.add(cron_job.clone()).await?;
        let req: CronJobUpdate = CronJobUpdate {
            id: job.id.clone().unwrap().to_string(),
            job_status: "running".to_string(),
            job_uuid: Some(uuid.to_string()),
            ..Default::default()
        };
        CronJobBmc::update(req).await?;
        Ok("CronJob Started".to_string())
    }

    pub async fn stop(id: String) -> AppResult<String> {
        // 1. Fetch the CronJob entity from the database
        let cron_job_entity_option = CronJobBmc::get_by_id(id.clone()).await?;
        let cron_job_entity = match cron_job_entity_option {
            Some(cj) => cj,
            None => return Err(anyhow!("CronJob with ID '{}' not found.", id).into()),
        };

        // 2. Get the job_uuid (which is the scheduler's job GUID stored as a string)
        let job_uuid_str = match cron_job_entity.job_uuid.as_ref() {
            Some(uuid_str) => uuid_str,
            None => return Err(anyhow!("CronJob with ID '{}' does not have a scheduler job UUID. It might not be running or was not started correctly.", id).into()),
        };

        // 3. Parse the job_uuid string into a Uuid type
        let scheduler_job_uuid = match Uuid::parse_str(job_uuid_str) {
            Ok(uuid) => uuid,
            Err(_) => {
                return Err(anyhow!(
                    "Failed to parse job UUID '{}' for CronJob ID '{}'. Invalid format.",
                    job_uuid_str,
                    id
                )
                .into())
            }
        };

        // 4. Create a JobScheduler instance
        // Note: Creating a new JobScheduler instance here means this approach relies on tokio-cron-scheduler
        // either managing jobs globally by UUID or using a persistent store that this new instance can access.
        // If jobs are purely in-memory to the instance that created them, this stop function might not find the job.
        // A more robust solution would involve a shared/global JobScheduler instance for the application.
        let sched = JobScheduler::new().await?;

        // 5. Remove the job from the scheduler using its UUID
        match sched.remove(&scheduler_job_uuid).await {
            Ok(_) => {
                // 6. Optionally, update the job's status and clear its UUID in the database
                let update_dto = CronJobUpdate {
                    id: id.clone(),                         // The ID of the CronJob to update
                    name: None,                             // No change to name
                    contract_id: None,                      // No change to project_id
                    cron_expr: None,                        // No change to cron_expr
                    job_type: None,                         // No change to job_type
                    job_params: None,                       // No change to job_params
                    description: None,                      // No change to description
                    enabled: None,                          // No change to enabled status
                    job_status: "stop".to_string(),         // Set status to "stopped"
                    job_uuid: None, // Clear the job_uuid by setting it to None (will be NULL in DB)
                    ..Default::default()
                };

                CronJobBmc::update(update_dto).await?;

                Ok(format!("CronJob with ID '{}' stopped successfully.", id))
            }
            Err(e) => {
                // Handle errors from the scheduler, e.g., job not found in this scheduler instance
                Err(anyhow!("Failed to stop job in scheduler for CronJob ID '{}': {}. It might have already been stopped or not found in this scheduler instance.", id, e).into())
            }
        }
    }

    pub async fn check_status(id: String) -> AppResult<String> {
        // 1. Fetch the CronJob entity from the database
        let cron_job_entity_option = CronJobBmc::get_by_id(id.clone()).await?;
        let cron_job_entity = match cron_job_entity_option {
            Some(cj) => cj,
            None => return Err(anyhow!("CronJob with ID '{}' not found.", id).into()),
        };

        // 2. Check if job_uuid exists
        match cron_job_entity.job_uuid.as_ref() {
            Some(_) => return Ok("running".to_string()),
            None => return Ok("stopped".to_string()), // No job_uuid, so it's considered stopped
        };
    }
}
