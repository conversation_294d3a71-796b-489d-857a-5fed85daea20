use anyhow::anyhow;

use crate::{
    app_writer::AppResult,
    db::{ListParams, WhereOptions},
    dtos::warehouse::{WarehouseCreate, WarehouseUpdate},
    entities::warehouse::{Warehouse, WarehouseBmc},
};

pub struct WarehouseService;
impl WarehouseService {
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match WarehouseBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    pub async fn get_list(req: ListParams) -> AppResult<Vec<Warehouse>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = WarehouseBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Warehouse> {
        match WarehouseBmc::get_by_query(params).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("Warehouse not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn get_by_id(id: String) -> AppResult<Warehouse> {
        match WarehouseBmc::get_by_id(id).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("Warehouse not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn create(req: WarehouseCreate) -> AppResult<String> {
        WarehouseBmc::create(req).await?;
        Ok("Warehouse created".to_string())
    }

    pub async fn update(req: WarehouseUpdate) -> AppResult<String> {
        WarehouseBmc::update(req).await?;
        Ok("Warehouse updated".to_string())
    }

    pub async fn delete(id: String) -> AppResult<String> {
        WarehouseBmc::delete(id).await?;
        Ok("Warehouse deleted".to_string())
    }
}
