use anyhow::anyhow;

use crate::{
    app_writer::AppResult,
    db::{ListParams, WhereOptions},
    dtos::warehouse_position::{WarehousePositionCreate, WarehousePositionUpdate},
    entities::warehouse_position::{WarehousePosition, WarehousePositionBmc},
};

pub struct WarehousePositionService;
impl WarehousePositionService {
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match WarehousePositionBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    pub async fn get_list(req: ListParams) -> AppResult<Vec<WarehousePosition>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = WarehousePositionBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<WarehousePosition> {
        match WarehousePositionBmc::get_by_query(params).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("WarehousePosition not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn get_by_id(id: String) -> AppResult<WarehousePosition> {
        match WarehousePositionBmc::get_by_id(id).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("WarehousePosition not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn create(req: WarehousePositionCreate) -> AppResult<String> {
        WarehousePositionBmc::create(req).await?;
        Ok("WarehousePosition created".to_string())
    }

    pub async fn update(req: WarehousePositionUpdate) -> AppResult<String> {
        WarehousePositionBmc::update(req).await?;
        Ok("WarehousePosition updated".to_string())
    }

    pub async fn delete(id: String) -> AppResult<String> {
        WarehousePositionBmc::delete(id).await?;
        Ok("WarehousePosition deleted".to_string())
    }
}
