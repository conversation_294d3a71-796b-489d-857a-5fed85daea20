use salvo::{
    async_trait, hyper::StatusCode, prelude::EndpointOutRegister, writing::<PERSON>son, Depot, Request,
    Response, Writer,
};
use serde::Serialize;

use crate::app_error::AppError;

pub struct AppWriter<T>(pub AppResult<T>);

#[async_trait]
impl<T: Serialize + Default + Send> Writer for AppWriter<T> {
    async fn write(self, req: &mut Request, depot: &mut Depot, res: &mut Response) {
        match self.0 {
            Ok(data) => ResponseBuilder::with_data(data).into_response(res),
            Err(e) => e.write(req, depot, res).await,
        }
    }
}

impl<T: Serialize + Default + Send> EndpointOutRegister for AppWriter<T> {
    fn register(_components: &mut salvo::oapi::Components, operation: &mut salvo::oapi::Operation) {
        operation
            .responses
            .insert("0".to_string(), salvo::oapi::Response::new("success"));
        operation
            .responses
            .insert("500".to_string(), salvo::oapi::Response::new("error"));
    }
}

impl<T> From<AppResult<T>> for AppWriter<T> {
    fn from(result: AppResult<T>) -> Self {
        AppWriter(result)
    }
}

impl<T> From<AppError> for AppWriter<T> {
    fn from(result: AppError) -> Self {
        AppWriter(Err(result))
    }
}

#[derive(Debug, Serialize, Default)]
pub struct ResponseBuilder<T> {
    pub code: i32,
    pub data: T,
    pub msg: String,
}

#[derive(Debug, Serialize)]
pub struct ErrorResponseBuilder {
    pub code: i32,
    pub msg: String,
    #[serde(skip)]
    pub source_error: AppError,
}

impl<T: Serialize + Send + Default> ResponseBuilder<T> {
    pub fn with_data(data: T) -> Self {
        Self {
            code: 200,
            data,
            msg: "success".to_string(),
        }
    }
    #[allow(dead_code)]
    pub fn with_data_msg(data: T, msg: &str) -> Self {
        Self {
            code: 200,
            data,
            msg: msg.to_string(),
        }
    }
}

impl ErrorResponseBuilder {
    pub fn with_err(err: AppError) -> Self {
        let (code, msg) = match &err {
            AppError::AnyHow(e) => (400, e.to_string()),
            AppError::ParseError(e) => (400, e.to_string()),
            AppError::DbError(error) => (400, error.to_string()),
            AppError::XlsxError(e) => (400, e.to_string()), // Changed code from 401 to 400
            AppError::CalamineError(e) => (400, e.to_string()), // Added CalamineError
            AppError::JobSchedulerError(e) => (400, e.to_string()), // Added JobSchedulerError
            AppError::JsonParseError(e) => (400, e.to_string()), // Added JsonParseError
            AppError::RequestError(e) => (400, e.to_string()), // Added RequestError
        };
        Self {
            code,
            msg,
            source_error: err,
        }
    }
}
impl<T: Serialize + Send + Default> ResponseBuilder<T> {
    pub fn into_response(self, res: &mut Response) {
        res.render(Json(self));
    }
}

impl ErrorResponseBuilder {
    pub fn into_response(self, res: &mut Response) {
        let status_code = match self.source_error {
            AppError::AnyHow(_) => StatusCode::BAD_REQUEST,
            AppError::ParseError(_) => StatusCode::BAD_REQUEST,
            AppError::DbError(_) => StatusCode::INTERNAL_SERVER_ERROR,
            AppError::XlsxError(_) => StatusCode::BAD_REQUEST,
            AppError::CalamineError(_) => StatusCode::BAD_REQUEST, // Ensure CalamineError is handled
            AppError::JobSchedulerError(_) => StatusCode::INTERNAL_SERVER_ERROR, // Handle JobSchedulerError
            AppError::JsonParseError(_) => StatusCode::BAD_REQUEST, // Handle JsonParseError
            AppError::RequestError(_) => StatusCode::BAD_REQUEST,   // Handle RequestError
        };
        res.stuff(status_code, Json(self));
    }
}

pub type AppResult<T> = Result<T, AppError>;

#[async_trait]
impl Writer for AppError {
    async fn write(mut self, _req: &mut Request, _depot: &mut Depot, res: &mut Response) {
        ErrorResponseBuilder::with_err(self).into_response(res)
    }
}

impl EndpointOutRegister for AppError {
    fn register(_components: &mut salvo::oapi::Components, operation: &mut salvo::oapi::Operation) {
        operation
            .responses
            .insert("500".to_string(), salvo::oapi::Response::new("error"));
    }
}
