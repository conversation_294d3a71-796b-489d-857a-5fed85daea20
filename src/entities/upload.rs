use crate::db::{Castable, CountRecord, Database, RelateParams, WhereOptions};
use crate::{
    app_writer::AppResult,
    db::ListOptions,
    dtos::upload::{UploadFileCreate, UploadFileUpdate},
};
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UploadFile {
    pub id: RecordId,
    pub t_id: String,
    pub owner: String,
    pub url: String,
    pub name: String,
    pub oss_path: String,
    pub file_type: String,
    pub is_temp: bool,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for UploadFile {}

pub struct UploadFileBmc;

#[allow(dead_code)]
impl UploadFileBmc {
    const ENTITY: &'static str = "upload_file";

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<UploadFile>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: &str) -> AppResult<Option<UploadFile>> {
        Database::exec_get_by_id(Self::ENTITY, id).await
    }

    pub async fn create(upload_file: UploadFileCreate) -> AppResult<String> {
        Database::exec_create(Self::ENTITY, upload_file).await
    }

    pub async fn update(mut upload_file: UploadFileUpdate) -> AppResult<String> {
        let tid = upload_file.id.clone();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        upload_file.id = final_tid.clone();
        Database::exec_update(Self::ENTITY, &final_tid, upload_file).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<UploadFile>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn return_item_related(
        from: String,
        to: String,
        item_table: String,
    ) -> AppResult<Vec<String>> {
        Database::exec_return_relate(&item_table, &from, &to).await
    }

    pub async fn relate_to_item(params: RelateParams, state: &str) -> AppResult<String> {
        let from = vec![params.from];
        Database::exec_relate_batch(state, from, params.to).await
    }

    pub async fn unrelate_to_item(table: &str, from: &str, to: &str) -> AppResult<String> {
        Database::exec_unrelate(table, from, to).await
    }
}
