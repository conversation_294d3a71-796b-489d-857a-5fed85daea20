use crate::db::{Castable, CountRecord, Database, RelateParams, WhereOptions};
use crate::{
    app_writer::AppResult,
    db::ListOptions,
    dtos::quota_contract::{QuotaContractCreate, QuotaContractResponse, QuotaContractUpdate},
};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use std::str::FromStr;
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QuotaContract {
    pub id: RecordId,
    pub serial: String,
    pub project_name: Option<String>,
    pub contract_name: Option<String>,
    pub contract_type: Option<String>,
    pub contract_time: Option<String>,
    pub desc: Option<String>,
    pub company_id: Option<String>,
    pub company_name: Option<String>,
    pub status: String,
    pub request_quota: Decimal,
    pub used_quota: Decimal,
    pub expect_profit: Decimal,
    created_at: i64,
    updated_at: i64,
}

impl Castable for QuotaContract {}

impl QuotaContract {
    pub fn response(self) -> QuotaContractResponse {
        QuotaContractResponse {
            id: self.id.to_string(),
            serial: self.serial,
            project_name: self.project_name,
            contract_name: self.contract_name,
            contract_type: self.contract_type,
            contract_time: self.contract_time,
            desc: self.desc,
            company_id: self.company_id,
            company_name: self.company_name,
            status: self.status,
            request_quota: self.request_quota,
            used_quota: self.used_quota,
            expect_profit: self.expect_profit,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }

    pub fn create(quota_contract: QuotaContractCreate) -> QuotaContract {
        let time_now = chrono::Local::now().timestamp_millis();

        QuotaContract {
            id: RecordId::from_str("quota_contract:new").unwrap(),
            serial: quota_contract.serial,
            project_name: quota_contract.project_name,
            contract_name: quota_contract.contract_name,
            contract_type: quota_contract.contract_type,
            contract_time: quota_contract.contract_time,
            desc: quota_contract.desc,
            company_id: quota_contract.company_id,
            company_name: quota_contract.company_name,
            status: quota_contract.status,
            request_quota: quota_contract.request_quota,
            used_quota: quota_contract.used_quota,
            expect_profit: quota_contract.expect_profit,
            created_at: time_now,
            updated_at: time_now,
        }
    }

    pub fn update(new: QuotaContractUpdate, old: QuotaContract) -> QuotaContract {
        let time_now = chrono::Local::now().timestamp_millis();

        QuotaContract {
            id: old.id,
            serial: new.serial,
            project_name: new.project_name,
            contract_name: new.contract_name,
            contract_type: new.contract_type,
            contract_time: new.contract_time,
            desc: new.desc,
            company_id: new.company_id,
            company_name: new.company_name,
            status: new.status,
            request_quota: new.request_quota,
            used_quota: new.used_quota,
            expect_profit: new.expect_profit,
            created_at: old.created_at,
            updated_at: time_now,
        }
    }
}

pub struct QuotaContractBmc;

impl QuotaContractBmc {
    const ENTITY: &'static str = "quota_contract";

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<QuotaContract>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: &str) -> AppResult<Option<QuotaContract>> {
        Database::exec_get_by_id(Self::ENTITY, id).await
    }

    pub async fn create(quota_contract: QuotaContractCreate) -> AppResult<String> {
        Database::exec_create(Self::ENTITY, quota_contract).await
    }

    pub async fn update(mut quota_contract: QuotaContractUpdate) -> AppResult<String> {
        let tid = quota_contract.id.clone();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        quota_contract.id = final_tid.clone();
        Database::exec_update(Self::ENTITY, &final_tid, quota_contract).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<QuotaContract>> {
        // let params = vec![WhereOptions::new("quota_contractname".to_string(), quota_contractname)];
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn return_item_related(
        from: String,
        to: String,
        item_table: String,
    ) -> AppResult<Vec<String>> {
        Database::exec_return_relate(&item_table, &from, &to).await
    }

    pub async fn relate_to_item(params: RelateParams, state: &str) -> AppResult<String> {
        let from = vec![params.from];
        Database::exec_relate_batch(state, from, params.to).await
    }

    pub async fn get_list_by_quota_contract(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        state: String,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<QuotaContract>> {
        Database::exec_query_relate(Self::ENTITY, page, limit, &state, options, params).await
    }

    pub async fn unrelate_to_item(table: &str, from: &str, to: &str) -> AppResult<String> {
        Database::exec_unrelate(table, from, to).await
    }
}
