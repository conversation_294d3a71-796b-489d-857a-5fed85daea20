use std::str::FromStr;

use crate::{
    app_writer::AppResult,
    db::{CountRecord, Creatable, ListOptions, Patchable},
    dtos::permission::{PermissionCreate, PermissionResponse, PermissionUpdate},
};

use super::role::Role;
use crate::db::{Castable, Database, WhereOptions};
use anyhow::anyhow;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Permission {
    pub id: Option<RecordId>,
    pub name: String,
    pub code: String,
    pub backup: Option<String>,
    pub method: Option<String>,
    pub path: Option<String>,
    pub group: Option<String>,
    pub role: Option<Vec<Role>>,
}

impl Castable for Permission {}
impl Creatable for Permission {}
impl Patchable for Permission {}

impl Permission {
    pub fn response(self) -> PermissionResponse {
        PermissionResponse {
            id: self.id.unwrap().to_string(),
            name: self.name,
            code: self.code,
            backup: self.backup,
            method: self.method,
            path: self.path,
            group: self.group,
        }
    }

    pub async fn create(req: PermissionCreate) -> AppResult<Self> {
        let code = format!(
            "{}:{}:{}",
            req.group.clone().unwrap(),
            req.method.clone().unwrap(),
            req.path.clone().unwrap()
        );

        let existing_permission =
            PermissionBmc::get_by_query(vec![WhereOptions::new("code".to_string(), code.clone())])
                .await?;

        if existing_permission.is_some() {
            return Err(anyhow!("权限信息的路径、方法已存在，请勿重复创建").into());
        }

        let obj = Self {
            id: None,
            name: req.name,
            code,
            backup: req.backup,
            method: req.method,
            path: req.path,
            group: req.group,
            role: None,
        };
        Ok(obj)
    }

    pub async fn update(req: PermissionUpdate) -> AppResult<Self> {
        let code = format!(
            "{}:{}:{}",
            req.group.clone().unwrap(),
            req.method.clone().unwrap(),
            req.path.clone().unwrap()
        );

        let existing_permission =
            PermissionBmc::get_by_query(vec![WhereOptions::new("code".to_string(), code.clone())])
                .await?;

        if let Some(existing) = existing_permission {
            if existing.id.unwrap().to_string() != req.id {
                return Err(anyhow!("权限信息的路径、方法已存在，请勿重复创建").into());
            }
            if existing.backup == req.backup {
                return Err(anyhow!("权限信息没有任何变更").into());
            }
        }

        let obj = Self {
            id: Some(RecordId::from_str(&req.id).unwrap()),
            name: req.name,
            code,
            backup: req.backup,
            method: req.method,
            path: req.path,
            group: req.group,
            role: None,
        };

        Ok(obj)
    }
}

pub struct PermissionBmc;

impl PermissionBmc {
    const ENTITY: &'static str = "permission";

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<Permission>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: &str) -> AppResult<Option<Permission>> {
        Database::exec_get_by_id(Self::ENTITY, id).await
    }

    pub async fn create(req: PermissionCreate) -> AppResult<String> {
        let obj = Permission::create(req).await?;
        Database::exec_create(Self::ENTITY, obj).await
    }

    pub async fn update(req: PermissionUpdate) -> AppResult<String> {
        let tid = req.id.clone();
        let obj = Permission::update(req).await?;
        Database::exec_update(Self::ENTITY, &tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<Permission>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn get_list_with_state(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        state: String,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<Permission>> {
        Database::exec_query_relate(Self::ENTITY, page, limit, &state, options, params).await
    }
}
