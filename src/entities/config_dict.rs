use crate::{
    app_writer::AppResult,
    db::{Castable, CountRecord, Creatable, Database, ListOptions, WhereOptions},
    dtos::config_dict::{ConfigDictCreate, ConfigDictResponse, ConfigDictUpdate},
};
use chrono::Local;
use salvo::oapi::ToSchema;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ConfigDict {
    pub id: Option<RecordId>,
    pub stable: bool,
    pub status: Option<String>,
    pub creator_id: Option<RecordId>,
    pub creator_name: Option<String>,
    pub updater_id: Option<RecordId>,
    pub updater_name: Option<String>,
    pub memo: Option<String>,
    pub config_type: Option<String>,
    pub config_item: Option<String>,
    pub item_default: Option<String>,
    pub item_custom: Option<String>,
    created_at: i64,
    updated_at: i64,
}

impl Castable for ConfigDict {}
impl Creatable for ConfigDict {}

impl ConfigDict {
    pub fn response(self) -> ConfigDictResponse {
        ConfigDictResponse {
            id: self.id.unwrap().to_string(),
            stable: self.stable,
            status: self.status,
            creator_id: self.creator_id.map(|id| id.to_string()),
            creator_name: self.creator_name,
            updater_id: self.updater_id.map(|id| id.to_string()),
            updater_name: self.updater_name,
            memo: self.memo,
            config_type: self.config_type,
            config_item: self.config_item,
            item_default: self.item_default,
            item_custom: self.item_custom,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }

    pub fn create(req: ConfigDictCreate) -> ConfigDict {
        let res = ConfigDict {
            id: None,
            stable: false,
            status: None,
            creator_id: None,
            creator_name: None,
            updater_id: None,
            updater_name: None,
            memo: None,
            config_type: req.config_type.clone(),
            config_item: req.config_item.clone(),
            item_default: req.item_default.clone(),
            item_custom: req.item_custom.clone(),
            created_at: req.created_at,
            updated_at: req.updated_at,
        };
        res
    }

    pub fn update(new: ConfigDictUpdate, old: ConfigDict) -> ConfigDict {
        let res = ConfigDict {
            id: old.id,
            stable: new.stable,
            status: new.status,
            creator_id: old.creator_id,
            creator_name: old.creator_name,
            updater_id: old.updater_id,
            updater_name: old.updater_name,
            memo: new.memo,
            config_type: new.config_type,
            config_item: new.config_item,
            item_default: new.item_default,
            item_custom: new.item_custom,
            created_at: old.created_at,
            updated_at: Local::now().timestamp(),
        };
        res
    }
}

pub struct ConfigDictBmc;

impl ConfigDictBmc {
    const ENTITY: &'static str = "config_dict";

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<ConfigDict>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<ConfigDict>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: &str) -> AppResult<Option<ConfigDict>> {
        Database::exec_get_by_id(Self::ENTITY, id).await
    }

    pub async fn create(req: ConfigDictCreate) -> AppResult<String> {
        let obj = ConfigDict::create(req);
        match Database::exec_create(Self::ENTITY, obj).await {
            Ok(_) => Ok("信息创建成功".to_string()),
            Err(e) => Err(e),
        }
    }

    pub async fn update(req: ConfigDictUpdate) -> AppResult<String> {
        let tid = req.id.clone();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        Database::exec_update(Self::ENTITY, &final_tid, req).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }
}
