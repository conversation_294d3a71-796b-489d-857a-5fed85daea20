use crate::{
    app_writer::AppResult,
    db::{CountRecord, Creatable, ListOptions, Patchable},
    dtos::menu::{MenuCreate, MenuResponse, MenuUpdate},
};

use crate::db::{Castable, Database, WhereOptions};
use anyhow::anyhow;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Menu {
    pub id: Option<RecordId>,
    pub name: String,
    pub order: i32,
    pub path: Option<String>,
    pub parent: Option<String>,
    pub component: Option<String>,
    pub redirect: Option<String>,
    pub active: Option<String>,
    pub title: Option<String>,
    pub icon: Option<String>,
    pub keep_alive: Option<String>,
    pub hidden: Option<String>,
    pub is_link: Option<String>,
    pub remark: Option<String>,
    pub children: Option<Vec<Menu>>,
}

impl Castable for Menu {}
impl Creatable for Menu {}
impl Patchable for Menu {}

impl Menu {
    pub fn response(self) -> MenuResponse {
        let children = self
            .children
            .map(|children| children.into_iter().map(|child| child.response()).collect());

        MenuResponse {
            id: self.id.unwrap().to_string(),
            name: self.name,
            order: self.order,
            path: self.path,
            component: self.component,
            redirect: self.redirect,
            active: self.active,
            title: self.title,
            icon: self.icon,
            keep_alive: self.keep_alive,
            hidden: self.hidden,
            is_link: self.is_link,
            parent: self.parent,
            remark: self.remark,
            children,
        }
    }

    pub fn create(menu: MenuCreate) -> Menu {
        Menu {
            id: None,
            name: menu.name,
            order: menu.order,
            path: menu.path,
            parent: menu.parent,
            component: menu.component,
            redirect: menu.redirect,
            active: menu.active,
            title: menu.title,
            icon: menu.icon,
            keep_alive: menu.keep_alive,
            hidden: menu.hidden,
            is_link: menu.is_link,
            remark: menu.remark,
            children: None,
        }
    }

    pub fn update(new: MenuUpdate, old: Menu) -> Menu {
        let path = if new.path.is_some() {
            new.path
        } else {
            old.path
        };
        let parent = if new.parent.is_some() {
            new.parent
        } else {
            old.parent
        };
        let component = if new.component.is_some() {
            new.component
        } else {
            old.component
        };
        let redirect = if new.redirect.is_some() {
            new.redirect
        } else {
            old.redirect
        };
        let active = if new.active.is_some() {
            new.active
        } else {
            old.active
        };
        let title = if new.title.is_some() {
            new.title
        } else {
            old.title
        };
        let icon = if new.icon.is_some() {
            new.icon
        } else {
            old.icon
        };
        let keep_alive = if new.keep_alive.is_some() {
            new.keep_alive
        } else {
            old.keep_alive
        };
        let hidden = if new.hidden.is_some() {
            new.hidden
        } else {
            old.hidden
        };
        let is_link = if new.is_link.is_some() {
            new.is_link
        } else {
            old.is_link
        };
        let remark = if new.remark.is_some() {
            new.remark
        } else {
            old.remark
        };
        Menu {
            id: None,
            name: new.name,
            order: new.order,
            path,
            parent,
            component,
            redirect,
            active,
            title,
            icon,
            keep_alive,
            hidden,
            is_link,
            remark,
            children: None,
        }
    }
}

pub struct MenuBmc;

#[allow(dead_code)]
impl MenuBmc {
    const ENTITY: &'static str = "menu";

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<Menu>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_by_id(id: &str) -> AppResult<Option<Menu>> {
        Database::exec_get_by_id(Self::ENTITY, id).await
    }

    pub async fn create(menu: MenuCreate) -> AppResult<String> {
        let obj = Menu::create(menu);
        Database::exec_create(Self::ENTITY, obj).await
    }

    pub async fn update(menu: MenuUpdate) -> AppResult<String> {
        let check: Option<Menu> = Database::exec_get_by_id(Self::ENTITY, &menu.id.clone()).await?;
        if check.is_none() {
            return Err(anyhow!("Menu not found.").into());
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        let obj = Menu::update(menu, old);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<Menu>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }
}
