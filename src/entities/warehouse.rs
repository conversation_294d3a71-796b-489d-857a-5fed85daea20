use crate::{
    app_error::AppError,
    app_writer::AppResult,
    db::{Castable, CountRecord, Creatable, Database, ListOptions, Patchable, WhereOptions},
    dtos::warehouse::{WarehouseCreate, WarehouseResponse, WarehouseUpdate},
};
use anyhow::anyhow;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize)]
pub struct Warehouse {
    pub id: Option<RecordId>,
    pub name: String,
    pub serial: String,
    pub company: Option<String>,
    pub contacts_person: Option<String>,
    pub contacts_phone: Option<String>,
    pub contact_address: Option<String>,
    pub location: Option<String>,
    pub remark: Option<String>,
    created_at: i64,
    updated_at: i64,
}

impl Warehouse {
    pub fn response(self) -> WarehouseResponse {
        WarehouseResponse {
            id: self.id.unwrap().to_string(),
            name: self.name,
            serial: self.serial,
            company: self.company,
            contacts_person: self.contacts_person,
            contacts_phone: self.contacts_phone,
            contact_address: self.contact_address,
            location: self.location,
            remark: self.remark,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }

    pub fn create(warehouse: WarehouseCreate) -> Warehouse {
        Warehouse {
            id: None,
            name: warehouse.name,
            serial: warehouse.serial,
            company: warehouse.company,
            contacts_person: warehouse.contacts_person,
            contacts_phone: warehouse.contacts_phone,
            contact_address: warehouse.contact_address,
            location: warehouse.location,
            remark: warehouse.remark,
            created_at: warehouse.created_at,
            updated_at: warehouse.updated_at,
        }
    }

    pub fn update(new: WarehouseUpdate, old: Warehouse) -> Warehouse {
        Warehouse {
            id: old.id,
            name: new.name,
            serial: new.serial,
            company: new.company,
            contacts_person: new.contacts_person,
            contacts_phone: new.contacts_phone,
            contact_address: new.contact_address,
            location: new.location,
            remark: new.remark,
            created_at: old.created_at,
            updated_at: new.updated_at,
        }
    }
}

impl Creatable for Warehouse {}
impl Patchable for Warehouse {}
impl Castable for Warehouse {}

pub struct WarehouseBmc;

impl WarehouseBmc {
    const ENTITY: &'static str = "warehouse";

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<Warehouse>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: String) -> AppResult<Option<Warehouse>> {
        Database::exec_get_by_id(Self::ENTITY, &id).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<Warehouse>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn create(warehouse: WarehouseCreate) -> AppResult<String> {
        Database::exec_create(Self::ENTITY, warehouse).await
    }

    pub async fn update(warehouse: WarehouseUpdate) -> AppResult<String> {
        let check: Option<Warehouse> =
            Database::exec_get_by_id(Self::ENTITY, &warehouse.id.clone()).await?;
        if check.is_none() {
            return Err(AppError::AnyHow(anyhow!("Warehouse not found.")));
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        let obj = Warehouse::update(warehouse, old);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }
}
