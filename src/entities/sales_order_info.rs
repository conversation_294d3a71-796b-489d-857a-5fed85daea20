use std::str::FromStr;

use crate::app_error::AppError;
use crate::app_writer::AppResult;
use crate::db::{Castable, CountRecord, Creatable, Database, ListOptions, Patchable, UpdateOptions, WhereOptions};
use crate::dtos::sales_order_info::{
    SalesOrderInfoCreate, SalesOrderInfoResponse, SalesOrderInfoUpdate,
};
use anyhow::anyhow;
use chrono::Local;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize)]
pub struct SalesOrderInfo {
    pub id: Option<RecordId>,
    pub order_serial: String,
    pub product_serial: String,
    pub product_name: Option<String>,
    pub product_model: Option<String>,
    pub product_type: Option<String>,
    // 销售单价
    pub sales_price: Decimal,
    // 成本单价
    pub cost_price: Decimal,
    pub platform_fee: Decimal,
    pub discount: Decimal,
    pub quantity: Decimal,
    // 销售总额
    pub total_sales_price: Decimal,
    // 成本总额
    pub total_cost_price: Decimal,
    pub express_company: Option<String>,
    pub express_order: Option<String>,
    pub system_remark: Option<String>,
    created_at: i64,
    updated_at: i64,
}

impl Creatable for SalesOrderInfo {}
impl Patchable for SalesOrderInfo {}
impl Castable for SalesOrderInfo {}

impl SalesOrderInfo {
    pub async fn response(self) -> SalesOrderInfoResponse {
        SalesOrderInfoResponse {
            id: self.id.unwrap().to_string(),
            order_serial: self.order_serial,
            product_serial: self.product_serial,
            product_name: self.product_name,
            product_model: self.product_model,
            product_type: self.product_type,
            sales_price: self.sales_price,
            cost_price: self.cost_price,
            platform_fee: self.platform_fee,
            discount: self.discount,
            quantity: self.quantity,
            total_sales_price: self.total_sales_price,
            total_cost_price: self.total_cost_price,
            express_company: self.express_company,
            express_order: self.express_order,
            system_remark: self.system_remark,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }
    pub fn create(sales_order: SalesOrderInfoCreate) -> SalesOrderInfo {
        let time_now = Local::now().timestamp_millis();

        SalesOrderInfo {
            id: None,
            order_serial: sales_order.order_serial,
            product_serial: sales_order.product_serial,
            product_name: sales_order.product_name,
            product_model: sales_order.product_model,
            product_type: sales_order.product_type,
            sales_price: sales_order.sales_price,
            cost_price: sales_order.cost_price,
            platform_fee: sales_order.platform_fee,
            discount: sales_order.discount,
            quantity: sales_order.quantity,
            total_sales_price: sales_order.total_sales_price,
            total_cost_price: sales_order.total_cost_price,
            express_company: sales_order.express_company,
            express_order: sales_order.express_order,
            system_remark: sales_order.system_remark,
            created_at: time_now,
            updated_at: time_now,
        }
    }

    pub fn update(new: SalesOrderInfoUpdate, old: SalesOrderInfo) -> SalesOrderInfo {
        let time_now = Local::now().timestamp_millis();

        SalesOrderInfo {
            id: old.id.clone(),
            order_serial: new.order_serial,
            product_serial: new.product_serial,
            product_name: new.product_name,
            product_model: new.product_model,
            product_type: new.product_type,
            sales_price: new.sales_price,
            cost_price: new.cost_price,
            platform_fee: new.platform_fee,
            discount: new.discount,
            quantity: new.quantity,
            total_sales_price: new.total_sales_price,
            total_cost_price: new.total_cost_price,
            express_company: new.express_company,
            express_order: new.express_order,
            system_remark: new.system_remark,
            created_at: old.created_at,
            updated_at: time_now,
        }
    }

    /// 将 SalesOrderInfoUpdate 转换为 UpdateOptions 列表
    ///
    /// # 参数
    /// * `update_data` - 要更新的销售订单信息数据
    ///
    /// # 返回值
    /// * `Vec<UpdateOptions>` - 数据库更新字段列表
    pub fn to_update_options(update_data: SalesOrderInfoUpdate) -> Vec<UpdateOptions> {
        let mut update_fields = Vec::new();
        let time_now = Local::now().timestamp_millis();

        // 必需字段
        update_fields.push(UpdateOptions::new("order_serial".to_string(), update_data.order_serial));
        update_fields.push(UpdateOptions::new("product_serial".to_string(), update_data.product_serial));

        // 可选字段
        if let Some(product_name) = update_data.product_name {
            update_fields.push(UpdateOptions::new("product_name".to_string(), product_name));
        }
        if let Some(product_model) = update_data.product_model {
            update_fields.push(UpdateOptions::new("product_model".to_string(), product_model));
        }
        if let Some(product_type) = update_data.product_type {
            update_fields.push(UpdateOptions::new("product_type".to_string(), product_type));
        }
        if let Some(express_company) = update_data.express_company {
            update_fields.push(UpdateOptions::new("express_company".to_string(), express_company));
        }
        if let Some(express_order) = update_data.express_order {
            update_fields.push(UpdateOptions::new("express_order".to_string(), express_order));
        }
        if let Some(system_remark) = update_data.system_remark {
            update_fields.push(UpdateOptions::new("system_remark".to_string(), system_remark));
        }

        // 数值字段
        update_fields.push(UpdateOptions::new("sales_price".to_string(), update_data.sales_price.to_string()));
        update_fields.push(UpdateOptions::new("cost_price".to_string(), update_data.cost_price.to_string()));
        update_fields.push(UpdateOptions::new("platform_fee".to_string(), update_data.platform_fee.to_string()));
        update_fields.push(UpdateOptions::new("discount".to_string(), update_data.discount.to_string()));
        update_fields.push(UpdateOptions::new("quantity".to_string(), update_data.quantity.to_string()));
        update_fields.push(UpdateOptions::new("total_sales_price".to_string(), update_data.total_sales_price.to_string()));
        update_fields.push(UpdateOptions::new("total_cost_price".to_string(), update_data.total_cost_price.to_string()));

        // 自动更新时间戳
        update_fields.push(UpdateOptions::new("updated_at".to_string(), time_now.to_string()));

        update_fields
    }
}

pub struct SalesOrderInfoBmc;

impl SalesOrderInfoBmc {
    const ENTITY: &'static str = "sales_order_info";

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<SalesOrderInfo>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<SalesOrderInfo>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: String) -> AppResult<Option<SalesOrderInfo>> {
        Database::exec_get_by_id(Self::ENTITY, &id).await
    }

    pub async fn create(sales_order: SalesOrderInfoCreate) -> AppResult<String> {
        let obj = SalesOrderInfo::create(sales_order);
        Database::exec_create(Self::ENTITY, obj).await
    }

    pub async fn update(sales_order: SalesOrderInfoUpdate) -> AppResult<String> {
        let check: Option<SalesOrderInfo> =
            Database::exec_get_by_id(Self::ENTITY, &sales_order.id.clone()).await?;
        if check.is_none() {
            return Err(AppError::AnyHow(anyhow!("SalesOrderInfo not found.")));
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        let obj = SalesOrderInfo::update(sales_order, old);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }
}
