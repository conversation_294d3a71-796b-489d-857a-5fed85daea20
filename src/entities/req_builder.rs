use crate::app_error::AppError;
use crate::app_writer::AppResult;
use crate::db::{Castable, CountRecord, Creatable, Database, Patchable};
use crate::db::{ListOptions, WhereOptions};
use crate::dtos::req_builder::{ReqBuilderCreate, ReqBuilderResponse, ReqBuilderUpdate};
use anyhow::anyhow;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize)]
pub struct ReqBuilder {
    pub id: Option<RecordId>,
    pub url: String,
    pub method: String,
    pub headers: Option<String>,
    pub query_params: Option<String>,
    pub body: Option<String>,
    pub timeout_secs: Option<u64>,
    pub basic_auth: Option<String>,
    pub bearer_token: Option<String>,
    pub token_expired: Option<bool>,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Creatable for ReqBuilder {}
impl Patchable for ReqBuilder {}
impl Castable for ReqBuilder {}

impl ReqBuilder {
    pub fn response(self) -> ReqBuilderResponse {
        let headers = self
            .headers
            .as_ref()
            .map(|h| serde_json::from_str(h).unwrap_or_default());
        let query_params = self
            .query_params
            .as_ref()
            .map(|q| serde_json::from_str(q).unwrap_or_default());
        let basic_auth = self
            .basic_auth
            .as_ref()
            .map(|auth| serde_json::from_str(auth).unwrap_or_default());

        ReqBuilderResponse {
            id: self.id.unwrap().to_string(),
            url: self.url,
            method: self.method,
            headers,
            query_params,
            body: self.body,
            timeout_secs: self.timeout_secs,
            basic_auth: basic_auth,
            bearer_token: self.bearer_token,
            token_expired: self.token_expired,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }

    pub fn create(req_builder: ReqBuilderCreate) -> ReqBuilder {
        let headers = serde_json::to_string(&req_builder.headers).unwrap();
        let query_params = serde_json::to_string(&req_builder.query_params).unwrap();
        let basic_auth = serde_json::to_string(&req_builder.basic_auth).unwrap();

        ReqBuilder {
            id: None,
            url: req_builder.url,
            method: req_builder.method,
            headers: Some(headers),
            query_params: Some(query_params),
            body: req_builder.body,
            timeout_secs: req_builder.timeout_secs,
            basic_auth: Some(basic_auth),
            bearer_token: req_builder.bearer_token,
            token_expired: None,
            created_at: req_builder.created_at,
            updated_at: req_builder.updated_at,
        }
    }
    pub fn update(req_builder: ReqBuilderUpdate, old: ReqBuilder) -> ReqBuilder {
        let headers = if let Some(headers) = req_builder.headers {
            Some(serde_json::to_string(&headers).unwrap())
        } else {
            old.headers
        };
        let query_params = if let Some(query_params) = req_builder.query_params {
            Some(serde_json::to_string(&query_params).unwrap())
        } else {
            old.query_params
        };
        let basic_auth = if let Some(basic_auth) = req_builder.basic_auth {
            Some(serde_json::to_string(&basic_auth).unwrap())
        } else {
            old.basic_auth
        };

        ReqBuilder {
            id: old.id,
            url: req_builder.url.unwrap_or(old.url),
            method: req_builder.method.unwrap_or(old.method),
            headers,
            query_params,
            body: req_builder.body.or(old.body),
            timeout_secs: req_builder.timeout_secs.or(old.timeout_secs),
            basic_auth,
            bearer_token: req_builder.bearer_token.or(old.bearer_token),
            token_expired: req_builder.token_expired.or(old.token_expired),
            created_at: old.created_at,
            updated_at: req_builder.updated_at,
        }
    }
}

pub struct ReqBuilderBmc;

impl ReqBuilderBmc {
    const ENTITY: &'static str = "req_builder";

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<ReqBuilder>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<ReqBuilder>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: String) -> AppResult<Option<ReqBuilder>> {
        Database::exec_get_by_id(Self::ENTITY, &id).await
    }

    pub async fn create(req: ReqBuilderCreate) -> AppResult<String> {
        let obj = ReqBuilder::create(req);
        Database::exec_create(Self::ENTITY, obj).await
    }

    pub async fn update(req: ReqBuilderUpdate) -> AppResult<String> {
        let check: Option<ReqBuilder> =
            Database::exec_get_by_id(Self::ENTITY, &req.id.clone()).await?;
        if check.is_none() {
            return Err(AppError::AnyHow(anyhow!("ReqBuilder not found.")));
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        let obj = ReqBuilder::update(req, old);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }
}
