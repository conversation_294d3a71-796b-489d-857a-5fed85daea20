use crate::{
    app_writer::AppResult,
    db::{Castable, CountRecord, Creatable, Database, ListOptions, Patchable, WhereOptions},
    dtos::cron_job::{CronJobCreate, CronJobResponse, CronJobUpdate},
};
use anyhow::anyhow;
use chrono::Local;
use salvo::oapi::ToSchema;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CronJob {
    pub id: Option<RecordId>,
    // 归属项目的任务
    pub contract_id: String,
    /// 任务名称
    pub name: String,
    /// Cron表达式
    pub cron_expr: String,
    /// 任务类型：1-HTTP请求, 2-脚本执行
    pub job_type: i32,
    /// 任务参数（JSON格式）
    pub job_params: String,
    /// 任务描述
    pub description: Option<String>,
    /// 是否启用：0-禁用，1-启用s
    pub enabled: i32,
    pub job_status: String,
    pub job_uuid: Option<String>,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for CronJob {}
impl Creatable for CronJob {}
impl Patchable for CronJob {}

impl CronJob {
    pub fn response(self) -> CronJobResponse {
        CronJobResponse {
            id: self.id.unwrap().to_string(),
            contract_id: self.contract_id,
            name: Some(self.name),
            cron_expr: Some(self.cron_expr),
            job_type: Some(self.job_type),
            job_params: Some(self.job_params),
            description: self.description,
            enabled: Some(self.enabled),
            job_status: Some(self.job_status),
            job_uuid: self.job_uuid,
            created_at: self.created_at.to_string(),
            updated_at: self.updated_at.to_string(),
        }
    }

    pub fn create(req: CronJobCreate) -> CronJob {
        let res = CronJob {
            id: None,
            contract_id: req.contract_id,
            name: req.name,
            cron_expr: req.cron_expr,
            job_type: req.job_type,
            job_params: req.job_params,
            description: req.description,
            enabled: req.enabled,
            job_status: req.job_status,
            job_uuid: req.job_uuid,
            created_at: req.created_at,
            updated_at: req.updated_at,
        };
        res
    }

    pub fn update(new: CronJobUpdate, old: CronJob) -> CronJob {
        let res = CronJob {
            id: old.id,
            contract_id: old.contract_id,
            name: new.name.unwrap_or(old.name),
            cron_expr: new.cron_expr.unwrap_or(old.cron_expr),
            job_type: new.job_type.unwrap_or(old.job_type),
            job_params: new.job_params.unwrap_or(old.job_params),
            description: new.description.or(old.description),
            enabled: new.enabled.unwrap_or(old.enabled),
            job_status: new.job_status,
            job_uuid: Some(new.job_uuid.unwrap_or(old.job_uuid.unwrap())),
            created_at: old.created_at,
            updated_at: Local::now().timestamp(),
        };
        res
    }
}

pub struct CronJobBmc;

impl CronJobBmc {
    const ENTITY: &'static str = "cron_job";

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<CronJob>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<CronJob>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: String) -> AppResult<Option<CronJob>> {
        Database::exec_get_by_id(Self::ENTITY, &id).await
    }

    pub async fn create(req: CronJobCreate) -> AppResult<String> {
        let obj = CronJob::create(req);
        match Database::exec_create(Self::ENTITY, obj).await {
            Ok(_) => Ok("信息创建成功".to_string()),
            Err(e) => Err(e),
        }
    }

    pub async fn update(req: CronJobUpdate) -> AppResult<String> {
        let check: Option<CronJob> =
            Database::exec_get_by_id(Self::ENTITY, &req.id.clone()).await?;
        if check.is_none() {
            return Err(anyhow!("CronJob not found.").into());
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        let obj = CronJob::update(req, old);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }
}
