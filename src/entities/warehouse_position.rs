use crate::{
    app_error::AppError,
    app_writer::AppResult,
    db::{Castable, CountRecord, Creatable, Database, ListOptions, Patchable, WhereOptions},
    dtos::warehouse_position::{
        WarehousePositionCreate, WarehousePositionResponse, WarehousePositionUpdate,
    },
};
use anyhow::anyhow;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize)]
pub struct WarehousePosition {
    pub id: Option<RecordId>,
    pub serial: String,
    pub warehouse: String,
    pub area: Option<String>,
    pub name: Option<String>,
    pub location_x: Option<String>,
    pub location_y: Option<String>,
    pub location_z: Option<String>,
    pub max_space: Decimal,
    pub position_type: String,
    pub acreage: Decimal,
    pub height: Decimal,
    pub remark: Option<String>,
    created_at: i64,
    updated_at: i64,
}

impl WarehousePosition {
    pub fn response(self) -> WarehousePositionResponse {
        WarehousePositionResponse {
            id: self.id.unwrap().to_string(),
            serial: self.serial,
            warehouse: self.warehouse,
            area: self.area,
            name: self.name,
            location_x: self.location_x,
            location_y: self.location_y,
            location_z: self.location_z,
            max_space: self.max_space,
            position_type: self.position_type,
            acreage: self.acreage,
            height: self.height,
            remark: self.remark,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }

    pub fn create(warehouse_position: WarehousePositionCreate) -> WarehousePosition {
        WarehousePosition {
            id: None,
            serial: warehouse_position.serial,
            warehouse: warehouse_position.warehouse,
            area: warehouse_position.area,
            name: warehouse_position.name,
            location_x: warehouse_position.location_x,
            location_y: warehouse_position.location_y,
            location_z: warehouse_position.location_z,
            max_space: warehouse_position.max_space,
            position_type: warehouse_position.position_type,
            acreage: warehouse_position.acreage,
            height: warehouse_position.height,
            remark: warehouse_position.remark,
            created_at: warehouse_position.created_at,
            updated_at: warehouse_position.updated_at,
        }
    }

    pub fn update(new: WarehousePositionUpdate, old: WarehousePosition) -> WarehousePosition {
        WarehousePosition {
            id: old.id,
            serial: new.serial,
            warehouse: new.warehouse,
            area: new.area,
            name: new.name,
            location_x: new.location_x,
            location_y: new.location_y,
            location_z: new.location_z,
            max_space: new.max_space,
            position_type: new.position_type,
            acreage: new.acreage,
            height: new.height,
            remark: new.remark,
            created_at: old.created_at,
            updated_at: new.updated_at,
        }
    }
}

impl Creatable for WarehousePosition {}
impl Patchable for WarehousePosition {}
impl Castable for WarehousePosition {}

pub struct WarehousePositionBmc;

impl WarehousePositionBmc {
    const ENTITY: &'static str = "warehouse_position";

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<WarehousePosition>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: String) -> AppResult<Option<WarehousePosition>> {
        Database::exec_get_by_id(Self::ENTITY, &id).await
    }
    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<WarehousePosition>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn create(warehouse_position: WarehousePositionCreate) -> AppResult<String> {
        Database::exec_create(Self::ENTITY, warehouse_position).await
    }

    pub async fn update(warehouse_position: WarehousePositionUpdate) -> AppResult<String> {
        let check: Option<WarehousePosition> =
            Database::exec_get_by_id(Self::ENTITY, &warehouse_position.id.clone()).await?;
        if check.is_none() {
            return Err(AppError::AnyHow(anyhow!("WarehousePosition not found.")));
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        let obj = WarehousePosition::update(warehouse_position, old);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }
}
