use std::str::FromStr;

use crate::app_error::AppError;
use crate::app_writer::AppResult;
use crate::db::{Castable, CountRecord, Creatable, Database, ListOptions, Patchable, WhereOptions};
use crate::dtos::product_attribute::{
    ProductAttributeCreate, ProductAttributeResponse, ProductAttributeUpdate,
};
use anyhow::anyhow;
use chrono::Local;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize)]
pub struct ProductAttribute {
    pub id: Option<RecordId>,
    pub product_serial: String,
    pub stable: bool,
    pub status: Option<String>,
    pub name: String,
    pub value: String,
    pub sort: i64,
    created_at: i64,
    updated_at: i64,
}

impl Creatable for ProductAttribute {}
impl Patchable for ProductAttribute {}
impl Castable for ProductAttribute {}

impl ProductAttribute {
    pub async fn response(self) -> ProductAttributeResponse {
        ProductAttributeResponse {
            id: self.id.unwrap().to_string(),
            product_serial: self.product_serial,
            stable: self.stable,
            status: self.status.unwrap(),
            name: self.name,
            value: self.value,
            sort: self.sort,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }
    pub fn create(product_attribute: ProductAttributeCreate) -> ProductAttribute {
        let time_now = Local::now().timestamp_millis();
        ProductAttribute {
            id: None,
            product_serial: product_attribute.product_serial,
            stable: product_attribute.stable,
            status: Some(product_attribute.status),
            name: product_attribute.name,
            value: product_attribute.value,
            sort: product_attribute.sort,
            created_at: time_now,
            updated_at: time_now,
        }
    }

    pub fn update(new: ProductAttributeUpdate, old: ProductAttribute) -> ProductAttribute {
        let time_now = Local::now().timestamp_millis();
        ProductAttribute {
            id: old.id.clone(),                 // Preserve the ID of the existing entity
            product_serial: new.product_serial, // Directly use value from DTO
            stable: new.stable,                 // Directly use value from DTO
            status: Some(new.status),           // Wrap DTO String status in Option for entity
            name: new.name,                     // Directly use value from DTO
            value: new.value,                   // Directly use value from DTO
            sort: new.sort,                     // Directly use value from DTO
            created_at: old.created_at,         // Keep original creation timestamp
            updated_at: time_now,               // Set current timestamp for update
        }
    }
}

pub struct ProductAttributeBmc;

impl ProductAttributeBmc {
    const ENTITY: &'static str = "product_attribute";

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<ProductAttribute>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<ProductAttribute>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: String) -> AppResult<Option<ProductAttribute>> {
        Database::exec_get_by_id(Self::ENTITY, &id).await
    }

    pub async fn create(product_attribute: ProductAttributeCreate) -> AppResult<String> {
        let obj = ProductAttribute::create(product_attribute);
        Database::exec_create(Self::ENTITY, obj).await
    }

    pub async fn update(product_attribute: ProductAttributeUpdate) -> AppResult<String> {
        let check: Option<ProductAttribute> =
            Database::exec_get_by_id(Self::ENTITY, &product_attribute.id.clone()).await?;
        if check.is_none() {
            return Err(AppError::AnyHow(anyhow!("ProductAttribute not found.")));
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        let obj = ProductAttribute::update(product_attribute, old);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }
}
