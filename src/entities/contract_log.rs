use std::str::FromStr;

use crate::{
    app_writer::AppResult,
    db::{
        Castable, CountRecord, Creatable, Database, ListOptions, Patchable, RelateParams,
        WhereOptions,
    },
    dtos::contract_log::{ContractLogCreate, ContractLogResponse, ContractLogUpdate},
};
use anyhow::anyhow;
use chrono::Local;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize)]
pub struct ContractLog {
    pub id: Option<RecordId>,
    pub parent_id: String,            // 合同ID
    pub log_type: String,             // 日志类型，审核、状态变更等
    pub log_value: Option<String>,    // 日志记录值，根据类型来做解析
    pub log_date: Option<String>,     // 额外补充日期
    pub log_status: Option<String>,   // 日志状态，通过、拒绝、完成、确认等
    pub remark: Option<String>,       // 操作备注
    pub creater_id: Option<String>,   // 操作人ID
    pub creater_name: Option<String>, // 操作人姓名
    pub created_at: i64,
    pub updated_at: i64,
}

impl Creatable for ContractLog {}
impl Patchable for ContractLog {}
impl Castable for ContractLog {}

impl ContractLog {
    /// 将 ContractLog 实体转换为响应 DTO
    pub fn response(self) -> ContractLogResponse {
        ContractLogResponse {
            id: self.id.unwrap().to_string(),
            parent_id: self.parent_id,
            log_type: self.log_type,
            log_value: self.log_value,
            log_date: self.log_date,
            log_status: self.log_status,
            remark: self.remark,
            creater_id: self.creater_id,
            creater_name: self.creater_name,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }

    /// 从创建 DTO 创建新的 ContractLog 实体
    pub fn create(obj: ContractLogCreate) -> ContractLog {
        let time_now: i64 = Local::now().timestamp_millis();
        ContractLog {
            id: None,
            parent_id: obj.parent_id,
            log_type: obj.log_type,
            log_value: obj.log_value,
            log_date: obj.log_date,
            log_status: obj.log_status,
            remark: obj.remark,
            creater_id: obj.creater_id,
            creater_name: obj.creater_name,
            created_at: time_now,
            updated_at: time_now,
        }
    }

    /// 从更新 DTO 更新 ContractLog 实体
    pub fn update(mut self, obj: ContractLogUpdate) -> ContractLog {
        let time_now: i64 = Local::now().timestamp_millis();

        // 只更新提供的字段
        if let Some(parent_id) = obj.parent_id {
            self.parent_id = parent_id;
        }
        if let Some(log_type) = obj.log_type {
            self.log_type = log_type;
        }
        if let Some(log_value) = obj.log_value {
            self.log_value = Some(log_value);
        }
        if let Some(log_date) = obj.log_date {
            self.log_date = Some(log_date);
        }
        if let Some(log_status) = obj.log_status {
            self.log_status = Some(log_status);
        }
        if let Some(remark) = obj.remark {
            self.remark = Some(remark);
        }
        if let Some(creater_id) = obj.creater_id {
            self.creater_id = Some(creater_id);
        }
        if let Some(creater_name) = obj.creater_name {
            self.creater_name = Some(creater_name);
        }

        // 更新时间戳
        self.updated_at = time_now;

        self
    }
}

pub struct ContractLogBmc;

impl ContractLogBmc {
    const ENTITY: &'static str = "contract_log";

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<ContractLog>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<ContractLog>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: &str) -> AppResult<Option<ContractLog>> {
        Database::exec_get_by_id(Self::ENTITY, id).await
    }

    pub async fn create(contract_log: ContractLogCreate) -> AppResult<String> {
        let obj = ContractLog::create(contract_log);
        Database::exec_create(Self::ENTITY, obj).await
    }

    pub async fn update(contract_log: ContractLogUpdate) -> AppResult<String> {
        let check: Option<ContractLog> =
            Database::exec_get_by_id(Self::ENTITY, &contract_log.id.clone()).await?;
        if check.is_none() {
            return Err(anyhow!("ContractLog not found.").into());
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid.clone()
        };
        let obj = ContractLog::update(old, contract_log);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }

    pub async fn relate_to_item(params: RelateParams, state: &str) -> AppResult<String> {
        let from = vec![params.from];
        Database::exec_relate_batch(state, from, params.to).await
    }
}
