use crate::config::CFG;
use captcha::{filters::Noise, Captcha};

// 从Config文件读取Captcha的规格
#[allow(dead_code)]
pub fn create_captcha() -> (String, String) {
    let captcha_width = CFG.captcha.text_length.clone();
    let image_width = CFG.captcha.image_width.clone();
    let image_height = CFG.captcha.image_height.clone();
    let mut captcha_obj = Captcha::new();
    captcha_obj
        .add_chars(captcha_width)
        .apply_filter(Noise::new(0.2))
        .view(image_width, image_height);
    // .save(Path::new("static/captcha.png"))
    // .expect("save failed");
    let img_obj = format!("data:image/png;base64,{}", captcha_obj.as_base64().unwrap());
    (captcha_obj.chars_as_string(), img_obj)
}
