use calamine::{open_workbook, Reader, Xlsx};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use std::{path::Path, str::FromStr};

use crate::{
    app_error::AppError,
    app_writer::AppResult,
    dtos::{sales_order::SalesOrderCreate, sales_order_info::SalesOrderInfoCreate},
};

#[derive(Debug, Serialize, Deserialize)]
pub struct VipOrder {
    // 订单基本信息
    #[serde(rename = "合同ID")]
    pub contract_id: Option<String>,
    #[serde(rename = "订单号")]
    pub order_id: String,
    #[serde(rename = "订单状态")]
    pub order_status: String,
    #[serde(rename = "销售渠道")]
    pub sales_channel: String,
    #[serde(rename = "下单时间")]
    pub order_time: Option<String>,
    #[serde(rename = "支付时间")]
    pub payment_time: Option<String>,
    #[serde(rename = "订单变更时间")]
    pub modify_time: Option<String>,

    // 收货信息
    #[serde(rename = "供应商")]
    pub supplier: String,
    #[serde(rename = "收货时间")]
    pub receive_time: Option<String>,
    #[serde(rename = "收货人")]
    pub receiver: String,
    #[serde(rename = "收货地址")]
    pub address: String,
    #[serde(rename = "联系电话")]
    pub phone: String,
    #[serde(rename = "座机号")]
    pub telephone: Option<String>,
    #[serde(rename = "邮编")]
    pub postal_code: String,

    // 导出信息
    #[serde(rename = "是否导出")]
    pub is_exported: String,
    #[serde(rename = "导出时间")]
    pub export_time: Option<String>,

    // 发票信息
    #[serde(rename = "发票信息")]
    pub invoice_info: Option<String>,
    #[serde(rename = "纳税人识别号")]
    pub tax_id: Option<String>,
    #[serde(rename = "发票金额")]
    pub invoice_amount: Option<String>,

    // 金额信息
    #[serde(rename = "商品总金额")]
    pub total_amount: Option<String>,
    #[serde(rename = "优惠金额")]
    pub discount_amount: Option<String>,
    #[serde(rename = "运费")]
    pub shipping_fee: Option<String>,
    #[serde(rename = "客户应付金额")]
    pub payable_amount: Option<String>,
    #[serde(rename = "支付方式")]
    pub payment_method: Option<String>,
    #[serde(rename = "唯品币支付金额")]
    pub vip_coin_amount: Option<String>,
    #[serde(rename = "支付红包金额")]
    pub red_packet_amount: Option<String>,
    #[serde(rename = "商品小计")]
    pub subtotal_amount: Option<String>,

    // 商品信息
    #[serde(rename = "备注")]
    pub remark: Option<String>,
    #[serde(rename = "品牌")]
    pub brand: String,
    #[serde(rename = "商品名称")]
    pub product_name: String,
    #[serde(rename = "数量")]
    pub quantity: String,
    #[serde(rename = "尺寸")]
    pub size: Option<String>,
    #[serde(rename = "SKU编码")]
    pub sku_code: String,
    #[serde(rename = "单价")]
    pub unit_price: Option<String>,
    #[serde(rename = "定制信息")]
    pub custom_info: Option<String>,
    #[serde(rename = "供应商备注")]
    pub supplier_remark: Option<String>,
    #[serde(rename = "客服备注")]
    pub customer_service_remark: Option<String>,
    #[serde(rename = "商品款号")]
    pub product_code: String,
    #[serde(rename = "商家SKU")]
    pub merchant_sku: String,
    #[serde(rename = "商品颜色")]
    pub product_color: Option<String>,

    // 物流信息
    #[serde(rename = "仓库名称")]
    pub warehouse: Option<String>,
    #[serde(rename = "取消申请时间")]
    pub cancel_apply_time: Option<String>,
    #[serde(rename = "取消原因描述")]
    pub cancel_reason: Option<String>,
    #[serde(rename = "取消审核时间")]
    pub cancel_approve_time: Option<String>,
    #[serde(rename = "物流拒收时间")]
    pub reject_time: Option<String>,
    #[serde(rename = "拒收确认时间")]
    pub reject_confirm_time: Option<String>,
    #[serde(rename = "发货时间")]
    pub shipping_time: Option<String>,
    #[serde(rename = "物流签收时间")]
    pub logistics_sign_time: Option<String>,
    #[serde(rename = "订单签收时间")]
    pub order_sign_time: Option<String>,
    #[serde(rename = "订单类型")]
    pub order_type: Option<String>,
    #[serde(rename = "运单号")]
    pub tracking_number: String,
    #[serde(rename = "承运商")]
    pub carrier: String,
    #[serde(rename = "运单发货状态")]
    pub shipping_status: String,

    // 其他信息
    #[serde(rename = "订单标签")]
    pub order_tag: Option<String>,
    #[serde(rename = "服务保障")]
    pub service_guarantee: Option<String>,
    #[serde(rename = "管控解除时间")]
    pub control_release_time: Option<String>,
    #[serde(rename = "合包识别码")]
    pub package_code: Option<String>,
    #[serde(rename = "会员预约发货时间")]
    pub member_schedule_time: Option<String>,
}

impl VipOrder {
    // 转换为SalesOrderCreate
    pub fn to_sales_order_create(&self) -> SalesOrderCreate {
        SalesOrderCreate {
            contract_id: self.contract_id.clone(),
            status: Some(self.order_status.clone()),
            serial: self.order_id.clone(),
            purchase_time: self.order_time.clone(),
            pay_time: self.payment_time.clone(),
            pay_type: self.payment_method.clone(),
            pay_info: self.payment_method.clone(),
            customer: Some(self.receiver.clone()),
            receive_phone: Some(self.phone.clone()),
            customer_phone: Some(self.phone.clone()),
            address: Some(self.address.clone()),
            express_type: None,
            express_company: Some(self.carrier.clone()),
            express_order: Some(self.tracking_number.clone()),
            platform_name: Some("唯品会".to_string()),
            platform_serial: None,
            platform_order_serial: Some(self.order_id.clone()),
            platform_fee_total: Decimal::new(0, 2),
            amount: Decimal::from_str(
                &self
                    .subtotal_amount
                    .clone()
                    .unwrap_or_else(|| "0".to_string()),
            )
            .unwrap_or_else(|_| Decimal::new(0, 2)),
            express_fee: Decimal::from_str(
                &self.shipping_fee.clone().unwrap_or_else(|| "0".to_string()),
            )
            .unwrap_or_else(|_| Decimal::new(0, 2)),
            total_payment: Decimal::from_str(
                &self
                    .payable_amount
                    .clone()
                    .unwrap_or_else(|| "0".to_string()),
            )
            .unwrap_or_else(|_| Decimal::new(0, 2)),
            creator_id: None,
            updater_id: None,
            delivery_time: self.shipping_time.clone(),
            sign_time: self.logistics_sign_time.clone(),
            complete_time: self.order_sign_time.clone(),
            ..Default::default()
        }
    }

    // 转换为SalesOrderInfoCreate
    pub fn to_sales_order_info_create(&self) -> SalesOrderInfoCreate {
        SalesOrderInfoCreate {
            order_serial: self.order_id.clone(),
            product_serial: self.product_code.clone(),
            product_name: Some(self.product_name.clone()),
            product_model: Some(self.merchant_sku.clone()),
            product_type: None,
            sales_price: Decimal::from_str(
                &self.unit_price.clone().unwrap_or_else(|| "0".to_string()),
            )
            .unwrap_or_else(|_| Decimal::new(0, 2)),
            cost_price: Decimal::default(),
            platform_fee: Decimal::default(),
            discount: Decimal::from_str(
                &self
                    .discount_amount
                    .clone()
                    .unwrap_or_else(|| "0".to_string()),
            )
            .unwrap_or_else(|_| Decimal::new(0, 2)),
            quantity: Decimal::from_str(&self.quantity).unwrap_or_else(|_| Decimal::new(0, 2)),
            total_sales_price: Decimal::from_str(
                &self.total_amount.clone().unwrap_or_else(|| "0".to_string()),
            )
            .unwrap_or_else(|_| Decimal::new(0, 2)),
            total_cost_price: Decimal::default(),
            express_company: Some(self.carrier.clone()),
            express_order: Some(self.tracking_number.clone()),
            system_remark: self.remark.clone(),
            ..Default::default()
        }
    }
}

pub async fn import_orders(file_path: &Path, sheet: Option<String>, contract_id: Option<String>) -> AppResult<Vec<VipOrder>> {
    let mut workbook: Xlsx<_> = match open_workbook(file_path) {
        Ok(wb) => wb,
        Err(e) => return Err(AppError::XlsxError(e.to_string())),
    };
    let sheet = match sheet {
        Some(sheet) => sheet,
        None => "sheet1".to_string(),
    };
    let range = match workbook.worksheet_range(&sheet) {
        Ok(range) => range,
        Err(e) => return Err(AppError::XlsxError(e.to_string())),
    };

    let mut orders = Vec::new();

    for row in range.rows().skip(1) {
        // 跳过标题行
        let order = VipOrder {
            contract_id: contract_id.clone(),
            order_id: row[0].to_string(),
            order_status: row[1].to_string(),
            sales_channel: row[2].to_string(),
            order_time: {
                let val = row[3].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            payment_time: {
                let val = row[4].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            modify_time: {
                let val = row[5].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            supplier: row[6].to_string(),
            receive_time: {
                let val = row[7].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            receiver: row[8].to_string(),
            address: row[9].to_string(),
            phone: row[10].to_string(),
            telephone: {
                let val = row[11].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            postal_code: row[12].to_string(),
            is_exported: row[13].to_string(),
            export_time: {
                let val = row[14].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            invoice_info: {
                let val = row[15].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            tax_id: {
                let val = row[16].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            // 发票金额
            invoice_amount: {
                let val = row[17].to_string();
                if val.is_empty() {
                    Some("0".to_string())
                } else {
                    Some(val)
                }
            },
            // 商品总金额
            total_amount: {
                let val = row[18].to_string();
                if val.is_empty() {
                    Some("0".to_string())
                } else {
                    Some(val)
                }
            },
            // 优惠金额
            discount_amount: {
                let val = row[19].to_string();
                if val.is_empty() {
                    Some("0".to_string())
                } else {
                    Some(val)
                }
            },
            // 运费
            shipping_fee: {
                let val = row[20].to_string();
                if val.is_empty() {
                    Some("0".to_string())
                } else {
                    Some(val)
                }
            },
            // 客户应付金额
            payable_amount: {
                let val = row[21].to_string();
                if val.is_empty() {
                    Some("0".to_string())
                } else {
                    Some(val)
                }
            },
            payment_method: {
                let val = row[22].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            // 唯品币支付金额
            vip_coin_amount: {
                let val = row[23].to_string();
                if val.is_empty() {
                    Some("0".to_string())
                } else {
                    Some(val)
                }
            },
            // 支付红包金额
            red_packet_amount: {
                let val = row[24].to_string();
                if val.is_empty() {
                    Some("0".to_string())
                } else {
                    Some(val)
                }
            },
            // 商品小计
            subtotal_amount: {
                let val = row[25].to_string();
                if val.is_empty() {
                    Some("0".to_string())
                } else {
                    Some(val)
                }
            },
            remark: {
                let val = row[26].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            brand: row[27].to_string(),
            product_name: row[28].to_string(),
            quantity: row[29].to_string(),
            size: {
                let val = row[30].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            sku_code: row[31].to_string(),
            unit_price: {
                let val = row[32].to_string();
                if val.is_empty() {
                    Some("0".to_string())
                } else {
                    Some(val)
                }
            },
            custom_info: {
                let val = row[33].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            supplier_remark: {
                let val = row[34].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            customer_service_remark: {
                let val = row[35].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            product_code: row[36].to_string(),
            merchant_sku: row[37].to_string(),
            product_color: {
                let val = row[38].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            warehouse: {
                let val = row[39].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            cancel_apply_time: {
                let val = row[40].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            cancel_reason: {
                let val = row[41].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            cancel_approve_time: {
                let val = row[42].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            reject_time: {
                let val = row[43].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            reject_confirm_time: {
                let val = row[44].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            shipping_time: {
                let val = row[45].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            logistics_sign_time: {
                let val = row[46].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            order_sign_time: {
                let val = row[47].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            order_type: {
                let val = row[48].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            tracking_number: row[49].to_string(),
            carrier: row[50].to_string(),
            shipping_status: row[51].to_string(),
            order_tag: {
                let val = row[52].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            service_guarantee: {
                let val = row[53].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            control_release_time: {
                let val = row[54].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            package_code: {
                let val = row[55].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            member_schedule_time: {
                let val = row[56].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
        };
        orders.push(order);
    }

    Ok(orders)
}
