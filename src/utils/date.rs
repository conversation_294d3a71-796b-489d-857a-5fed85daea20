use chrono::{Datelike, Days, Duration, Local, NaiveDate};

// 通过日期获取该日期所在的年份
pub fn get_year(date_str: &str) -> i32 {
    let date = NaiveDate::parse_from_str(date_str, "%Y-%m-%d").unwrap();
    date.year()
}

// 通过日期获取该日期所在的月份
pub fn get_month(date_str: &str) -> u32 {
    let date = NaiveDate::parse_from_str(date_str, "%Y-%m-%d").unwrap();
    date.month()
}

// 通过日期获取该日期所在的月份的天数
pub fn get_days_in_month(date_str: &str) -> u32 {
    let date = NaiveDate::parse_from_str(date_str, "%Y-%m-%d").unwrap();
    date.day()
}

// 通过日期获取该日期对应年份的起始日期和结束日期
pub fn get_year_range(date_str: &str) -> (String, String) {
    let date = NaiveDate::parse_from_str(date_str, "%Y-%m-%d").unwrap();
    let year = date.year();

    let start_of_year = NaiveDate::from_ymd_opt(year, 1, 1)
        .unwrap()
        .format("%Y-%m-%d");
    let end_of_year = NaiveDate::from_ymd_opt(year, 12, 31)
        .unwrap()
        .format("%Y-%m-%d");

    (start_of_year.to_string(), end_of_year.to_string())
}

// 通过日期获得该日期对应月份的起始日期和结束日期
pub fn get_month_range(date_str: &str) -> (String, String) {
    let date = NaiveDate::parse_from_str(date_str, "%Y-%m-%d").unwrap();
    let year = date.year();
    let month = date.month();

    let start_of_month = NaiveDate::from_ymd_opt(year, month, 1)
        .unwrap()
        .format("%Y-%m-%d");
    let end_of_month = NaiveDate::from_ymd_opt(year, month + 1, 1)
        .unwrap()
        .checked_sub_days(Days::new(1))
        .unwrap()
        .format("%Y-%m-%d");

    (start_of_month.to_string(), end_of_month.to_string())
}

pub fn convert_excel_datetime(source: i64) -> Option<NaiveDate> {
    let start = NaiveDate::from_ymd_opt(1900, 1, 1).expect("DATE");
    let date = start.checked_add_signed(Duration::days(source));
    date
}

pub fn default_now() -> i64 {
    Local::now().timestamp_millis()
}

pub fn default_zero() -> i64 {
    0
}

pub fn default_false() -> bool {
    false
}

pub fn default_true() -> bool {
    true
}
