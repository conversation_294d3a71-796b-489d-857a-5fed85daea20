use rust_decimal::Decimal;
use serde::{Deserialize, Deserializer};
use std::str::FromStr;

pub fn deserialize_decimal_from_float_or_string<'de, D>(
    deserializer: D,
) -> Result<Decimal, D::Error>
where
    D: Deserializer<'de>,
{
    #[derive(Deserialize)]
    #[serde(untagged)]
    enum StringOrFloat {
        String(String),
        Float(f64),
        Int(i64),
    }

    match StringOrFloat::deserialize(deserializer)? {
        StringOrFloat::String(s) => Decimal::from_str(&s).map_err(serde::de::Error::custom),
        StringOrFloat::Float(f) => Decimal::try_from(f).map_err(serde::de::Error::custom),
        StringOrFloat::Int(i) => Ok(Decimal::from(i)),
    }
}

pub fn deserialize_option_decimal_from_float_or_string<'de, D>(
    deserializer: D,
) -> Result<Option<Decimal>, D::Error>
where
    D: Deserializer<'de>,
{
    #[derive(Deserialize)]
    #[serde(untagged)]
    enum StringOrFloatOrNull {
        String(String),
        Float(f64),
        Int(i64),
        Null,
    }

    match StringOrFloatOrNull::deserialize(deserializer)? {
        StringOrFloatOrNull::String(s) => {
            if s.is_empty() {
                Ok(None)
            } else {
                Decimal::from_str(&s)
                    .map(Some)
                    .map_err(serde::de::Error::custom)
            }
        }
        StringOrFloatOrNull::Float(f) => {
            Decimal::try_from(f)
                .map(Some)
                .map_err(serde::de::Error::custom)
        }
        StringOrFloatOrNull::Int(i) => Ok(Some(Decimal::from(i))),
        StringOrFloatOrNull::Null => Ok(None),
    }
}
