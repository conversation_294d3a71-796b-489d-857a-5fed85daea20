// use std::future::Future;

use crate::app_writer::AppResult;
use crate::dtos::upload::{MultiResult, SingleResult};
use crate::CFG;
use chrono::Local;
use regex::{self, Regex};
use salvo::http::form::FilePart;

pub mod aliyun;
pub mod local;
pub mod tencent;

pub trait UploadMethod {
    async fn add_single(&self, req: FilePart, target: &str) -> AppResult<SingleResult>;
    async fn add_multi(&self, req: Vec<FilePart>, target: &str) -> AppResult<MultiResult>;
    async fn delete(&self, target: &str) -> AppResult<String>;
    async fn make_permanent(&self, source: &str) -> AppResult<SingleResult>;
}

pub enum OssProvider {
    Local(local::Local),
    <PERSON>yun(aliyun::<PERSON>yun),
    Tencent(tencent::Tencent),
}

impl UploadMethod for OssProvider {
    async fn add_single(&self, req: FilePart, target: &str) -> AppResult<SingleResult> {
        match self {
            Self::Local(local) => local.add_single(req, target).await,
            Self::Aliyun(aliyun) => aliyun.add_single(req, target).await,
            Self::Tencent(tencent) => tencent.add_single(req, target).await,
        }
    }

    async fn add_multi(&self, req: Vec<FilePart>, target: &str) -> AppResult<MultiResult> {
        match self {
            Self::Local(local) => local.add_multi(req, target).await,
            Self::Aliyun(aliyun) => aliyun.add_multi(req, target).await,
            Self::Tencent(tencent) => tencent.add_multi(req, target).await,
        }
    }

    async fn delete(&self, target: &str) -> AppResult<String> {
        match self {
            Self::Local(local) => local.delete(target).await,
            Self::Aliyun(aliyun) => aliyun.delete(target).await,
            Self::Tencent(tencent) => tencent.delete(target).await,
        }
    }

    async fn make_permanent(&self, source: &str) -> AppResult<SingleResult> {
        match self {
            Self::Local(local) => local.make_permanent(source).await,
            Self::Aliyun(aliyun) => aliyun.make_permanent(source).await,
            Self::Tencent(tencent) => tencent.make_permanent(source).await,
        }
    }
}

pub fn init_oss() -> OssProvider {
    match CFG.oss.oss_type.as_str() {
        "local" => OssProvider::Local(local::Local),
        "aliyun" => OssProvider::Aliyun(aliyun::Aliyun),
        "tencent" => OssProvider::Tencent(tencent::Tencent),
        _ => OssProvider::Local(local::Local),
    }
}

// pub fn init_oss() -> Box<dyn Future<Output = dyn UploadMethod>> {
//     match CFG.oss.oss_type.as_str() {
//         "local" => Box::new(local::Local),
//         "aliyun" => Box::new(aliyun::Aliyun),
//         // "tencent" => Box::new(tencent::Tencent),
//         _ => Box::new(local::Local),
//     }
// }

pub fn generate_oss_path(file_type: &str) -> String {
    let date = Local::now().format("%Y%m%d").to_string();
    format!("upload/{}/{}", file_type, date)
}

/// 替换文本中的指定内容
///
/// # Arguments
/// * `content` - 需要处理的文本内容
/// * `search` - 需要查找的文本
/// * `replace` - 替换后的文本
///
/// # Returns
/// 返回处理后的文本内容
///
/// # Example
/// ```
/// let content = r#"<img src="tmp/upload/image/123.jpg" />"#;
/// let new_content = replace_content(content, "tmp/", "");
/// assert_eq!(new_content, r#"<img src="upload/image/123.jpg" />"#);
/// ```
pub fn replace_content(content: String, search: String, replace: String) -> String {
    let re = Regex::new(&format!(r#"src="([^"]*{}[^"]*)"#, search)).unwrap();
    re.replace_all(&content, |caps: &regex::Captures| {
        let url = &caps[1];
        format!(r#"src="{}""#, url.replace(&search, &replace))
    })
    .to_string()
}
