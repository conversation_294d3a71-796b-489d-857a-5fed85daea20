use crate::app_writer::AppResult;
use crate::config::CFG;
use crate::dtos::upload::{MultiResult, SingleResult};
use crate::utils::os_path::md5_string;
use crate::utils::rand_utils::random_string;
use aliyun_oss_rust_sdk::oss::OSS;
use aliyun_oss_rust_sdk::request::RequestBuilder;
use anyhow::anyhow;
use salvo::http::form::FilePart;
use std::{ffi::OsStr, path::Path};

use super::UploadMethod;

pub struct Aliyun;

impl <PERSON>yun {
    async fn copy_object(
        client: &OSS,
        source: &str,
        target: &str,
        builder: RequestBuilder,
    ) -> Result<(), anyhow::Error> {
        let mut build = builder.clone();
        build.method = aliyun_oss_rust_sdk::request::RequestType::Put;

        build
            .headers
            .insert("x-oss-copy-source".to_string(), source.to_string());

        let (url, _) = client
            .build_request(target, build)
            .map_err(|e| anyhow!("构建请求失败: {}", e))?;

        // 直接使用 reqwest 发送请求
        let response = reqwest::Client::new()
            .put(url)
            .send()
            .await
            .map_err(|e| anyhow!("发送请求失败: {}", e))?;

        if response.status().is_success() {
            Ok(())
        } else {
            let status = response.status();
            let result = response.text().await?;
            Err(anyhow!("复制文件失败: status={}, error={}", status, result))
        }
    }
}

impl UploadMethod for Aliyun {
    async fn add_single(&self, req: FilePart, target: &str) -> AppResult<SingleResult> {
        let client = init_client();
        let file = req;

        let filename = file
            .name()
            .unwrap_or_default()
            .trim_start_matches('/')
            .to_owned();
        let path = Path::new(&filename);
        let stem = path.file_stem().and_then(OsStr::to_str).unwrap_or_default();
        let ext = path
            .extension()
            .and_then(OsStr::to_str)
            .unwrap_or_default()
            .to_lowercase();
        let new_name = format!("{}-{}.{}", stem, random_string(10), ext.clone());
        let dest_path = Path::new(&target).join(&new_name);
        let upload_path = dest_path.clone().to_str().unwrap().to_string();
        let md5digest = md5_string(new_name.clone());

        let builder = RequestBuilder::new();
        let buffer = std::fs::read(file.path()).unwrap();
        let resp = client
            .pub_object_from_buffer(&upload_path, &buffer, builder)
            .await;
        if resp.is_err() {
            return Err(anyhow!("上传失败: {}", resp.err().unwrap()).into());
        }

        let res = SingleResult {
            filename: new_name,
            file_type: ext,
            path: target.to_string(),
            url: oss_url(&dest_path),
            md5: Some(md5digest),
        };
        Ok(res)
    }

    async fn add_multi(&self, req: Vec<FilePart>, target: &str) -> AppResult<MultiResult> {
        let mut res = MultiResult {
            path: target.to_string(),
            files: vec![],
        };
        let client = init_client();
        let builder = RequestBuilder::new();

        for file in req {
            let filename = file
                .name()
                .unwrap_or_default()
                .trim_start_matches('/')
                .to_owned();
            let path = Path::new(&filename);
            let stem = path.file_stem().and_then(OsStr::to_str).unwrap_or_default();
            let ext = path
                .extension()
                .and_then(OsStr::to_str)
                .unwrap_or_default()
                .to_lowercase();
            let new_name = format!("{}-{}.{}", stem, random_string(10), ext.clone());
            let dest_path = Path::new(&target).join(&new_name);
            let upload_path = dest_path.clone().to_str().unwrap().to_string();
            let md5digest = md5_string(new_name.clone());

            let buffer = std::fs::read(file.path()).unwrap();
            let resp = client
                .pub_object_from_buffer(&upload_path, &buffer, builder.clone())
                .await;
            if resp.is_err() {
                log::error!("上传失败: {}", resp.err().unwrap());
                continue;
            }

            res.files.push(SingleResult {
                filename: new_name,
                file_type: ext,
                path: target.to_string(),
                url: oss_url(&dest_path),
                md5: Some(md5digest),
            });
        }
        Ok(res)
    }

    async fn delete(&self, target: &str) -> AppResult<String> {
        let client = init_client();
        let builder = RequestBuilder::new();
        let resp = client.delete_object(target, builder).await;
        if resp.is_err() {
            return Err(anyhow!("删除失败: {}", resp.err().unwrap()).into());
        }
        Ok("删除成功！".to_string())
    }

    async fn make_permanent(&self, source: &str) -> AppResult<SingleResult> {
        let client = init_client();
        let builder = RequestBuilder::new();

        // 检查文件是否存在
        let meta_result = client.get_object_metadata(source, builder.clone()).await;
        if meta_result.is_err() {
            return Err(anyhow!("源文件不存在").into());
        }

        // 检查是否是tmp开头
        if !source.starts_with("tmp/") {
            return Err(anyhow!("源文件路径必须以tmp/开头").into());
        }

        // 构建目标路径
        let target = source.replacen("tmp/", "", 1);

        // 执行移动操作（复制+删除）
        // 1. 复制文件
        let copy_source = format!("/{}/{}", &CFG.oss.bucket_name, source);
        Self::copy_object(&client, &copy_source, &target, builder.clone())
            .await
            .map_err(|e| anyhow!("移动文件失败: {}", e))?;

        // 2. 删除源文件
        let resp = client.delete_object(source, builder).await;
        if resp.is_err() {
            return Err(anyhow!("删除源文件失败: {}", resp.err().unwrap()).into());
        }
        let res = SingleResult {
            filename: "".to_string(),
            file_type: "".to_string(),
            path: target.clone().to_string(),
            url: oss_url(&Path::new(&target)),
            md5: None,
        };
        Ok(res)
    }
}

fn init_client() -> OSS {
    OSS::new(
        &CFG.oss.secret_id,
        &CFG.oss.secret_key,
        &CFG.oss.endpoint,
        &CFG.oss.bucket_name,
    )
}

fn oss_url(dest: &Path) -> String {
    let dest_trans = dest.as_os_str().to_str().unwrap();
    format!(
        "https://{}.{}/{}",
        &CFG.oss.bucket_name, &CFG.oss.endpoint, dest_trans
    )
}
