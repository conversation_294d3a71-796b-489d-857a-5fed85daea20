use calamine::<PERSON><PERSON><PERSON> as <PERSON>amineError;
use salvo::http::ParseError;
use surrealdb::<PERSON><PERSON><PERSON> as SurrealError;
use thiserror::Error;
use tokio_cron_scheduler::JobSchedulerError;

#[derive(Erro<PERSON>, Debug)]
pub enum AppError {
    #[error("error:`{0}`")]
    AnyHow(#[from] anyhow::Error),
    #[error("http::ParseError:`{0}`")]
    ParseError(#[from] ParseError),
    #[error("error:`{0}`")]
    XlsxError(String),
    #[error("Failed to use the database: {0}")]
    DbError(#[from] SurrealError),
    #[error("Excel file error: {0}")]
    CalamineError(#[from] CalamineError),
    #[error("Job scheduler error: {0}")]
    JobSchedulerError(#[from] JobSchedulerError),
    #[error("JSON parse error: {0}")]
    JsonParseError(String),
    #[error("HTTP request error: {0}")]
    RequestError(#[from] reqwest::Error),
}
