use crate::error::{AppError, AppResult};
use crate::service::file_parser::{FileParser, ParseResult, TransactionData};
use sea_orm::{ActiveModelTrait, DatabaseConnection, Set};
use std::path::Path;
use uuid::Uuid;

#[derive(Clone)]
pub struct ImportService {
    db: DatabaseConnection,
}

impl ImportService {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }

    pub async fn import_file(&self, file_path: &Path, file_name: &str) -> AppResult<Uuid> {
        // Parse the file
        let parse_result = FileParser::parse_file(file_path)?;
        
        // Get file metadata
        let file_metadata = std::fs::metadata(file_path)?;
        let file_size = file_metadata.len() as i64;
        let file_type = file_path
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("unknown")
            .to_string();

        // Create import record
        let import_id = Uuid::new_v4();
        
        // Note: The following code will need to be updated after entity generation
        // This is a placeholder showing the intended structure
        
        /*
        let import_record = import_record::ActiveModel {
            id: Set(import_id),
            file_name: Set(file_name.to_string()),
            file_type: Set(file_type),
            file_size: Set(file_size),
            status: Set("processing".to_string()),
            total_rows: Set(Some(parse_result.total_rows as i32)),
            processed_rows: Set(0),
            error_rows: Set(0),
            error_message: Set(None),
            created_at: Set(chrono::Utc::now().into()),
            updated_at: Set(chrono::Utc::now().into()),
        };

        import_record.insert(&self.db).await?;
        */

        // Process and insert data entries
        let mut processed_rows = 0;
        let mut error_rows = 0;
        let mut errors = Vec::new();

        for row in &parse_result.rows {
            match self.process_data_row(&import_id, row, &parse_result).await {
                Ok(_) => processed_rows += 1,
                Err(e) => {
                    error_rows += 1;
                    errors.push(format!("Row {}: {}", row.row_number, e));
                }
            }
        }

        // Update import record with final status
        let status = if error_rows == 0 {
            "completed"
        } else if processed_rows == 0 {
            "failed"
        } else {
            "completed_with_errors"
        };

        /*
        let update_record = import_record::ActiveModel {
            id: Set(import_id),
            status: Set(status.to_string()),
            processed_rows: Set(processed_rows),
            error_rows: Set(error_rows),
            error_message: Set(if errors.is_empty() {
                None
            } else {
                Some(errors.join("\n"))
            }),
            updated_at: Set(chrono::Utc::now().into()),
            ..Default::default()
        };

        update_record.update(&self.db).await?;
        */

        Ok(import_id)
    }

    async fn process_data_row(
        &self,
        import_id: &Uuid,
        row: &crate::service::file_parser::ParsedRow,
        _parse_result: &ParseResult,
    ) -> AppResult<()> {
        // Parse the row data into transaction structure
        let transaction_data = FileParser::parse_transaction_data(&row.data)?;

        // Validate required fields
        if transaction_data.merchant_txn_id.is_none() || transaction_data.trade_order_id.is_none() {
            return Err(AppError::Validation(
                "Missing required fields: Merchant Txn ID or Trade Order ID".to_string()
            ));
        }

        // Note: This will be updated after entity generation
        // This is a placeholder showing the intended structure for transactions table

        /*
        let transaction = transactions::ActiveModel {
            id: Set(Uuid::new_v4()),
            import_record_id: Set(*import_id),
            row_number: Set(row.row_number as i32),
            request_time: Set(transaction_data.request_time.unwrap_or_else(|| chrono::Utc::now())),
            accounting_amount: Set(transaction_data.accounting_amount.unwrap_or_default()),
            balance: Set(transaction_data.balance.unwrap_or_default()),
            accounting_currency: Set(transaction_data.accounting_currency.unwrap_or_default()),
            accounting_type: Set(transaction_data.accounting_type.unwrap_or_default()),
            account_subject: Set(transaction_data.account_subject.unwrap_or_default()),
            txn_create_time: Set(transaction_data.txn_create_time.unwrap_or_else(|| chrono::Utc::now())),
            txn_complete_time: Set(transaction_data.txn_complete_time.unwrap_or_else(|| chrono::Utc::now())),
            merchant_txn_id: Set(transaction_data.merchant_txn_id.unwrap()),
            trade_order_id: Set(transaction_data.trade_order_id.unwrap()),
            country: Set(transaction_data.country.unwrap_or_default()),
            txn_amount: Set(transaction_data.txn_amount.unwrap_or_default()),
            txn_currency: Set(transaction_data.txn_currency.unwrap_or_default()),
            payee_txn_fee: Set(transaction_data.payee_txn_fee.unwrap_or_default()),
            payee_txn_fee_currency: Set(transaction_data.payee_txn_fee_currency.unwrap_or_default()),
            payer_txn_fee: Set(transaction_data.payer_txn_fee.unwrap_or_default()),
            payer_txn_fee_currency: Set(transaction_data.payer_txn_fee_currency.unwrap_or_default()),
            payee_tax: Set(transaction_data.payee_tax.unwrap_or_default()),
            payee_tax_currency: Set(transaction_data.payee_tax_currency.unwrap_or_default()),
            payer_tax: Set(transaction_data.payer_tax.unwrap_or_default()),
            payer_tax_currency: Set(transaction_data.payer_tax_currency.unwrap_or_default()),
            remark: Set(transaction_data.remark),
            created_at: Set(chrono::Utc::now().into()),
        };

        transaction.insert(&self.db).await?;
        */

        // Placeholder - return success for now
        Ok(())
    }

    pub async fn get_import_status(&self, import_id: &Uuid) -> AppResult<ImportStatus> {
        // Note: This will need to be updated after entity generation
        // This is a placeholder
        
        Ok(ImportStatus {
            id: *import_id,
            file_name: "placeholder.csv".to_string(),
            status: "pending".to_string(),
            total_rows: Some(0),
            processed_rows: 0,
            error_rows: 0,
            error_message: None,
        })
    }
}

#[derive(Debug, serde::Serialize)]
pub struct ImportStatus {
    pub id: Uuid,
    pub file_name: String,
    pub status: String,
    pub total_rows: Option<i32>,
    pub processed_rows: i32,
    pub error_rows: i32,
    pub error_message: Option<String>,
}
