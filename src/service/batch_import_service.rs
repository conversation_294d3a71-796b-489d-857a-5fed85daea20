use crate::error::{AppError, AppResult};
use crate::service::file_parser::FileParser;
use chrono::{DateTime, NaiveDateTime, Utc};
use entity::transactions;
use rust_decimal::prelude::FromPrimitive;
use rust_decimal::Decimal;
use sea_orm::{ConnectionTrait, DatabaseConnection, EntityTrait, Set, TransactionTrait};
use serde_json::Value;
use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use std::str::FromStr;
use std::sync::Arc;
use tokio::sync::Semaphore;
use tracing::{error, info, warn};
use uuid::Uuid;

#[derive(Clone)]
pub struct BatchImportService {
    db: DatabaseConnection,
    max_concurrent_files: usize,
    batch_size: usize,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ImportProgress {
    pub total_files: usize,
    pub processed_files: usize,
    pub total_records: usize,
    pub processed_records: usize,
    pub failed_files: Vec<String>,
    pub current_file: Option<String>,
}

#[derive(Debug, Clone)]
pub struct TransactionRecord {
    pub filename: String,
    pub row_number: i32,
    pub request_time: DateTime<Utc>,
    pub accounting_amount: Decimal,
    pub balance: Decimal,
    pub accounting_currency: String,
    pub accounting_type: String,
    pub account_subject: String,
    pub txn_create_time: DateTime<Utc>,
    pub txn_complete_time: Option<DateTime<Utc>>,
    pub merchant_txn_id: Option<String>,
    pub trade_order_id: Option<String>,
    pub country: Option<String>,
    pub txn_amount: Option<Decimal>,
    pub txn_currency: Option<String>,
    pub payee_txn_fee: Option<Decimal>,
    pub payee_txn_fee_currency: Option<String>,
    pub payer_txn_fee: Option<Decimal>,
    pub payer_txn_fee_currency: Option<String>,
    pub payee_tax: Option<Decimal>,
    pub payee_tax_currency: Option<String>,
    pub payer_tax: Option<Decimal>,
    pub payer_tax_currency: Option<String>,
    pub remark: Option<String>,
}

impl BatchImportService {
    pub fn new(db: DatabaseConnection) -> Self {
        Self {
            db,
            max_concurrent_files: 8, // 最大并发文件数
            batch_size: 1000,        // 批量插入大小
        }
    }

    pub fn with_concurrency(mut self, max_concurrent: usize) -> Self {
        self.max_concurrent_files = max_concurrent;
        self
    }

    pub fn with_batch_size(mut self, batch_size: usize) -> Self {
        self.batch_size = batch_size;
        self
    }

    /// 批量导入 data 目录下的所有文件
    pub async fn import_data_directory(&self, data_dir: &Path) -> AppResult<ImportProgress> {
        info!("Starting batch import from directory: {:?}", data_dir);

        // 1. 按顺序读取文件名
        let mut file_paths = self.get_sorted_file_paths(data_dir)?;
        let total_files = file_paths.len();

        info!("Found {} files to process", total_files);

        let mut progress = ImportProgress {
            total_files,
            processed_files: 0,
            total_records: 0,
            processed_records: 0,
            failed_files: Vec::new(),
            current_file: None,
        };

        // 2. 使用信号量控制并发
        let semaphore = Arc::new(Semaphore::new(self.max_concurrent_files));
        let mut handles = Vec::new();

        // 分批处理文件，限制每批最多处理的文件数以控制内存
        let chunk_size = self.max_concurrent_files.min(5); // 限制每批最多5个文件
        let mut batch_count = 0;

        while !file_paths.is_empty() {
            batch_count += 1;
            let batch: Vec<_> = file_paths
                .drain(..std::cmp::min(chunk_size, file_paths.len()))
                .collect();

            info!(
                "Processing batch {}/{} ({} files)",
                batch_count,
                (total_files + chunk_size - 1) / chunk_size,
                batch.len()
            );

            for file_path in batch {
                let permit = semaphore.clone().acquire_owned().await.unwrap();
                let db = self.db.clone();
                let batch_size = self.batch_size;

                let handle = tokio::spawn(async move {
                    let _permit = permit; // 持有许可证直到任务完成
                    Self::process_single_file(db, file_path, batch_size).await
                });

                handles.push(handle);
            }

            // 等待当前批次完成
            for handle in handles.drain(..) {
                match handle.await {
                    Ok(Ok(file_progress)) => {
                        progress.processed_files += 1;
                        progress.processed_records += file_progress.processed_records;
                        info!(
                            "✓ Completed file: {} ({} records) - Progress: {}/{} files, {} total records",
                            file_progress.current_file.as_ref().unwrap(),
                            file_progress.processed_records,
                            progress.processed_files,
                            total_files,
                            progress.processed_records
                        );
                    }
                    Ok(Err(e)) => {
                        error!("File processing failed: {:?}", e);
                        progress.failed_files.push(format!("Unknown file: {}", e));
                    }
                    Err(e) => {
                        error!("Task join failed: {:?}", e);
                        progress.failed_files.push(format!("Task error: {}", e));
                    }
                }
            }

            // 强制垃圾回收和让出控制权，释放内存
            tokio::task::yield_now().await;

            // 每处理完一批后，打印内存使用情况（如果可能）
            info!(
                "Batch {} completed. Processed: {}/{} files, {} records",
                batch_count, progress.processed_files, total_files, progress.processed_records
            );
        }

        info!(
            "Batch import completed. Processed: {}/{} files, {} records",
            progress.processed_files, progress.total_files, progress.processed_records
        );

        Ok(progress)
    }

    /// 导入单个文件（用于上传接口）
    pub async fn import_single_file(&self, file_path: &Path) -> AppResult<ImportProgress> {
        info!("Importing single file: {:?}", file_path);

        let result =
            Self::process_single_file(self.db.clone(), file_path.to_path_buf(), self.batch_size)
                .await?;

        Ok(result)
    }

    /// 获取排序后的文件路径列表
    fn get_sorted_file_paths(&self, data_dir: &Path) -> AppResult<Vec<PathBuf>> {
        let mut file_paths = Vec::new();

        for entry in fs::read_dir(data_dir)? {
            let entry = entry?;
            let path = entry.path();

            if path.is_file() {
                if let Some(extension) = path.extension() {
                    if extension == "xlsx" || extension == "xls" || extension == "csv" {
                        file_paths.push(path);
                    }
                }
            }
        }

        // 按文件名排序
        file_paths.sort_by(|a, b| {
            a.file_name()
                .unwrap_or_default()
                .cmp(b.file_name().unwrap_or_default())
        });

        Ok(file_paths)
    }

    /// 处理单个文件的静态方法
    async fn process_single_file(
        db: DatabaseConnection,
        file_path: PathBuf,
        batch_size: usize,
    ) -> AppResult<ImportProgress> {
        let filename = file_path
            .file_name()
            .and_then(|n| n.to_str())
            .ok_or_else(|| AppError::InvalidFormat("Invalid filename".to_string()))?
            .to_string();

        info!("Processing file: {}", filename);

        // 1. 检查文件是否已经导入
        if Self::is_file_already_imported(&db, &filename).await? {
            info!("File {} already imported, skipping...", filename);
            let existing_count = Self::get_file_record_count(&db, &filename).await?;
            return Ok(ImportProgress {
                total_files: 1,
                processed_files: 1,
                total_records: existing_count,
                processed_records: existing_count,
                failed_files: Vec::new(),
                current_file: Some(filename),
            });
        }

        // 2. 创建分区（如果不存在）
        Self::ensure_partition_exists(&db, &filename).await?;

        // 2. 解析文件数据
        let parse_result = FileParser::parse_file(&file_path)?;
        let total_records = parse_result.rows.len();

        info!("Parsed {} records from {}", total_records, filename);

        // 3. 分批转换和插入，避免内存累积
        let mut processed_records = 0;
        let conversion_batch_size = batch_size * 2; // 转换批次稍大一些

        for (batch_index, rows_chunk) in parse_result.rows.chunks(conversion_batch_size).enumerate()
        {
            let mut transaction_records = Vec::new();

            // 转换当前批次的记录
            for (index, parsed_row) in rows_chunk.iter().enumerate() {
                let global_index = batch_index * conversion_batch_size + index;
                match Self::convert_to_transaction_record(
                    &filename,
                    global_index as i32 + 1,
                    &parsed_row.data,
                ) {
                    Ok(record) => transaction_records.push(record),
                    Err(e) => {
                        warn!(
                            "Failed to convert row {} in {}: {:?}",
                            global_index + 1,
                            filename,
                            e
                        );
                        continue;
                    }
                }
            }

            // 分批插入当前转换的记录
            for insert_chunk in transaction_records.chunks(batch_size) {
                Self::batch_insert_records(&db, insert_chunk).await?;
                processed_records += insert_chunk.len();

                // 每插入一批后打印进度
                if processed_records % (batch_size * 10) == 0 {
                    info!(
                        "Progress: {} - inserted {}/{} records",
                        filename, processed_records, total_records
                    );
                }
            }

            // 释放当前批次的内存
            drop(transaction_records);
        }

        info!("Inserted {} records from {}", processed_records, filename);

        Ok(ImportProgress {
            total_files: 1,
            processed_files: 1,
            total_records,
            processed_records,
            failed_files: Vec::new(),
            current_file: Some(filename),
        })
    }

    /// 确保分区存在
    async fn ensure_partition_exists(db: &DatabaseConnection, filename: &str) -> AppResult<()> {
        // 生成安全的分区名称（移除特殊字符）
        let partition_name = Self::sanitize_partition_name(filename);

        // 检查分区是否已存在
        let check_sql = format!(
            "SELECT 1 FROM pg_tables WHERE tablename = 'transactions_{}'",
            partition_name
        );

        let result = db
            .query_all(sea_orm::Statement::from_string(
                sea_orm::DatabaseBackend::Postgres,
                check_sql,
            ))
            .await?;

        // 如果没有找到记录，说明分区不存在
        if result.is_empty() {
            // 分区不存在，创建它
            let create_sql = format!(
                "CREATE TABLE IF NOT EXISTS transactions_{} PARTITION OF transactions FOR VALUES IN ('{}')",
                partition_name, filename
            );

            db.execute(sea_orm::Statement::from_string(
                sea_orm::DatabaseBackend::Postgres,
                create_sql,
            ))
            .await?;

            info!("Created partition: transactions_{}", partition_name);
        }

        Ok(())
    }

    /// 检查文件是否已经导入
    async fn is_file_already_imported(db: &DatabaseConnection, filename: &str) -> AppResult<bool> {
        let count_sql = format!(
            "SELECT COUNT(*) as count FROM transactions WHERE filename = '{}'",
            filename.replace("'", "''") // 防止SQL注入
        );

        let result = db
            .query_one(sea_orm::Statement::from_string(
                sea_orm::DatabaseBackend::Postgres,
                count_sql,
            ))
            .await?;

        if let Some(row) = result {
            let count: i64 = row.try_get("", "count")?;
            Ok(count > 0)
        } else {
            Ok(false)
        }
    }

    /// 获取文件的记录数
    async fn get_file_record_count(db: &DatabaseConnection, filename: &str) -> AppResult<usize> {
        let count_sql = format!(
            "SELECT COUNT(*) as count FROM transactions WHERE filename = '{}'",
            filename.replace("'", "''") // 防止SQL注入
        );

        let result = db
            .query_one(sea_orm::Statement::from_string(
                sea_orm::DatabaseBackend::Postgres,
                count_sql,
            ))
            .await?;

        if let Some(row) = result {
            let count: i64 = row.try_get("", "count")?;
            Ok(count as usize)
        } else {
            Ok(0)
        }
    }

    /// 清理分区名称，移除特殊字符
    fn sanitize_partition_name(filename: &str) -> String {
        filename
            .chars()
            .map(|c| {
                if c.is_alphanumeric() || c == '_' {
                    c
                } else {
                    '_'
                }
            })
            .collect::<String>()
            .to_lowercase()
    }

    /// 转换解析的数据为 TransactionRecord
    fn convert_to_transaction_record(
        filename: &str,
        row_number: i32,
        data: &HashMap<String, Value>,
    ) -> AppResult<TransactionRecord> {
        // 辅助函数：从 Value 中提取字符串，支持多种可能的列名
        let get_string_flexible = |keys: &[&str]| -> AppResult<String> {
            for key in keys {
                if let Some(value) = data.get(*key) {
                    if let Some(s) = value.as_str() {
                        return Ok(s.to_string());
                    }
                }
            }
            Err(AppError::InvalidFormat(format!(
                "Missing or invalid field, tried: {:?}",
                keys
            )))
        };

        // 辅助函数：从 Value 中提取字符串（单一键名）
        let get_string = |key: &str| -> AppResult<String> { get_string_flexible(&[key]) };

        // 辅助函数：从 Value 中提取可选字符串，支持多种可能的列名
        let get_optional_string_flexible = |keys: &[&str]| -> Option<String> {
            for key in keys {
                if let Some(value) = data.get(*key) {
                    if let Some(s) = value.as_str() {
                        if !s.trim().is_empty() {
                            return Some(s.to_string());
                        }
                    }
                }
            }
            None
        };

        // 辅助函数：支持多种列名格式的 get_optional_decimal
        let get_optional_decimal_flexible = |keys: &[&str]| -> Option<Decimal> {
            for key in keys {
                if let Some(value) = data.get(*key) {
                    if let Some(s) = value.as_str() {
                        if !s.trim().is_empty() {
                            // 清理数值字符串（移除逗号、货币符号等）
                            let cleaned = s
                                .replace(",", "")
                                .replace("$", "")
                                .replace("¥", "")
                                .replace("€", "")
                                .trim()
                                .to_string();

                            if let Ok(decimal) = Decimal::from_str(&cleaned) {
                                return Some(decimal);
                            }
                        }
                    }
                }
            }
            None
        };

        // 辅助函数：从 Value 中提取 Decimal
        let get_decimal = |key: &str| -> AppResult<Decimal> {
            let value = data
                .get(key)
                .ok_or_else(|| AppError::InvalidFormat(format!("Missing field: {}", key)))?;

            match value {
                Value::Number(n) => {
                    if let Some(f) = n.as_f64() {
                        Decimal::from_f64(f).ok_or_else(|| {
                            AppError::InvalidFormat(format!(
                                "Invalid decimal value for {}: {}",
                                key, f
                            ))
                        })
                    } else {
                        Err(AppError::InvalidFormat(format!(
                            "Invalid number for {}",
                            key
                        )))
                    }
                }
                Value::String(s) => Decimal::from_str(s).map_err(|_| {
                    AppError::InvalidFormat(format!("Invalid decimal string for {}: {}", key, s))
                }),
                _ => Err(AppError::InvalidFormat(format!("Invalid type for {}", key))),
            }
        };

        // 辅助函数：从 Value 中提取 DateTime
        let get_datetime = |key: &str| -> AppResult<DateTime<Utc>> {
            let value_str = get_string(key)?;

            // 尝试多种日期格式
            let formats = [
                "%Y-%m-%d %H:%M:%S",
                "%Y/%m/%d %H:%M:%S",
                "%Y-%m-%d %H:%M:%S%.f",
                "%Y/%m/%d %H:%M:%S%.f",
                "%Y-%m-%dT%H:%M:%S",
                "%Y-%m-%dT%H:%M:%SZ",
                "%Y-%m-%dT%H:%M:%S%.fZ",
            ];

            for format in &formats {
                if let Ok(naive_dt) = NaiveDateTime::parse_from_str(&value_str, format) {
                    return Ok(DateTime::from_naive_utc_and_offset(naive_dt, Utc));
                }
            }

            Err(AppError::InvalidFormat(format!(
                "Invalid datetime format for {}: {}",
                key, value_str
            )))
        };

        // 辅助函数：支持多种列名格式的 get_datetime
        let get_datetime_flexible = |keys: &[&str]| -> AppResult<DateTime<Utc>> {
            let value_str = get_string_flexible(keys)?;

            // 尝试多种日期格式
            let formats = [
                "%Y-%m-%d %H:%M:%S",
                "%Y/%m/%d %H:%M:%S",
                "%Y-%m-%d %H:%M:%S%.f",
                "%Y/%m/%d %H:%M:%S%.f",
                "%Y-%m-%dT%H:%M:%S",
                "%Y-%m-%dT%H:%M:%SZ",
                "%Y-%m-%dT%H:%M:%S%.fZ",
            ];

            for format in &formats {
                if let Ok(naive_dt) = NaiveDateTime::parse_from_str(&value_str, format) {
                    return Ok(DateTime::from_naive_utc_and_offset(naive_dt, Utc));
                }
            }

            Err(AppError::InvalidFormat(format!(
                "Invalid datetime format for {:?}: {}",
                keys, value_str
            )))
        };

        // 辅助函数：支持多种列名格式的 get_decimal
        let get_decimal_flexible = |keys: &[&str]| -> AppResult<Decimal> {
            let value_str = get_string_flexible(keys)?;

            // 清理数值字符串（移除逗号、货币符号等）
            let cleaned = value_str
                .replace(",", "")
                .replace("$", "")
                .replace("¥", "")
                .replace("€", "")
                .trim()
                .to_string();

            Decimal::from_str(&cleaned).map_err(|_| {
                AppError::InvalidFormat(format!(
                    "Invalid decimal format for {:?}: {}",
                    keys, value_str
                ))
            })
        };

        // 辅助函数：支持多种列名格式的 get_optional_datetime
        let get_optional_datetime_flexible = |keys: &[&str]| -> Option<DateTime<Utc>> {
            for key in keys {
                if let Some(value) = data.get(*key) {
                    if let Some(s) = value.as_str() {
                        if !s.trim().is_empty() {
                            // 尝试多种日期格式
                            let formats = [
                                "%Y-%m-%d %H:%M:%S",
                                "%Y/%m/%d %H:%M:%S",
                                "%Y-%m-%d %H:%M:%S%.f",
                                "%Y/%m/%d %H:%M:%S%.f",
                                "%Y-%m-%dT%H:%M:%S",
                                "%Y-%m-%dT%H:%M:%SZ",
                                "%Y-%m-%dT%H:%M:%S%.fZ",
                            ];

                            for format in &formats {
                                if let Ok(naive_dt) =
                                    NaiveDateTime::parse_from_str(s.trim(), format)
                                {
                                    return Some(DateTime::from_naive_utc_and_offset(
                                        naive_dt, Utc,
                                    ));
                                }
                            }
                        }
                    }
                }
            }
            None
        };

        Ok(TransactionRecord {
            filename: filename.to_string(),
            row_number,
            request_time: get_datetime_flexible(&["Request Time"])?,
            accounting_amount: get_decimal_flexible(&["Accounting Amount"])?,
            balance: get_decimal_flexible(&["Balance"])?,
            accounting_currency: get_string_flexible(&["Accounting Currency"])?,
            accounting_type: get_string_flexible(&["Accounting Type"])?,
            account_subject: get_string_flexible(&["Account Subject"])?,
            txn_create_time: get_datetime_flexible(&["Txn Create Time"])?,
            txn_complete_time: get_optional_datetime_flexible(&[
                "Txn Complete Time",
                "Txn complete Time",
            ]),
            merchant_txn_id: get_optional_string_flexible(&["Merchant Txn ID"]),
            trade_order_id: get_optional_string_flexible(&["Trade Order ID"]),
            country: get_optional_string_flexible(&["Country"]),
            txn_amount: get_optional_decimal_flexible(&["Txn Amount"]),
            txn_currency: get_optional_string_flexible(&["Txn Currency"]),
            payee_txn_fee: get_optional_decimal_flexible(&["Payee Txn Fee"]),
            payee_txn_fee_currency: get_optional_string_flexible(&["Payee Txn Fee Currency"]),
            payer_txn_fee: get_optional_decimal_flexible(&["Payer Txn Fee"]),
            payer_txn_fee_currency: get_optional_string_flexible(&["Payer Txn Fee Currency"]),
            payee_tax: get_optional_decimal_flexible(&["Payee Tax"]),
            payee_tax_currency: get_optional_string_flexible(&["Payee Tax Currency"]),
            payer_tax: get_optional_decimal_flexible(&["Payer Tax"]),
            payer_tax_currency: get_optional_string_flexible(&["Payer Tax Currency"]),
            remark: get_optional_string_flexible(&["Remark"]),
        })
    }

    /// 批量插入记录
    async fn batch_insert_records(
        db: &DatabaseConnection,
        records: &[TransactionRecord],
    ) -> AppResult<()> {
        if records.is_empty() {
            return Ok(());
        }

        let txn = db.begin().await?;

        let mut active_models = Vec::new();
        for record in records {
            let active_model = transactions::ActiveModel {
                id: Set(Uuid::new_v4()),
                filename: Set(record.filename.clone()),
                row_number: Set(record.row_number),
                request_time: Set(record.request_time.into()),
                accounting_amount: Set(record.accounting_amount),
                balance: Set(record.balance),
                accounting_currency: Set(record.accounting_currency.clone()),
                accounting_type: Set(record.accounting_type.clone()),
                account_subject: Set(record.account_subject.clone()),
                txn_create_time: Set(record.txn_create_time.into()),
                txn_complete_time: Set(record.txn_complete_time.map(|dt| dt.into())),
                merchant_txn_id: Set(record.merchant_txn_id.clone()),
                trade_order_id: Set(record.trade_order_id.clone()),
                country: Set(record.country.clone()),
                txn_amount: Set(record.txn_amount),
                txn_currency: Set(record.txn_currency.clone()),
                payee_txn_fee: Set(record.payee_txn_fee),
                payee_txn_fee_currency: Set(record.payee_txn_fee_currency.clone()),
                payer_txn_fee: Set(record.payer_txn_fee),
                payer_txn_fee_currency: Set(record.payer_txn_fee_currency.clone()),
                payee_tax: Set(record.payee_tax),
                payee_tax_currency: Set(record.payee_tax_currency.clone()),
                payer_tax: Set(record.payer_tax),
                payer_tax_currency: Set(record.payer_tax_currency.clone()),
                remark: Set(record.remark.clone()),
                created_at: Set(Utc::now().into()),
            };
            active_models.push(active_model);
        }

        transactions::Entity::insert_many(active_models)
            .exec(&txn)
            .await?;

        txn.commit().await?;

        Ok(())
    }
}
