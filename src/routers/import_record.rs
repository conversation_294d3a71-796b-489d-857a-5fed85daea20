use crate::{
    app_writer::{App<PERSON><PERSON><PERSON>, AppWriter},
    db::{ListParams, ListResponse},
    dtos::import_record::{ImportRecordCreate, ImportRecordResponse, ImportRecordUpdate},
    services::import_record::ImportRecordService,
};

use salvo::{
    oapi::{
        endpoint,
        extract::{JsonBody, PathParam},
    },
    Router, Writer,
};

pub fn router() -> Router {
    Router::with_path("import_record")
        .post(create_import_record)
        .put(update_import_record)
        .push(
            Router::with_path("<id>")
                .get(get_import_record_by_id)
                .delete(delete_import_record),
        )
        .push(Router::with_path("list").post(get_import_record_list))
}

/// 获取导入记录列表
#[endpoint(tags("import_record"))]
async fn get_import_record_list(
    req: <PERSON>son<PERSON><PERSON><ListParams>,
) -> AppWriter<ListResponse<ImportRecordResponse>> {
    let list = ImportRecordService::get_list(req.0.clone()).await.unwrap();

    let mut data: Vec<ImportRecordResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        data.push(tmp);
    }
    let page = req.0.page.clone().unwrap();
    let total = ImportRecordService::get_total(req.params.clone())
        .await
        .unwrap();
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };

    AppWriter(Ok(res))
}

/// 根据ID获取导入记录
#[endpoint(tags("import_record"), parameters(("id", description = "import_record id for params")))]
async fn get_import_record_by_id(id: PathParam<String>) -> AppWriter<ImportRecordResponse> {
    match ImportRecordService::get_by_id(id.0).await {
        Ok(import_record) => {
            let res = import_record.response();
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

/// 创建导入记录
#[endpoint(tags("import_record"))]
async fn create_import_record(req: JsonBody<ImportRecordCreate>) -> AppWriter<String> {
    let result = ImportRecordService::create(req.0).await;
    AppWriter(result)
}

/// 更新导入记录
#[endpoint(tags("import_record"))]
async fn update_import_record(req: JsonBody<ImportRecordUpdate>) -> AppResult<AppWriter<String>> {
    let result = ImportRecordService::update(req.0).await;
    Ok(AppWriter(result))
}

/// 删除导入记录
#[endpoint(tags("import_record"), parameters(("id", description = "import_record id")))]
async fn delete_import_record(id: PathParam<String>) -> AppWriter<String> {
    let result = ImportRecordService::delete(id.0).await;
    AppWriter(result)
}