use crate::{
    app_writer::{App<PERSON><PERSON><PERSON>, AppWriter},
    db::{ListParams, ListResponse, WhereOptions},
    dtos::sales_order_info::{SalesOrderInfoCreate, SalesOrderInfoResponse, SalesOrderInfoUpdate},
    services::sales_order_info::SalesOrderInfoService,
};
use salvo::{
    oapi::{
        endpoint,
        extract::{JsonBody, PathParam},
    },
    Request,
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("sales_order_info")
        .post(create_sales_order_info)
        .put(update_sales_order_info)
        .push(
            Router::with_path("<id>")
                .get(get_sales_order_info_by_id)
                .delete(delete_sales_order_info),
        )
        .push(Router::with_path("list").post(get_sales_order_info_list))
}

#[endpoint(tags("sales_order_info"))]
async fn get_sales_order_info_list(
    req: JsonBody<ListParams>,
) -> AppWriter<ListResponse<SalesOrderInfoResponse>> {
    let list = SalesOrderInfoService::get_list(req.0.clone())
        .await
        .unwrap();

    let mut data: Vec<SalesOrderInfoResponse> = Vec::new();
    for item in list {
        let tmp = item.response().await;
        data.push(tmp);
    }
    let page = req.0.page.clone().unwrap();
    let total = SalesOrderInfoService::get_total(req.0.params.clone())
        .await
        .unwrap();
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };
    AppWriter(Ok(res))
}

#[endpoint(tags("sales_order_info"), parameters(("id", description = "sales_order_info id for params")))]
async fn get_sales_order_info_by_id(id: PathParam<String>) -> AppWriter<SalesOrderInfoResponse> {
    match SalesOrderInfoService::get_by_id(id.0).await {
        Ok(sales_order_info) => {
            let res = sales_order_info.response().await;
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("sales_order_info"))]
async fn create_sales_order_info(req: JsonBody<SalesOrderInfoCreate>) -> AppWriter<String> {
    let result = SalesOrderInfoService::create(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("sales_order_info"))]
async fn update_sales_order_info(
    req: JsonBody<SalesOrderInfoUpdate>,
) -> AppResult<AppWriter<String>> {
    let result = SalesOrderInfoService::update(req.0).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("sales_order_info"), parameters(("id", description = "user id")))]
async fn delete_sales_order_info(id: PathParam<String>) -> AppWriter<String> {
    let result = SalesOrderInfoService::delete(id.0).await;
    AppWriter(result)
}
