use crate::{
    app_writer::{App<PERSON><PERSON><PERSON>, AppWriter},
    db::{ListOptions, ListParams, ListResponse},
    dtos::repayment_log::{RepaymentLogCreate, RepaymentLogResponse, RepaymentLogUpdate},
    services::{repayment_log::RepaymentLogService, user::UserService},
};
use salvo::{
    http::form::FilePart,
    oapi::{
        endpoint,
        extract::{JsonBody, PathParam},
    },
    Depot, Request,
};
use salvo::{Router, Writer};
use serde_json;

pub fn router() -> Router {
    Router::with_path("repayment_log")
        .post(create_repayment_log)
        .put(update_repayment_log)
        .push(Router::with_path("list").post(get_repayment_log_list))
        .push(
            Router::with_path("<id>")
                .get(get_repayment_log_by_id)
                .delete(delete_repayment_log),
        )
}

#[endpoint(tags("repayment_log"))]
async fn get_repayment_log_list(
    req: JsonBody<ListParams>,
) -> AppWriter<ListResponse<RepaymentLogResponse>> {
    let list = RepaymentLogService::get_list(req.0.clone()).await.unwrap();

    let mut data: Vec<RepaymentLogResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        data.push(tmp);
    }
    let page = req.0.page.clone().unwrap();
    let total = RepaymentLogService::get_total(req.params.clone())
        .await
        .unwrap();
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };

    AppWriter(Ok(res))
}

#[endpoint(tags("repayment_log"), parameters(("id", description = "repayment_log id for params")))]
async fn get_repayment_log_by_id(id: PathParam<String>) -> AppWriter<RepaymentLogResponse> {
    match RepaymentLogService::get_by_id(id.0).await {
        Ok(repayment_log) => {
            let res = repayment_log.response();
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("repayment_log"))]
async fn create_repayment_log(req: &mut Request, depot: &mut Depot) -> AppWriter<String> {
    // 解析 form-data
    let form_data = match req.form_data().await {
        Ok(form) => form,
        Err(e) => {
            return AppWriter(Err(anyhow::anyhow!("解析表单数据失败: {}", e).into()));
        }
    };

    // 提取文件（可选）
    let file: Option<FilePart> = form_data.files.get("file").cloned();

    // 提取并解析 objectData
    let object_data_str = match form_data.fields.get("objectData") {
        Some(value) => value,
        None => {
            return AppWriter(Err(anyhow::anyhow!("缺少 objectData 字段").into()));
        }
    };

    // 解析 JSON 字符串为 RepaymentLogCreate
    let mut repayment_log_data: RepaymentLogCreate = match serde_json::from_str(object_data_str) {
        Ok(data) => data,
        Err(e) => {
            return AppWriter(Err(
                anyhow::anyhow!("解析 objectData JSON 失败: {}", e).into()
            ));
        }
    };

    let current_user = depot.get::<String>("current_user").unwrap();
    // 设置操作人信息
    let user = UserService::get_by_id(current_user.clone()).await.unwrap();
    repayment_log_data.creater_id = Some(user.id.unwrap().to_string());
    repayment_log_data.creater_name = Some(user.username);

    // 调用服务层创建还款日志
    let result = RepaymentLogService::create(file, repayment_log_data).await;
    AppWriter(result)
}

#[endpoint(tags("repayment_log"))]
async fn update_repayment_log(req: JsonBody<RepaymentLogUpdate>) -> AppResult<AppWriter<String>> {
    let result = RepaymentLogService::update(req.0).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("repayment_log"), parameters(("id", description = "repayment_log id")))]
async fn delete_repayment_log(id: PathParam<String>) -> AppWriter<String> {
    let result = RepaymentLogService::delete(id.0).await;
    AppWriter(result)
}
