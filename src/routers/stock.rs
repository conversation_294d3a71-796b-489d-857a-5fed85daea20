use crate::{
    app_writer::{AppR<PERSON>ult, AppWriter},
    db::ListParams,
    dtos::stock::{StockCreate, StockResponse, StockUpdate},
    services::stock::StockService,
};
use salvo::{
    oapi::{
        endpoint,
        extract::{JsonBody, PathParam},
    },
    Request,
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("stock")
        .post(create_stock)
        .put(update_stock)
        .push(
            Router::with_path("<id>")
                .get(get_stock_by_id)
                .delete(delete_stock),
        )
        .push(Router::with_path("list").post(get_stock_list))
}

#[endpoint(tags("stock"))]
async fn get_stock_list(req: &mut Request) -> AppResult<AppWriter<Vec<StockResponse>>> {
    let req: ListParams = req.extract().await?;
    let list = StockService::get_list(req).await?;

    let mut res: Vec<StockResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        res.push(tmp);
    }

    Ok(AppWriter(Ok(res)))
}

#[endpoint(tags("stock"), parameters(("id", description = "stock id for params")))]
async fn get_stock_by_id(id: PathParam<String>) -> AppWriter<StockResponse> {
    match StockService::get_by_id(id.0).await {
        Ok(stock) => {
            let res = stock.response();
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("stock"))]
async fn create_stock(req: JsonBody<StockCreate>) -> AppWriter<String> {
    let result = StockService::create(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("stock"))]
async fn update_stock(req: JsonBody<StockUpdate>) -> AppResult<AppWriter<String>> {
    let result = StockService::update(req.0).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("stock"), parameters(("id", description = "user id")))]
async fn delete_stock(id: PathParam<String>) -> AppWriter<String> {
    let result = StockService::delete(id.0).await;
    AppWriter(result)
}
