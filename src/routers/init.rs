use crate::{
    app_writer::AppWriter,
    config::{MENU_CONFIG, PERMISSION_CONFIG},
    db,
    dtos::{role::RoleCreate, user::UserCreate},
    middleware::init::init_check,
    services,
};
use anyhow::anyhow;
use salvo::{oapi::endpoint, prelude::*, Depot};

pub fn router() -> Router {
    Router::with_path("/init")
        .push(Router::with_path("/db").post(init_db))
        .hoop(init_check)
}

#[endpoint(tags("common"))]
async fn init_db(depot: &mut Depot) -> AppWriter<String> {
    let init_user = depot.get::<String>("init_user").unwrap();
    if init_user.is_empty() {
        return AppWriter(Err(anyhow!("init_user is required").into()));
    }
    if init_user != "ferychen" {
        return AppWriter(Err(anyhow!("init_user is invalid").into()));
    }
    let init_pwd = depot.get::<String>("init_pwd").unwrap();
    if init_pwd.is_empty() {
        return AppWriter(Err(anyhow!("init_pwd is required").into()));
    }
    if init_pwd != "123$%^aB" {
        return AppWriter(Err(anyhow!("init_pwd is invalid").into()));
    }
    // 插入数据库清空表，初始化的操作
    let role_normal = RoleCreate {
        name: "普通用户".to_string(),
        code: Some("normal_user".to_string()),
        desc: Some("系统初始化默认角色".to_string()),
        stable: true,
        order: 2,
    };
    let _res =
        crate::services::role::RoleService::create(db::CreateParams { data: role_normal }).await;
    let role_admin = RoleCreate {
        name: "超级管理员".to_string(),
        code: Some("administrator".to_string()),
        desc: Some("系统初始化默认角色".to_string()),
        stable: true,
        order: 1,
    };
    let _res =
        crate::services::role::RoleService::create(db::CreateParams { data: role_admin }).await;
    let admin_init = UserCreate {
        login_name: "admin".to_string(),
        login_pwd: "123$%^aB".to_string(),
        role_id: Some(_res.unwrap()),
        is_admin: true,
        username: "系统管理员".to_string(),
        is_active: true,
        company_id: None,
        ..Default::default()
    };
    let admin_res =
        crate::services::user::UserService::create(db::CreateParams { data: admin_init }).await;
    if admin_res.is_err() {
        log::error!("init admin user error: {:?}", admin_res);
    } else {
        log::info!("init admin user success");
    }
    init_permission().await;
    init_menu().await;
    let _ = services::permission::PermissionService::init_permission().await;
    AppWriter(Ok("init db success".to_string()))
}

async fn init_permission() {
    for item in &PERMISSION_CONFIG.permissions {
        let code = format!("{}:{}:{}", item.group, item.method, item.path);
        let _ = crate::services::permission::PermissionService::create(db::CreateParams {
            data: crate::dtos::permission::PermissionCreate {
                name: item.name.clone(),
                code: Some(code),
                path: Some(item.path.clone()),
                method: Some(item.method.clone()),
                backup: Some("系统初始化创建".to_string()),
                group: Some(item.group.clone()),
            },
        })
        .await;
    }
}

async fn init_menu() {
    for item in &MENU_CONFIG.menus {
        let _ = crate::services::menu::MenuService::create(db::CreateParams {
            data: crate::dtos::menu::MenuCreate {
                name: item.name.clone(),
                order: item.order,
                path: Some(item.path.clone()),
                component: Some(item.component.clone()),
                redirect: Some(item.redirect.clone()),
                active: Some(item.active.clone()),
                title: Some(item.title.clone()),
                icon: Some(item.icon.clone()),
                keep_alive: Some(item.keep_alive.clone()),
                hidden: Some(item.hidden.clone()),
                is_link: Some(item.is_link.clone()),
                parent: Some(item.parent.clone()),
                remark: Some(item.remark.clone()),
            },
        })
        .await;
    }
}
