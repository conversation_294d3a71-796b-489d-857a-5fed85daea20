use crate::{
    app_writer::{AppR<PERSON>ult, AppWriter},
    db::{CreateParams, IdParams, ListParams, ListResponse, Page, UpdateParams, WhereOptions},
    dtos::permission::{PermissionCreate, PermissionResponse, PermissionUpdate},
    services::permission::PermissionService,
};
use anyhow::anyhow;
use salvo::{
    oapi::endpoint,
    oapi::extract::{JsonBody, PathParam},
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("permission")
        .post(create_permission)
        .put(update_permission)
        .push(Router::with_path("list").post(get_permission_list))
        .push(Router::with_path("role").post(get_permission_by_role))
        .push(
            Router::with_path("<id>")
                .get(get_permission_by_id)
                .delete(delete_permission),
        )
}

#[endpoint(tags("permission"))]
async fn get_permission_list(
    req: JsonBody<ListParams>,
) -> AppWriter<ListResponse<PermissionResponse>> {
    let page_params = req.0.page.clone();
    let mut page = Page::default();
    if page_params.is_some() {
        page = page_params.unwrap();
    };
    let req_in = ListParams {
        page: Some(page.clone()),
        params: req.0.params.clone(),
        options: req.0.options.clone(),
    };
    log::info!("req_in: {:?}", req_in);
    let list = PermissionService::get_list(req_in.clone()).await.unwrap();

    let mut data: Vec<PermissionResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        data.push(tmp);
    }
    let total = PermissionService::get_total(req_in.params).await.unwrap();
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };
    AppWriter(Ok(res))
}

#[endpoint(tags("permission"))]
async fn get_permission_by_role(req: JsonBody<IdParams>) -> AppWriter<Vec<PermissionResponse>> {
    let list = PermissionService::get_list_by_role(req.0.id.clone())
        .await
        .unwrap();
    let mut res: Vec<PermissionResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        res.push(tmp);
    }
    AppWriter(Ok(res))
}

#[endpoint(tags("permission"), parameters(("id", description = "permission id")))]
async fn get_permission_by_id(id: PathParam<String>) -> AppWriter<PermissionResponse> {
    match PermissionService::get_by_id(id.0).await {
        Ok(permission) => {
            if let Some(permission) = permission {
                let res = permission.response();
                AppWriter(Ok(res))
            } else {
                AppWriter(Err(anyhow!("permission not found").into()))
            }
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("permission"), parameters(("permissionname", description = "permission name")))]
async fn get_permission_by_name(
    permissionname: PathParam<String>,
) -> AppWriter<PermissionResponse> {
    let params = vec![WhereOptions {
        var: "name".to_string(),
        val: permissionname.0,
        operator: "=".to_string(),
    }];
    match PermissionService::get_by_query(params).await {
        Ok(permission) => {
            if let Some(permission) = permission {
                let res = permission.response();
                AppWriter(Ok(res))
            } else {
                AppWriter(Err(anyhow!("permission not found").into()))
            }
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("permission"))]
async fn create_permission(req: JsonBody<PermissionCreate>) -> AppWriter<String> {
    let result = PermissionService::create(CreateParams { data: req.0 }).await;
    AppWriter(result)
}

#[endpoint(tags("permission"))]
async fn update_permission(req: JsonBody<PermissionUpdate>) -> AppResult<AppWriter<String>> {
    let result = PermissionService::update(UpdateParams { data: req.0 }).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("permission"), parameters(("id", description = "user id")))]
async fn delete_permission(id: PathParam<String>) -> AppWriter<String> {
    let result = PermissionService::delete(id.0).await;
    AppWriter(result)
}
