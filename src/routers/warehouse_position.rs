use crate::{
    app_writer::{AppResult, AppWriter},
    db::ListParams,
    dtos::warehouse_position::{
        WarehousePositionCreate, WarehousePositionResponse, WarehousePositionUpdate,
    },
    services::warehouse_position::WarehousePositionService,
};
use salvo::{
    oapi::{
        endpoint,
        extract::{JsonBody, PathParam},
    },
    Request,
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("warehouse_position")
        .post(create_warehouse_position)
        .put(update_warehouse_position)
        .push(
            Router::with_path("<id>")
                .get(get_warehouse_position_by_id)
                .delete(delete_warehouse_position),
        )
        .push(Router::with_path("list").post(get_warehouse_position_list))
}

#[endpoint(tags("warehouse_position"))]
async fn get_warehouse_position_list(
    req: &mut Request,
) -> AppResult<AppWriter<Vec<WarehousePositionResponse>>> {
    let req: ListParams = req.extract().await?;
    let list = WarehousePositionService::get_list(req).await?;

    let mut res: Vec<WarehousePositionResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        res.push(tmp);
    }

    Ok(AppWriter(Ok(res)))
}

#[endpoint(tags("warehouse_position"), parameters(("id", description = "warehouse_position id for params")))]
async fn get_warehouse_position_by_id(
    id: PathParam<String>,
) -> AppWriter<WarehousePositionResponse> {
    match WarehousePositionService::get_by_id(id.0).await {
        Ok(warehouse_position) => {
            let res = warehouse_position.response();
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("warehouse_position"))]
async fn create_warehouse_position(req: JsonBody<WarehousePositionCreate>) -> AppWriter<String> {
    let result = WarehousePositionService::create(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("warehouse_position"))]
async fn update_warehouse_position(
    req: JsonBody<WarehousePositionUpdate>,
) -> AppResult<AppWriter<String>> {
    let result = WarehousePositionService::update(req.0).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("warehouse_position"), parameters(("id", description = "user id")))]
async fn delete_warehouse_position(id: PathParam<String>) -> AppWriter<String> {
    let result = WarehousePositionService::delete(id.0).await;
    AppWriter(result)
}
