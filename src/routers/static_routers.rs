use rust_embed::RustEmbed;
use salvo::fs::NamedFile;

use salvo::{http::ResBody, hyper::body::Bytes, oapi::endpoint, Request, Response, Router};
use std::path::Path;
#[derive(RustEmbed)]
#[folder = "assets"]
struct Assets;

#[allow(dead_code)]
pub fn create_static_routers() -> Vec<Router> {
    let icon_router = Router::with_path("favicon.ico").get(get_icon);
    let upload_router = Router::with_path("upload/<**path>").get(serve_upload_file_with_headers);
    vec![icon_router, upload_router]
}

#[endpoint(tags("comm"))]
pub async fn get_icon(res: &mut Response) {
    let icon = Assets::get("favicon.ico").unwrap();
    res.body(ResBody::Once(Bytes::from(icon.data.to_vec())));
}

#[endpoint(tags("comm"))]
pub async fn serve_upload_file_with_headers(req: &mut Request, res: &mut Response) {
    // 从URI路径中获取完整路径，去掉开头的"/"
    let uri_path = req.uri().path();
    let full_path = if uri_path.starts_with('/') {
        &uri_path[1..] // 去掉开头的"/"
    } else {
        uri_path
    };
    let path = Path::new(&full_path);

    // 检查文件是否存在
    if path.exists() && path.is_file() {
        // 根据文件扩展名设置正确的 Content-Type 和 Content-Disposition
        if let Some(ext) = path.extension().and_then(|s| s.to_str()) {
            match ext.to_lowercase().as_str() {
                "pdf" => {
                    res.headers_mut()
                        .insert("Content-Type", "application/pdf".parse().unwrap());
                    // 设置为 inline 让浏览器直接显示而不是下载
                    res.headers_mut()
                        .insert("Content-Disposition", "inline".parse().unwrap());
                }
                "jpg" | "jpeg" => {
                    res.headers_mut()
                        .insert("Content-Type", "image/jpeg".parse().unwrap());
                    res.headers_mut()
                        .insert("Content-Disposition", "inline".parse().unwrap());
                }
                "png" => {
                    res.headers_mut()
                        .insert("Content-Type", "image/png".parse().unwrap());
                    res.headers_mut()
                        .insert("Content-Disposition", "inline".parse().unwrap());
                }
                "gif" => {
                    res.headers_mut()
                        .insert("Content-Type", "image/gif".parse().unwrap());
                    res.headers_mut()
                        .insert("Content-Disposition", "inline".parse().unwrap());
                }
                "doc" => {
                    res.headers_mut()
                        .insert("Content-Type", "application/msword".parse().unwrap());
                }
                "docx" => {
                    res.headers_mut().insert(
                        "Content-Type",
                        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                            .parse()
                            .unwrap(),
                    );
                }
                _ => {}
            }
        }

        // 使用 NamedFile 来服务文件
        match NamedFile::open(path).await {
            Ok(named_file) => {
                named_file.send(req.headers(), res).await;
            }
            Err(_) => {
                res.status_code(salvo::http::StatusCode::NOT_FOUND);
                res.render("File not found");
            }
        }
    } else {
        res.status_code(salvo::http::StatusCode::NOT_FOUND);
        res.render("File not found");
    }
}
