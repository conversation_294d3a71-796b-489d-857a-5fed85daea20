use crate::{
    app_writer::{<PERSON><PERSON><PERSON><PERSON><PERSON>, App<PERSON>riter},
    db::{CreateParams, FlexibleParams, IdParams, ListParams, ListResponse, Page, UpdateParams},
    dtos::user::{UserCreate, UserResponse, UserUpdate},
    services::{financial_contract, user::UserService},
};
use anyhow::anyhow;
use salvo::{
    oapi::{
        endpoint,
        extract::{JsonBody, PathParam},
    },
    Depot,
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("user")
        .post(create_user)
        .put(update_user)
        .push(Router::with_path("search").post(search_user))
        .push(Router::with_path("current").post(get_current_user))
        .push(Router::with_path("list").post(get_user_list))
        .push(Router::with_path("role").post(get_user_by_role))
        .push(
            Router::with_path("<id>")
                .get(get_user_by_id)
                .put(update_user_fields)
                .delete(delete_user),
        )
}

#[endpoint(tags("users"))]
async fn get_user_list(req: JsonBody<ListParams>) -> AppWriter<ListResponse<UserResponse>> {
    let page_params = req.0.page.clone();
    let mut page = Page::default();
    if page_params.is_some() {
        page = page_params.unwrap();
    };
    let list = UserService::get_list(req.0.clone()).await.unwrap();

    let mut data: Vec<UserResponse> = Vec::new();
    for item in list {
        let mut tmp = item.response();
        tmp.login_pwd = "******".to_string();
        data.push(tmp);
    }
    let total = UserService::get_total(req.0.params).await.unwrap();
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };

    AppWriter(Ok(res))
}

#[endpoint(tags("users"))]
async fn get_user_by_role(req: JsonBody<FlexibleParams>) -> AppWriter<ListResponse<UserResponse>> {
    let list = UserService::get_list_by_role(req.0.clone()).await.unwrap();
    let mut res: Vec<UserResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        res.push(tmp);
    }
    let mut page = Page::default();
    if req.0.page.is_some() {
        page = req.0.page.unwrap();
    }
    let total = UserService::get_total(req.0.params).await.unwrap();
    let res = ListResponse {
        data: res,
        total,
        page: page.page,
        size: page.limit,
    };
    AppWriter(Ok(res))
}

#[endpoint(
    tags("users"),
    parameters(("id", description = "user id"))
)]
async fn get_user_by_id(id: PathParam<String>) -> AppWriter<UserResponse> {
    match UserService::get_by_id(id.0).await {
        Ok(user) => {
            let mut res = user.clone().response();
            res.login_pwd = "******".to_string();
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("users"))]
async fn search_user(req: JsonBody<IdParams>) -> AppWriter<UserResponse> {
    match UserService::get_by_login_name(req.0.id).await {
        Ok(user) => {
            let mut res = user.response();
            res.login_pwd = "******".to_string();
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("users"))]
async fn create_user(req: JsonBody<UserCreate>) -> AppWriter<String> {
    let result = UserService::create(CreateParams { data: req.0 }).await;
    AppWriter(result)
}

// #[endpoint(tags("users"))]
// async fn update_user(req: &mut Request) -> AppResult<AppWriter<String>> {
//     let req: UserUpdate = req.extract().await?;
//     let result = user::update(UpdateParams { data: req }).await;
//     Ok(AppWriter(result))
// }
#[endpoint(tags("users"))]
async fn update_user(req: JsonBody<UserUpdate>) -> AppResult<AppWriter<String>> {
    let result = UserService::update(UpdateParams { data: req.0 }).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("users"))]
async fn update_user_fields(
    id: PathParam<String>,
    req: JsonBody<IdParams>,
) -> AppResult<AppWriter<String>> {
    let req_id = req.0.id.clone();
    if req_id != id.0 {
        return Ok(AppWriter(Err(anyhow!("user id not match!").into())));
    }
    let result = UserService::update_fields(req.0.id, req.0.update_fields.unwrap()).await;
    Ok(AppWriter(result))
}

#[endpoint(
    tags("users"),
    parameters(("id", description = "user id"))
)]
async fn delete_user(id: PathParam<String>) -> AppWriter<String> {
    let result = UserService::delete(id.0).await;
    AppWriter(result)
}

#[endpoint(tags("users"))]
async fn get_current_user(depot: &mut Depot) -> AppWriter<UserResponse> {
    let req = depot.get::<String>("current_user").unwrap();
    match UserService::get_by_id(req.clone()).await {
        Ok(user) => {
            let mut res = user.response();
            res.login_pwd = "******".to_string();
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("users"))]
async fn update_current_user(
    req: JsonBody<UserUpdate>,
    depot: &mut Depot,
) -> AppResult<AppWriter<String>> {
    let user_id = depot.get::<String>("current_user").unwrap();
    if req.0.id != *user_id {
        return Ok(AppWriter(Err(anyhow!("user infomation not match!").into())));
    }
    let res = UserService::update(UpdateParams { data: req.0 }).await;
    Ok(AppWriter(res))
}
