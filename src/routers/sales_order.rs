use std::path::Path;

use crate::{
    app_writer::{AppR<PERSON>ult, AppWriter},
    db::{IdParams, IdsParams, ListOptions, ListParams, ListResponse, WhereOptions},
    dtos::{
        import_record::ImportRecordCreate,
        sales_order::{SalesOrderCreate, SalesOrderResponse, SalesOrderSummary, SalesOrderUpdate},
    },
    services::{
        import_record::ImportRecordService,
        import_task::{AsyncImportParams, ImportTaskService},
        order_import::{
            platform_importers::{GkOrderImporter, VipOrderImporter},
            OrderImportService,
        },
        sales_order::SalesOrderService,
        upload::UploadService,
    },
    utils::{
        order_sdk::{gk_order, vip_order},
        rand_utils::random_uppercase_serial,
    },
};
use anyhow::anyhow;
use rust_decimal::Decimal;
use salvo::{
    oapi::{
        endpoint,
        extract::{JsonBody, PathParam},
    },
    Depot, Request,
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("sales_order")
        .post(create_sales_order)
        .put(update_sales_order)
        .push(Router::with_path("list").post(get_sales_order_list))
        .push(Router::with_path("import").post(import_orders))
        .push(Router::with_path("count_payment").post(count_total_payment))
        .push(Router::with_path("update_batch").post(update_batch_by_ids))
        .push(
            Router::with_path("<id>")
                .get(get_sales_order_by_id)
                .delete(delete_sales_order),
        )
}

#[endpoint(tags("sales_order"))]
async fn get_sales_order_list(
    req: JsonBody<ListParams>,
) -> AppWriter<ListResponse<SalesOrderResponse>> {
    println!("传入参数：：：{:?}", req.0);
    let list = SalesOrderService::get_list(req.0.clone()).await.unwrap();

    let mut data: Vec<SalesOrderResponse> = Vec::new();
    for item in list {
        let tmp = item.response().await;
        data.push(tmp);
    }
    let page = req.0.page.clone().unwrap();
    let total = SalesOrderService::get_total(req.params.clone())
        .await
        .unwrap();
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };

    AppWriter(Ok(res))
}

#[endpoint(tags("sales_order"), parameters(("id", description = "sales_order id for params")))]
async fn get_sales_order_by_id(id: PathParam<String>) -> AppWriter<SalesOrderResponse> {
    match SalesOrderService::get_by_id(id.0).await {
        Ok(sales_order) => {
            let res = sales_order.response().await;
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("sales_order"))]
async fn create_sales_order(req: JsonBody<SalesOrderCreate>) -> AppWriter<String> {
    let result = SalesOrderService::create(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("sales_order"))]
async fn update_sales_order(req: JsonBody<SalesOrderUpdate>) -> AppResult<AppWriter<String>> {
    let result = SalesOrderService::update(req.0).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("sales_order"), parameters(("id", description = "user id")))]
async fn delete_sales_order(id: PathParam<String>) -> AppWriter<String> {
    let result = SalesOrderService::delete(id.0).await;
    AppWriter(result)
}

#[endpoint(tags("sales_order"))]
async fn import_orders(
    req: &mut Request,
    depot: &mut Depot,
) -> AppResult<AppWriter<ImportRecordCreate>> {
    let uid = depot.get::<String>("current_user").unwrap();
    let form = req.form_data().await?;
    let contract_id = form.fields.get("contract_id").cloned();
    let contract_name = form.fields.get("contract_name").cloned();
    println!("合同id: {:?}", contract_id);
    if contract_id.is_none() {
        return Err(anyhow!("合同id不能为空").into());
    }

    let sheet_name = form.fields.get("sheet_name").cloned();
    println!("sheet_name: {:?}", sheet_name);
    let file = form.files.get("file").cloned();
    let platform = form
        .fields
        .get("platform")
        .cloned()
        .unwrap_or("vip".to_string());

    if file.is_none() {
        return Err(anyhow!("文件上传失败").into());
    }
    let path =
        UploadService::upload_tmp(file.unwrap(), uid.clone(), "order_import".to_string()).await?;

    let serial = random_uppercase_serial(Some("IPR".to_string()), 8);

    // 3. 文件格式验证（同步，快速）
    // 简单验证文件是否可以解析，但不执行实际导入
    let validation_result = match platform.as_str() {
        "vip" => {
            // 只验证文件格式，不导入数据
            validate_excel_file(Path::new(&path), sheet_name.clone()).await
        }
        "gk" => {
            // 只验证文件格式，不导入数据
            validate_excel_file(Path::new(&path), sheet_name.clone()).await
        }
        _ => return Err(anyhow!("不支持的平台类型").into()),
    };

    // 如果文件格式验证失败，直接返回错误
    if let Err(e) = validation_result {
        return Err(anyhow!("文件格式验证失败: {}", e).into());
    }

    // 4. 创建 processing 状态的 ImportRecord（同步，快速）
    let file_info = form.files.get("file").unwrap();
    let file_name = file_info.name().unwrap_or("unknown").to_string();
    let file_extension = Path::new(&file_name)
        .extension()
        .and_then(|ext| ext.to_str())
        .unwrap_or("unknown")
        .to_string();

    let mut record = ImportRecordCreate::default();
    record.serial = Some(serial.clone());
    record.contract_id = contract_id.clone().unwrap();
    record.contract_name = contract_name;
    record.source = "手动导入".to_string();
    record.file_type = Some(file_extension);
    record.save_dir = "order_import".to_string();
    record.file_name = file_name;
    record.file_link = path.clone();
    record.status = "processing".to_string(); // 设置为处理中状态

    // 保存 processing 状态的记录
    ImportRecordService::create(record.clone()).await?;

    // 5. 启动异步导入任务（异步，不阻塞）
    let async_params = AsyncImportParams {
        serial: serial.clone(),
        platform: platform.clone(),
        file_path: path,
        sheet_name,
        contract_id: contract_id.unwrap(),
    };

    ImportTaskService::spawn_async_import(async_params);

    // 6. 立即返回 processing 状态的记录
    Ok(AppWriter(Ok(record)))
}

#[endpoint(tags("sales_order"))]
async fn count_total_payment(req: JsonBody<IdParams>) -> AppWriter<SalesOrderSummary> {
    let contract_id = req.0.clone();
    let params = vec![WhereOptions::new("contract_id".to_string(), contract_id.id)];
    let res = SalesOrderService::count_total_payment(params).await;
    AppWriter(res)
}

#[endpoint(tags("sales_order"))]
async fn update_batch_by_ids(req: JsonBody<IdsParams>) -> AppWriter<String> {
    let res = SalesOrderService::update_batch_by_ids(req.0.ids, req.0.update_fields).await;
    AppWriter(res)
}

/// 验证Excel文件格式
///
/// # 参数
/// * `file_path` - 文件路径
/// * `sheet_name` - 工作表名称
///
/// # 返回值
/// * `AppResult<()>` - 验证结果
///
/// # 功能
/// 快速验证Excel文件是否可以打开和读取指定工作表，不执行实际的数据解析
async fn validate_excel_file(file_path: &Path, sheet_name: Option<String>) -> AppResult<()> {
    use calamine::{open_workbook, Reader, Xlsx};

    // 尝试打开工作簿
    let mut workbook: Xlsx<_> =
        open_workbook(file_path).map_err(|e| anyhow!("无法打开Excel文件: {}", e))?;

    // 确定工作表名称
    let sheet = sheet_name.unwrap_or_else(|| "sheet1".to_string());

    // 尝试读取工作表范围（不解析具体数据）
    let _range = workbook
        .worksheet_range(&sheet)
        .map_err(|e| anyhow!("无法读取工作表 '{}': {}", sheet, e))?;

    Ok(())
}
