use crate::{
    app_writer::{A<PERSON><PERSON><PERSON><PERSON>, AppWriter},
    db::{ListParams, ListResponse, Page},
    dtos::company::{CompanyCreate, CompanyResponse, CompanyUpdate},
    services::company::CompanyService,
};
use salvo::oapi::{
    endpoint,
    extract::{JsonBody, PathParam},
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("company")
        .post(create_company)
        .put(update_company)
        .push(
            Router::with_path("<id>")
                .get(get_company_by_id)
                .delete(delete_company),
        )
        .push(Router::with_path("list").post(get_company_list))
}

#[endpoint(tags("company"))]
async fn get_company_list(req: JsonBody<ListParams>) -> AppWriter<ListResponse<CompanyResponse>> {
    let page_params = req.0.page.clone();
    let mut page = Page::default();
    if page_params.is_some() {
        page = page_params.unwrap();
    };
    let list = CompanyService::get_list(req.0.clone()).await.unwrap();
    let mut data: Vec<CompanyResponse> = Vec::new();
    for item in list {
        let tmp = item.response().await;
        data.push(tmp);
    }
    let total = CompanyService::get_total(req.0.params).await.unwrap();
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };

    AppWriter(Ok(res))
}

#[endpoint(tags("company"), parameters(("id", description = "company id for params")))]
async fn get_company_by_id(id: PathParam<String>) -> AppWriter<CompanyResponse> {
    match CompanyService::get_by_id(id.0).await {
        Ok(company) => {
            let res = company.response().await;
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("company"))]
async fn create_company(req: JsonBody<CompanyCreate>) -> AppWriter<String> {
    let result = CompanyService::create(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("company"))]
async fn update_company(req: JsonBody<CompanyUpdate>) -> AppResult<AppWriter<String>> {
    let result = CompanyService::update(req.0).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("company"), parameters(("id", description = "user id")))]
async fn delete_company(id: PathParam<String>) -> AppWriter<String> {
    let result = CompanyService::delete(id.0).await;
    AppWriter(result)
}
