use crate::{
    app_writer::{AppResult, AppWriter},
    db::{ListParams, WhereOptions},
    dtos::attachment::{AttachmentCreate, AttachmentResponse, AttachmentUpdate},
    services::attachment::AttachmentService,
};
use salvo::{
    oapi::{
        endpoint,
        extract::{JsonBody, PathParam},
    },
    Depot, Request,
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("attachment")
        .post(create_attachment)
        .put(update_attachment)
        .push(
            Router::with_path("<id>")
                .get(get_attachment_by_id)
                .delete(delete_attachment),
        )
        .push(Router::with_path("list").post(get_attachment_list))
}

#[endpoint(tags("attachment"))]
async fn get_attachment_list(req: &mut Request) -> AppResult<AppWriter<Vec<AttachmentResponse>>> {
    let req: ListParams = req.extract().await?;
    let list = AttachmentService::get_list(req).await?;

    let mut res: Vec<AttachmentResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        res.push(tmp);
    }

    Ok(AppWriter(Ok(res)))
}

#[endpoint(tags("attachment"), parameters(("id", description = "attachment id for params")))]
async fn get_attachment_by_id(id: PathParam<String>) -> AppWriter<AttachmentResponse> {
    match AttachmentService::get_by_id(id.0).await {
        Ok(attachment) => {
            let res = attachment.response();
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("attachment"))]
async fn create_attachment(req: JsonBody<AttachmentCreate>) -> AppWriter<String> {
    let result = AttachmentService::create(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("attachment"))]
async fn update_attachment(req: JsonBody<AttachmentUpdate>) -> AppWriter<String> {
    let result = AttachmentService::update(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("attachment"), parameters(("id", description = "user id")))]
async fn delete_attachment(id: PathParam<String>) -> AppWriter<String> {
    let result = AttachmentService::delete(id.0).await;
    AppWriter(result)
}
