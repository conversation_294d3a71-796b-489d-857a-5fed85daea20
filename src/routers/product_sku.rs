use crate::{
    app_writer::{App<PERSON><PERSON><PERSON>, AppWriter},
    db::{ListParams, ListResponse, WhereOptions},
    dtos::product_sku::{ProductSkuCreate, ProductSkuResponse, ProductSkuUpdate},
    services::product_sku::ProductSkuService,
};
use salvo::{
    oapi::{
        endpoint,
        extract::{JsonBody, PathParam},
    },
    Depot,
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("product_sku")
        .post(create_product_sku)
        .put(update_product_sku)
        .push(
            Router::with_path("<id>")
                .get(get_product_sku_by_id)
                .delete(delete_product_sku),
        )
        .push(Router::with_path("list").post(get_product_sku_list))
        .push(Router::with_path("draft").post(draft_product_sku)) // 添加draft子路由
}

#[endpoint(tags("product_sku"))]
async fn get_product_sku_list(
    req: JsonBody<ListParams>,
) -> AppWriter<ListResponse<ProductSkuResponse>> {
    let list = ProductSkuService::get_list(req.0.clone()).await.unwrap();
    let total = ProductSkuService::get_total(req.0.params.clone())
        .await
        .unwrap();
    let page = req.0.page.clone().unwrap();
    let mut data: Vec<ProductSkuResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        data.push(tmp);
    }
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };
    AppWriter(Ok(res))
}

#[endpoint(tags("product_sku"), parameters(("id", description = "product_sku id for params")))]
async fn get_product_sku_by_id(id: PathParam<String>) -> AppWriter<ProductSkuResponse> {
    match ProductSkuService::get_by_id(id.0).await {
        Ok(product_sku) => {
            let res = product_sku.response();
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("product_sku"))]
async fn create_product_sku(req: JsonBody<ProductSkuCreate>) -> AppWriter<String> {
    let result = ProductSkuService::create(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("product_sku"))]
async fn update_product_sku(req: JsonBody<ProductSkuUpdate>) -> AppWriter<String> {
    let result = ProductSkuService::update(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("product_sku"), parameters(("id", description = "user id")))]
async fn delete_product_sku(id: PathParam<String>) -> AppWriter<String> {
    let result = ProductSkuService::delete(id.0).await;
    AppWriter(result)
}

#[endpoint(tags("product_sku"))]
async fn draft_product_sku() -> AppWriter<String> {
    let product_sku_create = ProductSkuCreate::default();
    let result = ProductSkuService::draft(product_sku_create).await;
    AppWriter(result)
}

// 发布文章
#[endpoint(tags("product_sku"))]
async fn publish_product_sku(req: JsonBody<ProductSkuUpdate>) -> AppWriter<String> {
    match ProductSkuService::publish(req.0).await {
        Ok(res) => AppWriter(Ok(res)),
        Err(e) => AppWriter(Err(e)),
    }
}
