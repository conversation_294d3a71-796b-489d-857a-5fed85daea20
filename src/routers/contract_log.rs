use crate::{
    app_writer::{App<PERSON><PERSON><PERSON>, AppWriter},
    db::{ListOptions, ListParams, ListResponse},
    dtos::contract_log::{ContractLogCreate, ContractLogResponse, ContractLogUpdate},
    services::contract_log::ContractLogService,
};
use salvo::{
    http::form::FilePart,
    oapi::{
        endpoint,
        extract::{JsonBody, PathParam},
    },
    Request,
};
use salvo::{Router, Writer};
use serde_json;

pub fn router() -> Router {
    Router::with_path("contract_log")
        .post(create_contract_log)
        .put(update_contract_log)
        .push(Router::with_path("list").post(get_contract_log_list))
        .push(
            Router::with_path("<id>")
                .get(get_contract_log_by_id)
                .delete(delete_contract_log),
        )
}

#[endpoint(tags("contract_log"))]
async fn get_contract_log_list(
    req: JsonBody<ListParams>,
) -> AppWriter<ListResponse<ContractLogResponse>> {
    let list = ContractLogService::get_list(req.0.clone()).await.unwrap();

    let mut data: Vec<ContractLogResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        data.push(tmp);
    }
    let page = req.0.page.clone().unwrap();
    let total = ContractLogService::get_total(req.params.clone())
        .await
        .unwrap();
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };

    AppWriter(Ok(res))
}

#[endpoint(tags("contract_log"), parameters(("id", description = "contract_log id for params")))]
async fn get_contract_log_by_id(id: PathParam<String>) -> AppWriter<ContractLogResponse> {
    match ContractLogService::get_by_id(id.0).await {
        Ok(contract_log) => {
            let res = contract_log.response();
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("contract_log"))]
async fn create_contract_log(req: &mut Request) -> AppWriter<String> {
    // 解析 form-data
    let form_data = match req.form_data().await {
        Ok(form) => form,
        Err(e) => {
            return AppWriter(Err(anyhow::anyhow!("解析表单数据失败: {}", e).into()));
        }
    };

    // 提取文件（可选）
    let file: Option<FilePart> = form_data.files.get("file").cloned();

    // 提取并解析 objectData
    let object_data_str = match form_data.fields.get("objectData") {
        Some(value) => value,
        None => {
            return AppWriter(Err(anyhow::anyhow!("缺少 objectData 字段").into()));
        }
    };

    // 解析 JSON 字符串为 ContractLogCreate
    let contract_log_data: ContractLogCreate = match serde_json::from_str(object_data_str) {
        Ok(data) => data,
        Err(e) => {
            return AppWriter(Err(
                anyhow::anyhow!("解析 objectData JSON 失败: {}", e).into()
            ));
        }
    };

    // 调用服务层创建合同日志
    let result = ContractLogService::create(file, contract_log_data).await;
    AppWriter(result)
}

#[endpoint(tags("contract_log"))]
async fn update_contract_log(req: JsonBody<ContractLogUpdate>) -> AppResult<AppWriter<String>> {
    let result = ContractLogService::update(req.0).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("contract_log"), parameters(("id", description = "contract_log id")))]
async fn delete_contract_log(id: PathParam<String>) -> AppWriter<String> {
    let result = ContractLogService::delete(id.0).await;
    AppWriter(result)
}
