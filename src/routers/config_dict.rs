use crate::{
    app_writer::{AppR<PERSON>ult, AppWriter},
    db::{ListParams, ListResponse, Page},
    dtos::config_dict::{ConfigDictCreate, ConfigDictResponse, ConfigDictUpdate},
    services::config_dict::ConfigDictService,
};
use salvo::{
    oapi::{
        endpoint,
        extract::{JsonBody, PathParam},
    },
    Depot,
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("config_dict")
        .post(create_config_dict)
        .put(update_config_dict)
        .push(
            Router::with_path("<id>")
                .get(get_config_dict_by_id)
                .delete(delete_config_dict),
        )
        .push(Router::with_path("list").post(get_config_dict_list))
}

#[endpoint(tags("config_dict"))]
async fn get_config_dict_list(
    req: JsonBody<ListParams>,
) -> AppWriter<ListResponse<ConfigDictResponse>> {
    let page_params = req.0.page.clone();
    let mut page = Page::default();
    if page_params.is_some() {
        page = page_params.unwrap();
    };
    let list = ConfigDictService::get_list(req.0.clone()).await.unwrap();
    let mut data: Vec<ConfigDictResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        data.push(tmp);
    }
    let total = ConfigDictService::get_total(req.0.params).await.unwrap();
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };

    AppWriter(Ok(res))
}

#[endpoint(tags("config_dict"), parameters(("id", description = "config_dict id for params")))]
async fn get_config_dict_by_id(id: PathParam<String>) -> AppWriter<ConfigDictResponse> {
    match ConfigDictService::get_by_id(id.0).await {
        Ok(config_dict) => {
            let res = config_dict.response();
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("config_dict"))]
async fn create_config_dict(req: JsonBody<ConfigDictCreate>) -> AppWriter<String> {
    let result = ConfigDictService::create(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("config_dict"))]
async fn update_config_dict(req: JsonBody<ConfigDictUpdate>) -> AppResult<AppWriter<String>> {
    let result = ConfigDictService::update(req.0).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("config_dict"), parameters(("id", description = "user id")))]
async fn delete_config_dict(id: PathParam<String>) -> AppWriter<String> {
    let result = ConfigDictService::delete(id.0).await;
    AppWriter(result)
}
