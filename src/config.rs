use once_cell::sync::Lazy;
use serde::Deserialize;
use std::{fs::File, io::Read, path::Path};

#[derive(Debug, Deserialize)]
pub struct Configs {
    pub server: Server,
    pub log: Log,
    pub database: DataBase,
    pub cert: Cert,
    pub jwt: Jwt,
    /// redis路径配置
    pub redis: Redis,
    /// 图片上传压缩配置
    pub image: Image,
    /// 验证码配置
    pub captcha: Captcha,
    /// oss对应配置
    pub oss: Oss,
}

#[derive(Debug, Deserialize)]
pub struct Server {
    pub name: String,
    pub address: String,
    pub cors_allow_origin: Vec<String>,
    pub ssl: bool,
}

#[derive(Debug, Deserialize)]
pub struct DataBase {
    pub url: String,
    pub port: String,
    pub username: String,
    pub password: String,
    pub db_ns: String,
    pub db_name: String,
}

#[derive(Debug, Deserialize)]
pub struct Log {
    pub filter_level: String,
    pub with_ansi: bool,
    pub to_stdout: bool,
    pub directory: String,
    pub file_name: String,
    pub rolling: String,
}

#[derive(Debug, Deserialize)]
pub struct Jwt {
    pub jwt_secret: String,
    pub jwt_exp: i64,
    pub jwt_buf: i64,
}

#[derive(Debug, Deserialize)]
pub struct Cert {
    /// cert
    pub cert: String,
    /// key
    pub key: String,
}

#[derive(Debug, Deserialize)]
pub struct Redis {
    /// server address
    pub server: String,

    /// server port
    pub port: u16,
}

/// 图片配置
#[derive(Debug, Deserialize)]
pub struct Image {
    pub image_cut: bool,
    pub image_width: u16,
    pub image_height: u16,
}

/// 图片配置
#[derive(Debug, Deserialize)]
pub struct Captcha {
    pub image_height: u32,
    pub image_width: u32,
    pub text_length: u32,
}

/// 服务器OSS配置
#[derive(Debug, Deserialize)]
pub struct Oss {
    pub oss_type: String,
    pub endpoint: String,
    pub secret_id: String,
    pub secret_key: String,
    pub region: String,
    pub bucket_name: String,
    pub thumb_ext: String,
}

const CONFIG_FILE: &str = "config/config.yml";

pub static CFG: Lazy<Configs> = Lazy::new(self::Configs::init);

impl Configs {
    pub fn init() -> Self {
        let mut file = match File::open(CONFIG_FILE) {
            Ok(f) => f,
            Err(e) => panic!("配置文件不存在:{},错误信息:{}", CONFIG_FILE, e),
        };
        let mut cfg_contents = String::new();
        match file.read_to_string(&mut cfg_contents) {
            Ok(s) => s,
            Err(e) => panic!("读取配置文件失败,错误信息:{}", e),
        };
        match serde_yaml::from_str(&cfg_contents) {
            Ok(c) => c,
            Err(e) => panic!("解析配置文件失败,错误信息:{}", e),
        }
    }
}
pub static CERT_KEY: Lazy<CertKey> = Lazy::new(get_cert_key);

pub struct CertKey {
    pub cert: Vec<u8>,
    pub key: Vec<u8>,
}

impl CertKey {
    pub fn new(cert: Vec<u8>, key: Vec<u8>) -> Self {
        Self { cert, key }
    }
}
fn get_cert_key() -> CertKey {
    let cert = get_string(&CFG.cert.cert);
    let key = get_string(&CFG.cert.key);
    CertKey::new(cert, key)
}

fn get_string<P: AsRef<Path>>(path: P) -> Vec<u8> {
    std::fs::read(path).expect("读取文件失败")
}

// 初始化数据配置
#[derive(Debug, Deserialize)]
pub struct PermissionConfig {
    pub permissions: Vec<PermissionItem>,
}

#[derive(Debug, Deserialize)]
pub struct PermissionItem {
    pub name: String,
    pub method: String,
    pub path: String,
    pub group: String,
}

#[derive(Debug, Deserialize)]
pub struct MenuConfig {
    pub menus: Vec<MenuItem>,
}

#[derive(Debug, Deserialize)]
pub struct MenuItem {
    pub name: String,
    pub order: i32,
    pub path: String,
    pub component: String,
    pub redirect: String,
    pub active: String,
    pub title: String,
    pub icon: String,
    pub keep_alive: String,
    pub hidden: String,
    pub is_link: String,
    pub parent: String,
    pub remark: String,
}

const PERMISSIONS_FILE: &str = "config/permissions.yml";
const MENUS_FILE: &str = "config/menus.yml";

pub static PERMISSION_CONFIG: Lazy<PermissionConfig> = Lazy::new(load_permissions);
pub static MENU_CONFIG: Lazy<MenuConfig> = Lazy::new(load_menus);

fn load_permissions() -> PermissionConfig {
    let mut file = match File::open(PERMISSIONS_FILE) {
        Ok(f) => f,
        Err(e) => panic!("权限配置文件不存在:{},错误信息:{}", PERMISSIONS_FILE, e),
    };
    let mut contents = String::new();
    match file.read_to_string(&mut contents) {
        Ok(_) => {}
        Err(e) => panic!("读取权限配置文件失败,错误信息:{}", e),
    };
    match serde_yaml::from_str(&contents) {
        Ok(c) => c,
        Err(e) => panic!("解析权限配置文件失败,错误信息:{}", e),
    }
}

fn load_menus() -> MenuConfig {
    let mut file = match File::open(MENUS_FILE) {
        Ok(f) => f,
        Err(e) => panic!("菜单配置文件不存在:{},错误信息:{}", MENUS_FILE, e),
    };
    let mut contents = String::new();
    match file.read_to_string(&mut contents) {
        Ok(_) => {}
        Err(e) => panic!("读取菜单配置文件失败,错误信息:{}", e),
    };
    match serde_yaml::from_str(&contents) {
        Ok(c) => c,
        Err(e) => panic!("解析菜单配置文件失败,错误信息:{}", e),
    }
}
