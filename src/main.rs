use payermax::Config;
use payermax::service::{ImportService, BatchImportService};
use payermax::handler::{upload_file, start_import, get_import_status, list_imports, batch_import_data, upload_and_import};
use salvo::prelude::*;
use sea_orm::Database;
use tracing_subscriber;


#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt::init();

    // Load configuration
    dotenvy::dotenv().ok();
    let config = Config::from_env()?;

    // Connect to database
    let db = Database::connect(&config.database_url).await?;

    // Create services
    let import_service = ImportService::new(db.clone());
    let batch_import_service = BatchImportService::new(db.clone());

    // Create router
    let router = Router::new()
        .hoop(affix::inject(db))
        .push(
            Router::with_path("/api/v1")
                .push(
                    Router::with_path("/upload")
                        .post(upload_file)
                )
                .push(
                    Router::with_path("/import")
                        .post(start_import)
                        .push(
                            Router::with_path("/<id>")
                                .get(get_import_status)
                        )
                        .push(
                            Router::with_path("/list")
                                .get(list_imports)
                        )
                )
                .push(
                    Router::with_path("/batch")
                        .push(
                            Router::with_path("/import")
                                .post(batch_import_data)
                        )
                        .push(
                            Router::with_path("/upload")
                                .post(upload_and_import)
                        )
                )
        )
        .push(
            Router::with_path("/health")
                .get(health_check)
        );

    // Create service
    let service = Service::new(router);

    let acceptor = TcpListener::new(format!("{}:{}", config.server_host, config.server_port))
        .bind()
        .await;

    println!("Server running on http://{}:{}", config.server_host, config.server_port);
    println!("API endpoints:");
    println!("  POST /api/v1/upload - Upload file");
    println!("  POST /api/v1/import - Start import");
    println!("  GET  /api/v1/import/<id> - Get import status");
    println!("  GET  /api/v1/import/list - List all imports");
    println!("  POST /api/v1/batch/import - Batch import from data directory");
    println!("  POST /api/v1/batch/upload - Upload and import file");
    println!("  GET  /health - Health check");

    Server::new(acceptor).serve(service).await;

    Ok(())
}

#[handler]
async fn health_check(res: &mut Response) {
    res.render(Json(serde_json::json!({
        "status": "ok",
        "message": "PayerMax API is running"
    })));
}


