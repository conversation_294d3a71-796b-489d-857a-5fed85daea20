use chrono::Utc;
use entity::import_record::Entity as ImportRecord;
use entity::sum_transactions::{
    ActiveModel as SumTransactionsActiveModel, Entity as SumTransactions,
};
use entity::transactions::Entity as Transactions;
use payermax::config::Config;
use payermax::service::file_parser::FileParser;
use rust_decimal::Decimal;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, Database, EntityTrait, QueryFilter, QuerySelect, Set,
};
use std::collections::HashMap;
use std::path::Path;
use tracing::{error, info, warn};

#[derive(Debug)]
struct SummaryData {
    request_time: String,
    account_subject: String,
    account_type: String,
    account_currency: String,
    accounting_amount_in: Decimal,
    accounting_amount_out: Decimal,
    balance_origin: Decimal,
    qty_in: i32,
    qty_out: i32,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // 加载配置
    dotenvy::dotenv().ok();
    let config = Config::from_env()?;

    // 连接数据库
    let db = Database::connect(&config.database_url).await?;
    info!("Connected to database");

    println!("📊 PayerMax 汇总统计生成工具");
    println!("==============================");

    // 1. 获取所有已导入的文件和其记录数
    let transactions_stats = Transactions::find()
        .select_only()
        .column_as(entity::transactions::Column::Filename, "filename")
        .column_as(entity::transactions::Column::Id.count(), "count")
        .group_by(entity::transactions::Column::Filename)
        .into_tuple::<(String, i64)>()
        .all(&db)
        .await?;

    // 2. 获取所有 import_record 记录
    let import_records = ImportRecord::find().all(&db).await?;
    let import_record_map: HashMap<String, &entity::import_record::Model> = import_records
        .iter()
        .map(|record| (record.file_name.clone(), record))
        .collect();

    // 3. 找出完整导入的文件
    let mut complete_files = Vec::new();
    let mut incomplete_files = Vec::new();

    for (filename, actual_count) in &transactions_stats {
        if let Some(import_record) = import_record_map.get(filename.as_str()) {
            // 检查文件是否完整导入
            let expected_count = import_record.total_rows.unwrap_or(0) as i64;

            if *actual_count == expected_count && import_record.status == "completed" {
                complete_files.push(filename.clone());
                info!("✅ 完整文件: {} ({} 条记录)", filename, actual_count);
            } else {
                incomplete_files.push((filename.clone(), *actual_count, expected_count));
                warn!(
                    "⚠️  不完整文件: {} (实际: {}, 预期: {})",
                    filename, actual_count, expected_count
                );
            }
        } else {
            // 没有 import_record，尝试解析原始文件验证
            let file_path = Path::new("data").join(filename);
            if file_path.exists() {
                match FileParser::parse_file(&file_path) {
                    Ok(parse_result) => {
                        let expected_count = parse_result.rows.len() as i64;
                        if *actual_count == expected_count {
                            complete_files.push(filename.clone());
                            info!(
                                "✅ 完整文件 (通过文件验证): {} ({} 条记录)",
                                filename, actual_count
                            );
                        } else {
                            incomplete_files.push((
                                filename.clone(),
                                *actual_count,
                                expected_count,
                            ));
                            warn!(
                                "⚠️  不完整文件: {} (实际: {}, 文件: {})",
                                filename, actual_count, expected_count
                            );
                        }
                    }
                    Err(e) => {
                        warn!("❌ 无法验证文件 {}: {}", filename, e);
                        incomplete_files.push((filename.clone(), *actual_count, 0));
                    }
                }
            } else {
                warn!("❌ 原始文件不存在: {}", filename);
                incomplete_files.push((filename.clone(), *actual_count, 0));
            }
        }
    }

    println!("\n📈 文件统计:");
    println!("完整文件数: {}", complete_files.len());
    println!("不完整文件数: {}", incomplete_files.len());

    if !incomplete_files.is_empty() {
        println!("\n⚠️  不完整文件列表:");
        for (filename, actual, expected) in &incomplete_files {
            println!("  - {}: 实际 {} / 预期 {}", filename, actual, expected);
        }
    }

    // 4. 检查哪些文件已经生成过汇总
    let existing_summaries = SumTransactions::find()
        .select_only()
        .column(entity::sum_transactions::Column::File)
        .distinct()
        .into_tuple::<String>()
        .all(&db)
        .await?;

    let existing_set: std::collections::HashSet<String> = existing_summaries.into_iter().collect();
    let files_to_process: Vec<String> = complete_files
        .into_iter()
        .filter(|filename| !existing_set.contains(filename))
        .collect();

    println!("\n🔄 需要处理的文件:");
    if files_to_process.is_empty() {
        println!("  无需处理的新文件");
        return Ok(());
    }

    for filename in &files_to_process {
        println!("  - {}", filename);
    }

    // 5. 处理每个完整的文件
    let mut total_summaries_created = 0;

    for filename in &files_to_process {
        info!("处理文件: {}", filename);

        match process_file_summary(&db, filename).await {
            Ok(count) => {
                total_summaries_created += count;
                info!("✅ 文件 {} 生成了 {} 条汇总记录", filename, count);
            }
            Err(e) => {
                error!("❌ 处理文件 {} 失败: {}", filename, e);
            }
        }
    }

    println!("\n🎉 汇总统计完成!");
    println!("总共生成了 {} 条汇总记录", total_summaries_created);

    Ok(())
}

async fn process_file_summary(
    db: &sea_orm::DatabaseConnection,
    filename: &str,
) -> Result<usize, Box<dyn std::error::Error>> {
    // 查询该文件的所有交易记录
    let transactions = Transactions::find()
        .filter(entity::transactions::Column::Filename.eq(filename))
        .all(db)
        .await?;

    if transactions.is_empty() {
        return Ok(0);
    }

    // 按 request_time(yyyyMM), account_subject, account_type, account_currency 分组
    let mut groups: HashMap<(String, String, String, String), Vec<&entity::transactions::Model>> =
        HashMap::new();

    for transaction in &transactions {
        // 转换 request_time 为 yyyyMM 格式
        let request_time_yyyymm = format_request_time(&transaction.request_time);

        let key = (
            request_time_yyyymm,
            transaction.account_subject.clone(),
            transaction.accounting_type.clone(),
            transaction.accounting_currency.clone(),
        );

        groups.entry(key).or_insert_with(Vec::new).push(transaction);
    }

    // 为每个分组生成汇总记录
    let mut summaries = Vec::new();

    for ((request_time, account_subject, account_type, account_currency), group_transactions) in
        groups
    {
        let mut accounting_amount_in = Decimal::new(0, 0);
        let mut accounting_amount_out = Decimal::new(0, 0);
        let mut qty_in = 0i32;
        let mut qty_out = 0i32;
        let mut balance_origin = Decimal::new(0, 0);

        // 按 request_time 排序，取最后一条记录的 balance 作为 balance_origin
        let mut sorted_transactions = group_transactions.clone();
        sorted_transactions.sort_by(|a, b| a.request_time.cmp(&b.request_time));

        if let Some(last_transaction) = sorted_transactions.last() {
            balance_origin = last_transaction.balance.unwrap_or(Decimal::new(0, 0));
        }

        // 统计进出金额和数量
        for transaction in &group_transactions {
            if transaction.accounting_amount > Decimal::new(0, 0) {
                accounting_amount_in += transaction.accounting_amount;
                qty_in += 1;
            } else if transaction.accounting_amount < Decimal::new(0, 0) {
                accounting_amount_out += transaction.accounting_amount.abs();
                qty_out += 1;
            }
        }

        let summary = SummaryData {
            request_time,
            account_subject,
            account_type,
            account_currency,
            accounting_amount_in,
            accounting_amount_out,
            balance_origin,
            qty_in,
            qty_out,
        };

        summaries.push(summary);
    }

    // 智能插入或更新汇总记录
    let mut inserted_count = 0;
    let mut updated_count = 0;

    for summary in summaries {
        let balance_count =
            summary.balance_origin - summary.accounting_amount_in - summary.accounting_amount_out;

        // 检查是否已存在相同的记录
        let existing_record = SumTransactions::find()
            .filter(entity::sum_transactions::Column::File.eq(filename))
            .filter(entity::sum_transactions::Column::RequestTime.eq(&summary.request_time))
            .filter(entity::sum_transactions::Column::AccountSubject.eq(&summary.account_subject))
            .filter(entity::sum_transactions::Column::AccountCurrency.eq(&summary.account_currency))
            .one(db)
            .await?;

        if let Some(existing) = existing_record {
            // 记录已存在，检查是否需要更新
            let needs_update = existing.account_type != summary.account_type
                || existing.balance_count.unwrap_or_default() != balance_count
                || existing.accounting_amount_in.unwrap_or_default()
                    != summary.accounting_amount_in
                || existing.accounting_amount_out.unwrap_or_default()
                    != summary.accounting_amount_out
                || existing.balance_origin.unwrap_or_default() != summary.balance_origin
                || existing.qty_in.unwrap_or_default() != summary.qty_in
                || existing.qty_out.unwrap_or_default() != summary.qty_out;

            if needs_update {
                // 更新现有记录
                let mut active_model: SumTransactionsActiveModel = existing.into();
                active_model.account_type = Set(summary.account_type);
                active_model.balance_count = Set(Some(balance_count));
                active_model.accounting_amount_in = Set(Some(summary.accounting_amount_in));
                active_model.accounting_amount_out = Set(Some(summary.accounting_amount_out));
                active_model.balance_origin = Set(Some(summary.balance_origin));
                active_model.qty_in = Set(Some(summary.qty_in));
                active_model.qty_out = Set(Some(summary.qty_out));
                active_model.updated_at = Set(Some(Utc::now().into()));

                match active_model.update(db).await {
                    Ok(_) => {
                        updated_count += 1;
                        info!(
                            "更新汇总记录: {} - {} - {} - {}",
                            filename,
                            summary.request_time,
                            summary.account_subject,
                            summary.account_currency
                        );
                    }
                    Err(e) => error!("更新汇总记录失败: {}", e),
                }
            } else {
                info!(
                    "汇总记录无变化，跳过: {} - {} - {} - {}",
                    filename,
                    summary.request_time,
                    summary.account_subject,
                    summary.account_currency
                );
            }
        } else {
            // 记录不存在，插入新记录
            let active_model = SumTransactionsActiveModel {
                id: sea_orm::NotSet,
                file: Set(filename.to_string()),
                request_time: Set(summary.request_time.clone()),
                account_subject: Set(summary.account_subject.clone()),
                account_type: Set(summary.account_type.clone()),
                account_currency: Set(summary.account_currency.clone()),
                balance_count: Set(Some(balance_count)),
                accounting_amount_in: Set(Some(summary.accounting_amount_in)),
                accounting_amount_out: Set(Some(summary.accounting_amount_out)),
                balance_origin: Set(Some(summary.balance_origin)),
                qty_in: Set(Some(summary.qty_in)),
                qty_out: Set(Some(summary.qty_out)),
                created_at: Set(Some(Utc::now().into())),
                updated_at: Set(Some(Utc::now().into())),
            };

            match active_model.insert(db).await {
                Ok(_) => {
                    inserted_count += 1;
                    info!(
                        "插入新汇总记录: {} - {} - {} - {}",
                        filename,
                        summary.request_time,
                        summary.account_subject,
                        summary.account_currency
                    );
                }
                Err(e) => error!("插入汇总记录失败: {}", e),
            }
        }
    }

    info!(
        "文件 {} 处理完成: 插入 {} 条，更新 {} 条",
        filename, inserted_count, updated_count
    );
    Ok(inserted_count + updated_count)
}

fn format_request_time(request_time: &chrono::DateTime<chrono::FixedOffset>) -> String {
    request_time.format("%Y%m").to_string()
}
