use payermax::config::Config;
use payermax::service::batch_import_service::BatchImportService;
use sea_orm::Database;
use std::env;
use std::path::Path;
use std::time::Instant;
use tracing::{error, info};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // 加载配置
    dotenvy::dotenv().ok();
    let config = Config::from_env()?;

    // 连接数据库
    let db = Database::connect(&config.database_url).await?;
    info!("Connected to database");

    // 解析命令行参数
    let args: Vec<String> = env::args().collect();
    if args.len() < 2 {
        eprintln!("Usage: {} <data_directory> [max_concurrent_files] [batch_size]", args[0]);
        eprintln!("Example: {} data 2 500", args[0]);
        eprintln!("Note: Conservative mode uses smaller defaults for stability");
        std::process::exit(1);
    }

    let data_directory = Path::new(&args[1]);
    
    // 保守的默认值，适合大量文件处理
    let max_concurrent_files = args.get(2)
        .and_then(|s| s.parse().ok())
        .unwrap_or(2); // 默认只用2个并发，更稳定
    
    let batch_size = args.get(3)
        .and_then(|s| s.parse().ok())
        .unwrap_or(500); // 默认批次大小500，减少内存压力

    info!("=== Conservative Batch Import Mode ===");
    info!("Data directory: {:?}", data_directory);
    info!("Max concurrent files: {} (conservative)", max_concurrent_files);
    info!("Batch size: {} (conservative)", batch_size);
    info!("This mode is optimized for large datasets with memory constraints");

    if !data_directory.exists() {
        error!("Data directory does not exist: {:?}", data_directory);
        std::process::exit(1);
    }

    // 创建批量导入服务
    let import_service = BatchImportService::new(db, max_concurrent_files, batch_size);

    let start_time = Instant::now();
    
    // 执行导入
    match import_service.import_data_directory(data_directory).await {
        Ok(progress) => {
            let duration = start_time.elapsed();
            
            info!("=== Import Completed Successfully ===");
            info!("Total files: {}", progress.total_files);
            info!("Processed files: {}", progress.processed_files);
            info!("Total records processed: {}", progress.processed_records);
            info!("Failed files: {}", progress.failed_files.len());
            info!("Duration: {:?}", duration);
            
            if !progress.failed_files.is_empty() {
                info!("Failed files:");
                for failed_file in &progress.failed_files {
                    error!("  - {}", failed_file);
                }
            }
            
            // 计算性能指标
            let files_per_second = progress.processed_files as f64 / duration.as_secs_f64();
            let records_per_second = progress.processed_records as f64 / duration.as_secs_f64();
            
            info!("Performance:");
            info!("  - Files per second: {:.2}", files_per_second);
            info!("  - Records per second: {:.0}", records_per_second);
            
            if progress.failed_files.is_empty() {
                info!("🎉 All files imported successfully!");
            } else {
                info!("⚠️  Import completed with {} failed files", progress.failed_files.len());
            }
        }
        Err(e) => {
            error!("Import failed: {:?}", e);
            std::process::exit(1);
        }
    }

    Ok(())
}
