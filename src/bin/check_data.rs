use chrono::Utc;
use entity::import_record::{ActiveModel as ImportRecordActiveModel, Entity as ImportRecord};
use entity::transactions::Entity as Transactions;
use payermax::config::Config;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, ConnectionTrait, Database, EntityTrait, QueryFilter,
    QuerySelect, Set,
};
use std::path::Path;
use uuid::Uuid;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 加载配置
    dotenvy::dotenv().ok();
    let config = Config::from_env()?;

    // 连接数据库
    let db = Database::connect(&config.database_url).await?;
    println!("Connected to database");

    // 查询每个文件的记录数
    let results = Transactions::find()
        .select_only()
        .column_as(entity::transactions::Column::Filename, "filename")
        .column_as(entity::transactions::Column::Id.count(), "count")
        .group_by(entity::transactions::Column::Filename)
        .into_tuple::<(String, i64)>()
        .all(&db)
        .await?;

    println!("\n=== 导入结果统计 ===");
    let mut total_records = 0i64;
    for (filename, count) in &results {
        println!("文件: {} - 记录数: {}", filename, count);
        total_records += count;
    }

    println!("\n总记录数: {}", total_records);
    println!("总文件数: {}", results.len());

    // 查询分区信息
    let partition_query = r#"
        SELECT schemaname, tablename 
        FROM pg_tables 
        WHERE tablename LIKE 'transactions_%' 
        ORDER BY tablename;
    "#;

    let partitions = db
        .query_all(sea_orm::Statement::from_string(
            sea_orm::DatabaseBackend::Postgres,
            partition_query.to_string(),
        ))
        .await?;

    println!("\n=== 分区表信息 ===");
    for partition in partitions {
        let table_name: String = partition.try_get("", "tablename")?;
        println!("分区表: {}", table_name);
    }

    // 创建 import_record 记录
    println!("\n=== 创建 Import Record 记录 ===");
    let mut created_records = 0;
    let mut existing_records = 0;

    for (filename, count) in &results {
        // 检查是否已存在 import_record
        let existing = ImportRecord::find()
            .filter(entity::import_record::Column::FileName.eq(filename))
            .one(&db)
            .await?;

        if existing.is_some() {
            println!("Import record already exists for: {}", filename);
            existing_records += 1;
            continue;
        }

        // 获取文件信息
        let file_path = Path::new("data").join(filename);
        let file_size = if file_path.exists() {
            std::fs::metadata(&file_path)?.len() as i64
        } else {
            0 // 如果文件不存在，设为0
        };

        let file_extension = file_path
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("unknown");

        // 创建 import_record
        let import_record = ImportRecordActiveModel {
            id: Set(Uuid::new_v4()),
            file_name: Set(filename.clone()),
            file_type: Set(file_extension.to_string()),
            file_size: Set(file_size),
            status: Set("completed".to_string()),
            total_rows: Set(Some(*count as i32)),
            processed_rows: Set(Some(*count as i32)),
            error_rows: Set(Some(0)),
            error_message: Set(None),
            created_at: Set(Utc::now().into()),
            updated_at: Set(Utc::now().into()),
        };

        match import_record.insert(&db).await {
            Ok(_) => {
                println!(
                    "✓ Created import record for: {} ({} records)",
                    filename, count
                );
                created_records += 1;
            }
            Err(e) => {
                println!("✗ Failed to create import record for {}: {}", filename, e);
            }
        }
    }

    println!("\n=== Import Record 创建结果 ===");
    println!("新创建记录: {}", created_records);
    println!("已存在记录: {}", existing_records);
    println!("总文件数: {}", results.len());

    if created_records > 0 {
        println!(
            "🎉 成功为 {} 个已导入文件创建了 import_record 记录！",
            created_records
        );
    }

    Ok(())
}
