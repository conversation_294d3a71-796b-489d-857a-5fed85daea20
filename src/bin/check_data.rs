use entity::transactions::Entity as Transactions;
use payermax::config::Config;
use sea_orm::{ColumnTrait, ConnectionTrait, Database, EntityTrait, QuerySelect};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 加载配置
    dotenvy::dotenv().ok();
    let config = Config::from_env()?;

    // 连接数据库
    let db = Database::connect(&config.database_url).await?;
    println!("Connected to database");

    // 查询每个文件的记录数
    let results = Transactions::find()
        .select_only()
        .column_as(entity::transactions::Column::Filename, "filename")
        .column_as(entity::transactions::Column::Id.count(), "count")
        .group_by(entity::transactions::Column::Filename)
        .into_tuple::<(String, i64)>()
        .all(&db)
        .await?;

    println!("\n=== 导入结果统计 ===");
    let mut total_records = 0i64;
    for (filename, count) in &results {
        println!("文件: {} - 记录数: {}", filename, count);
        total_records += count;
    }

    println!("\n总记录数: {}", total_records);
    println!("总文件数: {}", results.len());

    // 查询分区信息
    let partition_query = r#"
        SELECT schemaname, tablename 
        FROM pg_tables 
        WHERE tablename LIKE 'transactions_%' 
        ORDER BY tablename;
    "#;

    let partitions = db
        .query_all(sea_orm::Statement::from_string(
            sea_orm::DatabaseBackend::Postgres,
            partition_query.to_string(),
        ))
        .await?;

    println!("\n=== 分区表信息 ===");
    for partition in partitions {
        let table_name: String = partition.try_get("", "tablename")?;
        println!("分区表: {}", table_name);
    }

    Ok(())
}
