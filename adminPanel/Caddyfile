test-admin.ferychen.icu {
    root * /opt/fund/adminPanel/dist/spa
    encode gzip

    # API 代理 - 必须在其他规则之前
    handle_path /api/* {
        reverse_proxy 127.0.0.1:6310
    }

    # 处理静态资源
    @static {
        path /assets/* /icons/* /img/* /resource/* /data/* /tinymce/* /favicon.ico
    }
    handle @static {
        file_server
    }

    # SPA 路由处理 - 所有其他请求返回 index.html
    handle {
        try_files {path} /index.html
        file_server
    }

    # 设置正确的 MIME 类型 - 放在最后
    header {
        # HTML 文件
        Content-Type "text/html; charset=utf-8" {
            path *.html
        }
        # CSS 文件
        Content-Type "text/css; charset=utf-8" {
            path *.css
        }
        # JS 文件
        Content-Type "application/javascript; charset=utf-8" {
            path *.js
        }
        # JSON 文件
        Content-Type "application/json; charset=utf-8" {
            path *.json
        }
        # 字体文件
        Content-Type "font/woff2" {
            path *.woff2
        }
        Content-Type "font/woff" {
            path *.woff
        }
        Content-Type "font/ttf" {
            path *.ttf
        }
    }
}
