.loading-container {
    position: fixed;
    left: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
}

.loading-svg {
    width: 60px;
    height: 60px;
    color: #646464;
}


.loading-title {
    font-size: 28px;
    color: #646464;
    font-family: '-apple-system', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.loader {
    font-size: 48px;
    color: #646464;
    display: inline-block;
    font-family: Arial, Helvetica, sans-serif;
    font-weight: 400;
    position: relative;
}

.loader:after {
    content: '';
    height: 4px;
    width: 0%;
    display: block;
    background: var(--primary-color);
    animation: 5s lineGrow linear infinite;
}

@keyframes lineGrow {
    to {
        width: 100%;
    }
}