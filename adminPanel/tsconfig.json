{"extends": "@quasar/app-vite/tsconfig-preset", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["unplugin-vue-define-options/macros-global", "vite/client", "quasar"], "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitThis": true, "alwaysStrict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"]}