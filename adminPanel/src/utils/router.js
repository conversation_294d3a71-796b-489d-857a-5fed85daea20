import { asyncRootRoute } from 'src/router/routes'
const pagesFile = import.meta.glob('../pages/**/*.vue')

/**
 * Recursively transforms menu data into routes with proper hierarchy
 * @param {Array} menuData - Array of menu items with name and parent fields
 * @param {string|null} parentName - Current parent name for recursion
 * @returns {Route[]} - Array of routes with nested children
 */
export const transformMenuToRoutes = (menuData) => {
  const routes = []

  for (const item of menuData) {
    let route = {
      name: item.name,
      path: item.path,
      fullPath: item.path,
      redirect: item.redirect,
      meta: {
        title: item.title,
        icon: item.icon,
        hidden: item.hidden,
        keepAlive: item.keep_alive,
        active: item.active,
      },
    }

    // Handle path based on conditions
    if (item.path !== '') {
      routes.push(route)
    } else {
      if (item.is_link === 'yes') {
        delete item.path
      }
    }
    // Recursively transform children
    if (item.children && item.children.length > 0) {
      route.children = transformMenuToRoutes(item.children)
    }

  }
  return routes
}

export const HandleRouter = (menuData) => {
  const result = []
  for (const item of menuData) {
    if (item.path !== '') {

      const obj = {
        path: item.path,
        fullPath: item.path,
        name: item.name,
        component: pageImporter(item.component),
        meta: {
          hidden: item.hidden,
          keepAlive: item.keep_alive,
          title: item.title,
          icon: item.icon,
          active: item.active,
        },
        redirect: item.redirect,
      }
      result.push(obj)
    }
    else {
      if (item.is_link === 'yes') {
        delete item.path
      }
    }
  }
  // 将需要鉴权路由的children（默认是空的）替换成后台传过来的整理后的路由表
  asyncRootRoute[0].children = [...result]
  // 返回鉴权路由并添加404路由
  return [...asyncRootRoute]
}

const pageImporter = (component) => {
  // Vite 版本：
  let fileKey = []
  let resultKey = ''
  if (component.split('/')[0] === 'pages') {
      fileKey = Object.keys(pagesFile)
      resultKey = getResultKey(fileKey, component)
      if (resultKey.length > 0) {
        return pagesFile[resultKey[0]]
      }
      console.error(`Component not found: ${component}`)
      return Promise.resolve()
  } else {
      return Promise.resolve()
  }
}

const getResultKey = (fileKey, component) => {
  const resultKey = fileKey.filter(key => {
      // 确保路径匹配时包含.vue后缀
      return key.replace('../', '') === `${component}.vue`
  })
  return resultKey
}
