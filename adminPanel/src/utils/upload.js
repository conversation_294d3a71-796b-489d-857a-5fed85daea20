import OSS from 'ali-oss'
import { LocalStorage } from 'quasar'
import { ref } from 'vue'
import { getAction } from 'src/api/manage'

const defaultUrl = 'system/upload/sts'
const getSuffix = (filename) => {
  const pos = filename.lastIndexOf('.')
  let suffix = ''
  if (pos !== -1) {
    suffix = filename.substring(pos)
  }
  return suffix
}

const randomString = (len) => {
  len = len || 32
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz123456789'
  const maxPos = chars.length
  let pwd = ''
  for (let i = 0; i < len; i++) {
    pwd += chars.charAt(Math.floor(Math.random() * maxPos))
  }
  return pwd
}

const uploadToken = ref()
export const ossUpload = async(url, file, dir, prefix) => {
  const getToken = LocalStorage.getItem('cms-upload')
  if (getToken === undefined) {
    uploadToken.value = JSON.parse(getToken)
  }
  else {
    const { code, data } = await getAction(url || defaultUrl)
    if (code === 200) {
      LocalStorage.set('cms-upload', JSON.stringify(data))
      uploadToken.value = data
    }
  }
  const timestamp = new Date().getTime()

  if (uploadToken.value && uploadToken.value.expiration - 3 < timestamp) {
    const { code, data } = await getAction(url)
    if (code === 200) {
      LocalStorage.set('cms-upload', JSON.stringify(data.data))
      uploadToken.value = data
    }
  }
  // key就代表文件层级和阿里云上的文件名
  const filename = file.name
  const picName = ref('')
  if (prefix) {
    picName.value = prefix + '-' + randomString(10) + getSuffix(filename)
  }
  else {
    picName.value = randomString(10) + getSuffix(filename)
  }
  const keyValue = dir + '/' + picName.value

  const client = new OSS({
    region: uploadToken.value.region,
    accessKeyId: uploadToken.value.accessKeyId,
    accessKeySecret: uploadToken.value.accessKeySecret,
    stsToken: uploadToken.value.secureToken,
    bucket: uploadToken.value.bucket,
    secure: true,
    endpoint: uploadToken.value.endpoint,
  })

  return await client.put(keyValue, file, {
    // 防止覆盖
    headers: { 'x-oss-forbid-overwrite': true },
    // 上传进度
    progress: (p, cpt, res) => {
      console.log(p * 100 + '%')
    },
  })

  // return await axios({
  //   url: uploadToken.value.host + '/' + keyValue, ossData,
  //   method: 'PUT',
  //   data: ossData,
  //   processData: false,
  //   cache: false,
  //   contentType: false,
  // })
}
