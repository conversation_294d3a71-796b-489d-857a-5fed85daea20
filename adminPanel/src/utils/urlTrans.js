export const transImageUrl = (item) => {
  if (item.substring(0, 4) === 'http') {
    // 非登录上传，头像为链接
    return item
  }
  else if (item.substring(0, 11) === 'sys-upload:') {
    // 非登录用户，头像为上传
    return process.env.API + item.substring(11)
  }
  else {
    return item
  }
}

export const transFileUrl = (item) => {
  if (item.substring(0, 4) === 'http' || item.substring(0, 5) === 'https') {
    // 非登录上传，文件为链接
    return item
  }
  else if (item.substring(0, 11) === 'sys-upload:') {
    // 非登录用户，文件为上传
    return process.env.API + item.substring(11)
  }
  else {
    return item
  }
}
