import { useQuasar } from 'quasar'
import { deleteActionByPath, postAction } from 'src/api/manage'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

export default function useTableData(url) {
  console.log('url', url)
  const { t } = useI18n()
  const $q = useQuasar()
  const loading = ref(false)
  const tableData = ref([])
  const recordDetailDialog = ref(null)

  // const pagination = ref({
  //   options: {
  //     order_by: 'id',
  //     desc  : false,
  //   },
  //   page: {
  //     page: 1,
  //     limit: 10,
  //   }
  // })

  const pagination = ref({
    sortBy: 'sort',
    descending: false,
    page: 1,
    rowsPerPage: 10,
  })
  const queryParams = ref({})
  const pageOptions = ref([10, 30, 50, 100])

  const showAddForm = () => {
    recordDetailDialog.value.formType = 'add'
    recordDetailDialog.value.show()
  }
  const showEditForm = (row) => {
    recordDetailDialog.value.formType = 'edit'
    recordDetailDialog.value.show(row)
  }
  const onRequest = async(props) => {
    if (url === undefined || !url.list) {
      $q.notify({
        type: 'negative',
        message: t('admin.UrlNotConfig'),
      })
      return
    }
    tableData.value = []
    loading.value = true
    // 组装分页和过滤条件
    const allParams = {
      options: {
        order_by: props.pagination.sortBy,
        desc  : props.pagination.descending,
      },
      page: {
        page: props.pagination.page,
        limit: props.pagination.rowsPerPage,
      },
    }
    if (queryParams.value && Object.keys(queryParams.value).length > 0) {
      console.log('queryParams.value', queryParams.value)
      allParams.params = Object.entries(queryParams.value).map(([key, value]) => ({
        var: key,
        val: value
      }))
    }
    // 带参数请求数据
    await postAction(url.list, allParams).then(res => {
      console.log('res', res)
      if (res.code === 200) {
        // 正确更新分页状态，避免引用问题
        pagination.value.sortBy = props.pagination.sortBy
        pagination.value.descending = props.pagination.descending
        pagination.value.page = props.pagination.page
        pagination.value.rowsPerPage = props.pagination.rowsPerPage
        // 并且加入总数字段
        pagination.value.rowsNumber = res.data.total
        tableData.value = res.data.data
      } else {
        // 请求失败时清空数据
        tableData.value = []
      }
    }).finally(() => {
      loading.value = false
    })
  }
  const getTableData = () => onRequest({ pagination: pagination.value, queryParams: queryParams.value })
  const handleSearch = () => {
    getTableData()
  }
  const resetSearch = () => {
    queryParams.value = {}
    // 重置搜索时回到第一页
    pagination.value.page = 1
    getTableData()
  }
  const handleFinish = () => {
    getTableData()
  }
  const handleDelete = (row) => {
    if (!url || !url.delete) {
      $q.notify({
        type: 'negative',
        message: '请先配置url',
      })
      return
    }
    $q.dialog({
      title: t('admin.Confirm'),
      message: t('admin.Confirm') + t('admin.Delete') + '?',
      cancel: true,
      persistent: true,
    }).onOk(async() => {
      // 此处是为了manage house hold 的salvo 路由做的特殊处理
      const res = await deleteActionByPath(url.delete, [row.id])
      if (res.code === 200) {
        $q.notify({
          type: 'positive',
          message: res.message,
        })
      }
      getTableData()
    })
  }
  return {
    loading,
    pagination,
    queryParams,
    pageOptions,
    tableData,
    recordDetailDialog,
    showAddForm,
    showEditForm,
    onRequest,
    getTableData,
    handleSearch,
    resetSearch,
    handleFinish,
    handleDelete,
  }
}
