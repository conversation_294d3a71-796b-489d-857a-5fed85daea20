<template>
  <q-card class="q-my-md">
    <q-card-section>
      <div class="row">
        <div class="text-h6 col">
          {{ t("Product") + t("Image") + t("List") }}
        </div>
        <div class="col-auto">
          <q-btn
            color="primary"
            size="sm"
            :label="t('admin.Add') + $t('Product') + $t('Image')"
            @click="showUploader"
          />
        </div>
      </div>
    </q-card-section>
    <q-card-section>
      <q-table
        grid
        row-key="id"
        :rows="itemDetail?.product_image"
        :hide-pagination="true"
      >
        <template #item="props">
          <div class="q-pa-xs col-xs-6 col-sm-4 col-md-2">
            <q-card>
              <q-card-section class="text-center">
                <ImagePreview
                  :src="props.row.url"
                  :height="'90'"
                  :max-width="'180'"
                  :preview="true"
                />
              </q-card-section>
              <q-card-section>
                <q-btn-group>
                  <q-btn
                    v-if="props.row.sort !== 999"
                    color="primary"
                    :label="$t('Set') + $t('Cover')"
                    size="xs"
                    @click="setCover(props.row)"
                  />
                  <q-btn
                    v-else
                    :label="$t('Cover') + $t('Image')"
                    size="xs"
                    :disable="true"
                    @click="setCover(props.row)"
                  />
                  <q-btn
                    color="negative"
                    :label="$t('Delete')"
                    size="xs"
                    @click="handleImageDelete(props.row)"
                  />
                </q-btn-group>
              </q-card-section>
            </q-card>
          </div>
        </template>
      </q-table>
    </q-card-section>
  </q-card>
</template>

<script setup>
import { computed, toRefs } from "vue";
const props = defineProps({
  url: {
    type: String,
    required: false,
    default: "",
  },
  fileType: {
    type: String,
    required: false,
    default: "",
  },
});
const { url, fileType } = toRefs(props);

const previewSrc = computed(() => {
  if (src.value.substring(0, 4) === "http") {
    // 非登录上传，头像为链接
    return src.value;
  } else if (src.value.substring(0, 11) === "sys-upload:") {
    // 非登录用户，头像为上传
    return process.env.API + src.value.substring(11);
  } else {
    return src.value;
  }
});
</script>
