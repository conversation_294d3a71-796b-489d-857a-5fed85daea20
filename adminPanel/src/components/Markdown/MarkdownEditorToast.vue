<template>
  <div :class="className" ref="editorElement" class="markdown-editor" />
</template>

<script lang="ts" setup>
import { Dark } from "quasar";
import { useVModel } from "@vueuse/core";

//import prism
import 'prismjs/themes/prism.css';
import Prism from 'prismjs';
import 'prismjs/components/prism-javascript.js';

//import editor
import '@toast-ui/editor/dist/toastui-editor.css'
import '@toast-ui/editor/dist/theme/toastui-editor-dark.css'
import Editor from '@toast-ui/editor';

import '@toast-ui/editor-plugin-code-syntax-highlight/dist/toastui-editor-plugin-code-syntax-highlight.css';
import codeSyntaxHighlight from '@toast-ui/editor-plugin-code-syntax-highlight';
import chart from '@toast-ui/editor-plugin-chart';
import tableMergedCell from '@toast-ui/editor-plugin-table-merged-cell';

import { onMounted, onUnmounted, ref, watch, computed } from "vue"

defineOptions({ name: "MarkdownEditorToast" });

interface Props {
  modelValue: string | null;
  previewOnly?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
  previewOnly: false,
});

const emit = defineEmits<{
  (e: "update:modelValue", content: string): void;
  (e: "onSave"): void;
}>();

const _modelValue = useVModel(props, "modelValue", emit);
const editorElement = ref<HTMLElement | null>(null)
const initialized = ref<boolean>(false)
const className = computed(() => { return `editor-panel-editor ${Dark.isActive ? " toastui-editor-dark" : ""} ` })

let editor: Editor;

const init = () => {
  editor = new Editor({
    el: editorElement.value as HTMLElement,
    initialEditType: 'wysiwyg',
    previewStyle: 'vertical',
    height: '100%',
    initialValue: props.modelValue || '',
    events: {
      change: () => {
        const content = editor.getMarkdown();
        if (content !== _modelValue.value) {
          _modelValue.value = content;
        }
      },
    },
    plugins: [
      [codeSyntaxHighlight, { highlighter: Prism }] as any,
      chart,
      tableMergedCell
    ]
  });
}

onMounted(() => {
  init();
  if (props.modelValue) {
    initialized.value = true;
  }
})

const stopWatch = watch(() => _modelValue.value, (newVal) => {
  if (!initialized.value && editor) {
    editor.setMarkdown(newVal || '');
    editor.moveCursorToStart();
    initialized.value = true;
  }
}, { immediate: true })

onUnmounted(() => {
  editor.destroy();
  stopWatch();
})

</script>

<style lang="scss">
.markdown-editor {
  height: 100%;
  width: 100%;

  :deep(.toastui-editor-defaultUI) {
    border: 1px solid #e4e4e4;
  }

  :deep(.toastui-editor-dark) {
    border-color: #4a4a4a;
  }
}
</style>
