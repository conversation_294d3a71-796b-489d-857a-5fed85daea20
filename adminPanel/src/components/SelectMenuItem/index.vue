<template>
  <div>
    <q-input
      v-model="returnItem" :label="label || t('content.Select') + t('content.Category') + t('content.Name')"
      :class="className"
      :input-style="{fontSize:' 16px',lineHeight: '28px',fontWeight: '400'}"
      readonly
    >
      <template #append>
        <q-btn
          dense color="primary"
          :label="t('Select')"
          @click="showSelectDialog"
        />
      </template>
    </q-input>
    <SelectDialog ref="selectItemDialog" :selection="selection" @handleSelect="handleSelect" />
  </div>
</template>

<script setup>
import { computed, ref, toRefs } from 'vue'
import SelectDialog from 'src/components/SelectMenuItem/SelectMenuItem.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
  selectItem: {
    type: [Object, Array],
    required: false,
  },
  selectItemName: {
    type: [String, Array],
    required: false,
  },
  selectItemId: {
    type: [Number, Array],
    required: false,
  },
  label: {
    type: String,
    required: false,
    default: function() {
      return '选择产品归属的系统目录'
    },
  },
  className: {
    type: String,
    required: false,
    default: '',
  },
  selection: {
    type: String,
    required: false,
    default: 'single',
  },
})

const { selectItem, selectItemName, selectItemId, label, className, selection } = toRefs(props)

const returnItem = computed(() => {
  if (selection === 'multiple') {
    let serial = ''
    let id = ''
    for (const u of selectItem.value) {
      if (u.serial) {
        serial += u.serial + ' '
      }
      else if (u.id) {
        id += u.id + ' '
      }
      else {
        return ''
      }
    }
    return serial || id
  }
  else {
    if (selectItem.value) {
      return selectItem.value.name
    }
    else {
      return '未选择产品目录'
    }
  }
})
const selectItemDialog = ref(null)
const showSelectDialog = () => {
  selectItemDialog.value.show(selectItem.value)
}

const emit = defineEmits(['update:selectItem', 'update:selectItemName', 'update:selectItemId'])

const handleSelect = (event) => {
  console.log(event)
  if (selection === 'multiple') {
    const itemList = []
    const itemNameList = []
    for (const i of event) {
      itemNameList.push(i.id)
      itemList.push(i.category_code)
    }
    emit('update:selectItem', event)
    emit('update:selectItemName', itemNameList)
    emit('update:selectItemId', itemList)
  }
  else {
    if (event.id) {
      emit('update:selectItem', event)
      emit('update:selectItemName', event.name)
      emit('update:selectItemId', event.id)
    }
    else {
      emit('update:selectItem', {})
      emit('update:selectItemName', '')
      emit('update:selectItemId', '')
    }
  }
}
</script>
