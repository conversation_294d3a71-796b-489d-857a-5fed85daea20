<template>
  <q-dialog v-model="selectItemVisible">
    <q-card style="min-width: 700px; max-width: 45vw">
      <q-card-section class="row justify-between items-center">
        <div class="text-subtitle1">
          <span>
            {{ title }}
            {{ selection === "multiple" ? $t("多选项目") : $t("单选项目") }}
          </span>
        </div>
        <q-btn dense color="primary" label="选择" @click="handleSelectItem()" />
      </q-card-section>

      <q-separator />

      <q-card-section class="items-center row q-gutter-md">
        <q-input
          v-model="queryParams.name"
          dense
          style="width: 20%"
          label="名称"
        />
        <q-btn dense color="primary" label="搜索" @click="handleSearch" />
        <q-btn dense color="primary" label="重置" @click="resetSearch" />
      </q-card-section>

      <q-table
        v-model:pagination="pagination"
        v-model:selected="selected"
        row-key="id"
        :rows="tableData"
        :columns="columns"
        :rows-per-page-options="pageOptions"
        :loading="loading"
        :selection="selection"
        @request="onRequest"
      >
        <template #body-cell-actions="props">
          <q-td :props="props">
            <div class="q-gutter-xs">
              <q-btn
                dense
                color="primary"
                label="选择"
                @click="handleSelectItem(props.row)"
              />
            </div>
          </q-td>
        </template>
      </q-table>
    </q-card>
  </q-dialog>
</template>

<script setup>
import useTableData from "src/composables/useTableData";
import { ArrayOrObject } from "src/utils/arrayOrObject";
import { computed, ref, toRefs } from "vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const props = defineProps({
  // 必须传递单选多选: multiple, single
  selection: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    required: false,
    default: "选择窗口",
  },
  urlList: {
    type: String,
    required: true,
  },
});
const { selection, title, urlList } = toRefs(props);
const columns = computed(() => {
  return [
    { name: "id", align: "center", label: "Id", field: (row) => row.id },
    {
      name: "name",
      align: "center",
      label: "名称",
      field: (row) => row.name,
    },
    {
      name: "serial",
      align: "center",
      label: "编号",
      field: (row) => row.serial,
    },
    {
      name: "type",
      align: "center",
      label: "操作",
      field: (row) => row.type,
    },
    { name: "actions", align: "center", label: "操作", field: "actions" },
  ];
});
const url = computed(() => {
  return {
    list: urlList.value,
  };
});
const {
  pagination,
  queryParams,
  pageOptions,
  loading,
  tableData,
  onRequest,
  getTableData,
  handleSearch,
  resetSearch,
} = useTableData(url.value);

const selectItemVisible = ref(false);
const selected = ref(null);

const show = (selectItem) => {
  console.log(url);
  selected.value = [];
  selectItemVisible.value = true;
  getTableData();
  if (selection.value === "multiple") {
    if (ArrayOrObject(selectItem) === "Array") {
      selected.value = selectItem;
    } else {
      selected.value = [];
    }
  } else {
    selected.value.push();
  }
};

defineExpose({
  show,
});
const emit = defineEmits(["handleSelectItem"]);
const handleSelectItem = (row) => {
  if (row.id) {
    emit("handleSelectItem", row);
    console.log(row);
  } else {
    emit("handleSelectItem", selected.value);
    console.log(selected);
  }
  selectItemVisible.value = false;
};
</script>
