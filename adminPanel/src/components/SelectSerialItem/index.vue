<template>
  <div>
    <q-input
      v-model="returnItem"
      outlined
      :label="label"
      :class="className"
      :input-style="{
        fontSize: ' 16px',
        lineHeight: '28px',
        fontWeight: '400',
      }"
      readonly
    >
      <template #append>
        <q-btn
          v-if="canEdit"
          dense
          color="primary"
          label="选择"
          @click="showSelectDialog"
        />
      </template>
    </q-input>
    <SelectDialog
      ref="selectItemDialog"
      :selection="selection"
      :url-list="url"
      @handleSelectItem="handleSelectItem"
    />
  </div>
</template>

<script setup>
import { computed, ref, toRefs } from "vue";
import SelectDialog from "./SelectItemDialog.vue";
const props = defineProps({
  selectItem: {
    type: [Object, Array],
    required: false,
  },
  label: {
    type: String,
    required: false,
    default: "选择数据项目",
  },
  url: {
    type: String,
    required: true,
  },
  className: {
    type: String,
    required: false,
    default: "",
  },
  canEdit: {
    type: Boolean,
    required: false,
    default: false,
  },
  selection: {
    type: String,
    required: false,
    default: "single",
  },
});

const { selectItem, label, className, selection, canEdit, url } = toRefs(props);

const returnItem = computed(() => {
  if (selection === "multiple") {
    let serial = "";
    let id = "";
    for (const u of selectItem.value) {
      if (u.serial) {
        serial += u.serial + " ";
      } else if (u.id) {
        id += u.id + " ";
      } else {
        return "";
      }
    }
    return serial || id;
  } else {
    if (selectItem.value?.name) {
      return selectItem.value.serial + "：" + selectItem.value.name;
    } else {
      return "未选择相关项目";
    }
  }
});
const selectItemDialog = ref(null);
const showSelectDialog = () => {
  selectItemDialog.value.show(selectItem.value);
};

const emit = defineEmits(["update:selectItem"]);

const handleSelectItem = (event) => {
  if (selection === "multiple") {
    emit("update:selectItem", event);
  } else {
    if (event.id) {
      console.log("get event", event);
      // SelectItemSerial.value = event[0].serial
      emit("update:selectItem", event);
    } else {
      emit("update:selectItem", {});
    }
  }
};
</script>
