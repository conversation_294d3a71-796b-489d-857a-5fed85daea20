<template>
  <div
    class="row"
    :style="{
      margin: !$q.screen.gt.sm ? '0px 3px 0px 3px' : '0px 15px 0px 5px',
    }"
  >
    <q-tabs
      class="tagViewBase col-12"
      align="left"
      active-color="white"
      active-class="tagActive"
      dense
      swipeable
      inline-label
      indicator-color="transparent"
      :breakpoint="0"
    >
      <q-route-tab :to="'/'" :class="tagViewClass('/')" flat dense no-caps>
        <q-icon size="1.1rem" name="home" />
        <div class="line-limit-length">{{ $t("router.Home") }}</div>
      </q-route-tab>
      <template
        v-for="(tag, i) in tagViewStore.tagView"
        :key="tag.fullPath + i"
      >
        <q-route-tab
          :to="tag.fullPath"
          :class="tagViewClass(tag.fullPath)"
          flat
          dense
          no-caps
        >
          <q-icon size="1.1rem" :name="tag.icon" />
          <div class="line-limit-length">{{ checkTitle(tag.title) }}</div>
          <q-btn
            class="tagView-remove-icon"
            style="display: inline-flex"
            round
            size="0.45em"
            flat
            icon="close"
            @click.prevent.stop="removetagViewAt(i)"
          />
          <q-menu touch-position context-menu>
            <q-list dense>
              <q-item clickable v-close-popup>
                <q-item-section @click="removetagViewOnRight(i)">
                  {{ $t("layout.Close") + $t("layout.Right") }}
                </q-item-section>
              </q-item>
              <q-item clickable v-close-popup>
                <q-item-section @click="removetagViewOnLeft(i)">
                  {{ $t("layout.Close") + $t("layout.Left") }}
                </q-item-section>
              </q-item>
              <q-item clickable v-close-popup>
                <q-item-section @click="removeOthertagView(i)">
                  {{ $t("layout.Close") + $t("layout.Other") }}
                </q-item-section>
              </q-item>
              <q-item clickable v-close-popup>
                <q-item-section @click="removeAllTagView()">
                  {{ $t("layout.Close") + $t("layout.All") }}
                </q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-route-tab>
      </template>
    </q-tabs>
  </div>
</template>

<script lang="js" setup>
import { storeToRefs } from "pinia";
import { SessionStorage } from "quasar";
import { useKeepAliveStore } from "src/stores/keep-alive";
import { useTagViewStore } from "src/stores/tagView";
import { useThemeStore } from "src/stores/theme";
import { computed, onUnmounted } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";

defineOptions({ name: "Tagview" });

const { t } = useI18n();
const route = useRoute();
const tagViewStore = useTagViewStore();
const keepAliveStore = useKeepAliveStore();
const themeStore = useThemeStore();

const { primaryColor, activeTextColor, activeBgColor } =
  storeToRefs(themeStore);

const checkTitle = (item) => {
  console.log("检查title:::::", item);
  const string = item.split("：");
  if (string.length < 2) {
    return t("router." + item);
  } else {
    let value = item.substring(string[0].length).split(":");
    if (value.length > 1) {
      return (
        t("router." + item.substring(0, string[0].length)) + "：" + value[1]
      );
    } else {
      return (
        t("router." + item.substring(0, string[0].length)) + "：" + value[0]
      );
    }
  }
};

const removeAllTagView = () => {
  tagViewStore.removeAllTagView();
};

const removetagViewAt = (i) => {
  tagViewStore.removeTagViewAt(i);
};

const removetagViewOnRight = (i) => {
  tagViewStore.removeTagViewOnRight(i);
};

const removetagViewOnLeft = (i) => {
  tagViewStore.removeTagViewOnLeft(i);
};

const removeOthertagView = (i) => {
  tagViewStore.removeOtherTagView(i);
};

const tagViewClass = computed(() => {
  //是否為當前路由
  return (path) => {
    return route.fullPath === path ? "tagView tagActive" : "tagView";
  };
});

onUnmounted(() => {
  unSubscribe();
});

const unSubscribe = tagViewStore.$subscribe((mutation, state) => {
  keepAliveStore.setKeepAliveList(state.tagView);
  SessionStorage.set("tagView", state.tagView);
});
</script>

<style lang="scss" scoped>
.tagViewBase {
  .tagView {
    margin: 4px 3px 2px 3px;
    min-height: 20px;
    padding: 0 10px;
    border-style: solid;
    border-width: 1px;
    border-color: $grey-4;
    border-radius: 4px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .tagActive {
    font-weight: bold;
    color: var(--q-active-text-color, #ffffff) !important;
    background: var(--q-active-bg-color, #1976d2) !important;
  }
}

.tagView-remove-icon {
  // font-size: .7rem;
  // border-radius: 0.2rem;
  font-weight: bold;
  opacity: 0.58;
  transition: all 0.3s;

  &:hover {
    opacity: 1;
  }
}

.line-limit-length {
  margin: 0px 5px 0px 7px;
  overflow: hidden;
  max-width: 180px;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
