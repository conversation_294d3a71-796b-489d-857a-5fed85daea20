<template>
  <q-dialog v-model="selectItemVisible">
    <q-card style="min-width: 700px; max-width: 45vw">
      <q-card-section class="row justify-between items-center">
        <div class="text-subtitle1">
          <span v-if="title !== ''">
            {{ title }}
            {{
              selection === "multiple"
                ? $t("SelectMultiple")
                : $t("SelectSingle")
            }}
          </span>
          <span v-else>
            {{
              $t("SelectOrderComponent", {
                oneOrMultiple:
                  selection === "multiple"
                    ? $t("SelectMultiple")
                    : $t("SelectSingle"),
              })
            }}
          </span>
        </div>
        <span
          v-if="selection === 'multiple'"
          class="text-subtitle2 text-negative row justify-center"
        >
          多选打勾后，翻页不会保存
        </span>
        <q-btn
          v-if="selection === 'multiple'"
          dense
          color="primary"
          :label="$t('Select')"
          @click="handleSelectItem"
        />
      </q-card-section>

      <q-separator />

      <q-card-section class="items-center row q-gutter-md">
        <q-table
          v-model:pagination="pagination"
          v-model:selected="selected"
          row-key="id"
          :rows="tableData"
          :columns="columns"
          :rows-per-page-options="pageOptions"
          :loading="loading"
          :selection="selection"
          class="col"
          @request="onRequest"
        >
          <template #body-cell-actions="props">
            <q-td :props="props">
              <div class="q-gutter-xs">
                <q-btn
                  v-if="selection !== 'multiple'"
                  dense
                  color="primary"
                  :label="t('admin.Select')"
                  @click="handleSelectItem(props.row)"
                />
              </div>
            </q-td>
          </template>
        </q-table>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import useTableData from "src/composables/useTableData";
import { ArrayOrObject } from "src/utils/arrayOrObject";
import { computed, ref, toRefs } from "vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const props = defineProps({
  // 必须传递单选多选: multiple, single
  selection: {
    type: String,
    required: false,
    default: "single",
  },
  title: {
    type: String,
    required: false,
    default: "",
  },
});
const { selection, title } = toRefs(props);
const url = {
  list: "/api/info/infoCategory/list",
};
const columns = computed(() => {
  return [
    { name: "id", align: "center", label: t("ID"), field: "id" },
    { name: "name", align: "center", label: t("Name"), field: "name" },
    { name: "actions", align: "center", label: t("Actions"), field: "actions" },
  ];
});
const { pagination, pageOptions, loading, tableData, onRequest, getTableData } =
  useTableData(url);
const selectItemVisible = ref(false);
const selected = ref(null);

const show = (selectItem) => {
  selected.value = [];
  selectItemVisible.value = true;
  pagination.value.order = "weight";
  pagination.value.rowsPerPage = 9999;
  getTableData();
  if (selection.value === "multiple") {
    if (ArrayOrObject(selectItem) === "Array") {
      selected.value = selectItem;
    } else {
      selected.value = [];
    }
  } else {
    selected.value.push();
  }
};

defineExpose({
  show,
});
const emit = defineEmits(["handleSelectItem"]);
const handleSelectItem = (row) => {
  if (selection.value === "multiple") {
    emit("handleSelectItem", selected.value);
  } else {
    emit("handleSelectItem", row);
  }
  selectItemVisible.value = false;
};
</script>
