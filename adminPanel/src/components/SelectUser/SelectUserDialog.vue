<template>
  <q-dialog v-model="selectUserVisible">
    <q-card style="min-width: 700px; max-width: 45vw">
      <q-card-section class="row justify-between items-center">
        <div class="text-subtitle1">
          <span v-if="title !== ''">
            {{ title }}
            {{ selection === "multiple" ? $t('SelectMultiple') : $t('SelectSingle') }}
          </span>
          <span v-else>
            {{ $t('SelectUserComponent', {
              oneOrMultiple: selection === "multiple" ? $t('SelectMultiple') :
                $t('SelectSingle')
            })
            }}
          </span>
        </div>
        <q-btn
          dense color="primary"
          :label="$t('Select')"
          @click="handleSelectUser()"
        />
      </q-card-section>

      <q-separator />

      <q-card-section class="items-center row q-gutter-md">
        <q-input
          v-model="queryParams.username" dense
          style="width: 20%"
          :label="$t('Username')"
        />
        <q-btn
          dense color="primary"
          :label="$t('Search')"
          @click="handleSearch"
        />
        <q-btn
          dense color="primary"
          :label="$t('Reset')"
          @click="resetSearch"
        />
      </q-card-section>

      <q-table
        v-model:pagination="pagination" v-model:selected="selected"
        row-key="username" :rows="tableData"
        :columns="columns" :rows-per-page-options="pageOptions"
        :loading="loading" :selection="selection"
        @request="onRequest"
      >
        <template #body-cell-admin="props">
          <q-td :props="props">
            <div class="column items-center q-gutter-xs">
              <q-badge v-if="props.row.admin" class="col" color="primary">
                管理员
              </q-badge>
              <q-badge v-else class="col" color="negative">
                其他
              </q-badge>
            </div>
          </q-td>
        </template>
        <template #body-cell-company_name="props">
          <q-td :props="props">
            <div class="column items-center q-gutter-xs">
              <q-badge v-if="props.row.company_name !== ''" class="col" color="primary">
                {{ props.row.company_name }}
              </q-badge>
              <q-badge v-else class="col" color="negative">
                {{ $t('NotBind') + $t('Company') }}
              </q-badge>
            </div>
          </q-td>
        </template>
      </q-table>
    </q-card>
  </q-dialog>
</template>

<script setup>
import useTableData from 'src/composables/useTableData'
import { computed, ref, toRefs } from 'vue'
import { useI18n } from 'vue-i18n'
import { ArrayOrObject } from 'src/utils/arrayOrObject'

const { t } = useI18n()
const props = defineProps({
  // 必须传递单选多选: multiple, single
  selection: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    required: false,
    default: '',
  }
})
const { selection, title } = toRefs(props)
const url = {
  list: 'api/system/sys_user/list'
}
const columns = computed(() => {
  return [
    { name: 'username', align: 'center', label: t('Username'), field: 'username' },
    { name: 'admin', align: 'center', label: t('Administrator'), field: 'admin' },
    { name: 'real_name', align: 'center', label: t('RealName'), field: 'real_name' },
    { name: 'company_name', align: 'center', label: t('Company'), field: 'company_name' },
  ]
})
const {
  pagination,
  queryParams,
  pageOptions,
  loading,
  tableData,
  onRequest,
  getTableData,
  handleSearch,
  resetSearch,
} = useTableData(url)

const selectUserVisible = ref(false)
const selected = ref(null)

const show = (selectUser) => {
  selected.value = []
  selectUserVisible.value = true
  getTableData()
  if (selection.value === 'multiple') {
    if (ArrayOrObject(selectUser) === 'Array') {
      selected.value = selectUser
    }
    else {
      selected.value = []
    }
  }
  else {
    selected.value.push()
  }
}

defineExpose({
  show
})
const emit = defineEmits(['handleSelectUser'])
const handleSelectUser = () => {
  emit('handleSelectUser', selected.value)
  console.log(selected)
  selectUserVisible.value = false
}
</script>
