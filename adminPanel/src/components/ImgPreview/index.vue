<template>
  <div class="row">
    <q-img
      v-if="preview"
      :ratio="16/9"
      :src="returnPreview" :height="height"
      :max-height="height"
      :max-width="maxWidth"
      @click="viewImage"
    />

    <q-img
      v-else
      :ratio="16/9"
      :src="returnPreview" :height="height"
      :max-height="height"
      :max-width="maxWidth"
      @click="viewImage"
    />
    <Preview v-if="preview" ref="previewDialog" :src="returnSrc" />
  </div>
</template>

<script setup>
import { computed, toRefs, ref } from 'vue'
import Preview from 'src/components/ImgPreview/preview.vue'
const props = defineProps({
  src: {
    type: String,
    required: false,
    default: '',
  },
  thumbSrc: {
    type: String,
    required: false,
    default: '',
  },
  height: {
    type: String,
    required: false,
    default: '180',
  },
  maxWidth: {
    type: String,
    required: false,
    default: '240',
  },
  preview: {
    type: Boolean,
    required: false,
    default: false,
  },
})
const { src, height, maxWidth, preview, thumbSrc } = toRefs(props)
const previewDialog = ref(null)

const returnSrc = computed(() => {
  if (src.value !== '') {
    if (src.value.substring(0, 4) === 'http') {
      // 非登录上传，头像为链接
      return src.value
    }
    else if (src.value.substring(0, 11) === 'sys-upload:') {
      // 非登录用户，头像为上传
      return process.env.API + src.value.substring(11)
    }
    else {
      return src.value
    }
  }
  else {
    return 'img/no_pic_800.png'
  }
})

const returnPreview = computed(() => {
  if (thumbSrc.value !== '') {
    if (thumbSrc.value.substring(0, 4) === 'http') {
      // 非登录上传，头像为链接
      return thumbSrc.value
    }
    else if (thumbSrc.value.substring(0, 11) === 'sys-upload:') {
      // 非登录用户，头像为上传
      return process.env.API + thumbSrc.value.substring(11)
    }
    else {
      return thumbSrc.value
    }
  }
  else {
    return 'img/no_pic_240.png'
  }
})

const viewImage = () => {
  previewDialog.value.show()
}
</script>
