import { defineStore } from "pinia";
import { Cookies, LocalStorage } from "quasar";
import { postAction } from "src/api/manage.ts";

export const useSettingStore = defineStore("setting", {
  state: () => ({
    language: "zh-CN",
    languageList: [],
    sideDrawerWidth: 220,
    darkTheme: false,
    sideDrawerTop: "h",
    sideDrawerBottom: "l",
  }),
  getters: {},
  actions: {
    SetDarkTheme(val: boolean) {
      this.darkTheme = val;
      LocalStorage.set("cms-dark-theme", val);
    },
    GetDarkTheme() {
      if (this.darkTheme) {
        return this.darkTheme;
      } else if (LocalStorage.getItem("cms-dark-theme")) {
        return LocalStorage.getItem("cms-dark-theme");
      } else {
        return false;
      }
    },
    GetLanguageList() {
      if (this.languageList) {
        return this.languageList;
      } else if (LocalStorage.getItem("cms-language-list")) {
        return LocalStorage.getItem("cms-language-list");
      } else {
        return ["default"];
      }
    },
    GetLanguage() {
      if (this.language) {
        return this.language;
      } else if (Cookies.get("cms-locale")) {
        return Cookies.get("cms-locale");
      } else {
        return "zh-CN";
      }
    },
    SetSideDrawerWidth(width: number) {
      this.sideDrawerWidth = width;
      LocalStorage.set("cms-side-drawer-width", width);
    },
    GetSideDrawerWidth() {
      if (this.sideDrawerWidth) {
        return this.sideDrawerWidth;
      } else if (LocalStorage.getItem("cms-side-drawer-width")) {
        return LocalStorage.getItem("cms-side-drawer-width");
      } else {
        return 220;
      }
    },
    SetSideDrawerTop(val: string) {
      this.sideDrawerTop = val;
      LocalStorage.set("cms-side-drawer-top", val);
    },
    GetSideDrawerTop() {
      if (this.sideDrawerTop) {
        return this.sideDrawerTop;
      } else if (LocalStorage.getItem("cms-side-drawer-top")) {
        return LocalStorage.getItem("cms-side-drawer-top");
      } else {
        return "h";
      }
    },
    SetSideDrawerBottom(val: string) {
      this.sideDrawerBottom = val;
      LocalStorage.set("cms-side-drawer-bottom", val);
    },
    GetSideDrawerBottom() {
      if (this.sideDrawerBottom) {
        return this.sideDrawerBottom;
      } else if (LocalStorage.getItem("cms-side-drawer-bottom")) {
        return LocalStorage.getItem("cms-side-drawer-bottom");
      } else {
        return "l";
      }
    },
    GetLayoutView() {
      const top = this.GetSideDrawerTop() + "Hh";
      const center = "LpR";
      const bottom = this.GetSideDrawerBottom() + "Fr";
      return top + " " + center + " " + bottom;
    },
  },
});
