import { defineStore } from "pinia";
import { Cookies, LocalStorage, SessionStorage } from "quasar";
import { postAction } from "src/api/manage.ts";
import { Router as router } from "src/router/index";
import { LoginRes } from "src/types";
import { useTagViewStore } from "./tagView";
interface User {
  id: string | null;
  token: string | null;
  token_expire: number | null;
  refresh_time: number | null;
  username: string | null;
  realname: string | null;
  is_admin: boolean;
  role: string;
}

export const useUserStore = defineStore("user", {
  state: (): User => ({
    id: "",
    token: "",
    token_expire: 0,
    refresh_time: 0,
    username: "",
    realname: "",
    is_admin: false,
    role: "",
  }),

  getters: {
    getUserName(state) {
      return state.username;
    },
    getRealName(state) {
      return state.realname;
    },
    getUserRole(state) {
      return state.role;
    },
    getFirstCharacterOfUserName(state) {
      return state.username ? state.username.charAt(0).toUpperCase() : "";
    },
  },

  actions: {
    async HandleLogin(loginForm: LoginRes) {
      const { code, data } = await postAction("/public/login", loginForm);
      if (code === 200 && data) {
        console.log("登录成功", data);
        const token = data.token;
        const token_expire = data.exp;
        this.SetToken(token);
        this.token_expire = token_expire;
        LocalStorage.set("token_expire", token_expire);
      } else {
        return false;
      }
      if (this.GetToken() !== "") {
        await this.GetCurrentUser();
      } else {
        this.HandleLogout();
        return false;
      }
      return true;
    },
    async GetCurrentUser() {
      const { code, data } = await postAction("/api/user/current", {});
      if (code === 200) {
        const id = data.id;
        this.id = id;
        const username = data.username;
        const realname = data.realname;
        const is_admin = data.is_admin ? true : false;
        const role = data.role;
        this.username = username;
        LocalStorage.set("cms-username", username);
        this.realname = realname;
        LocalStorage.set("cms-realname", realname);
        this.is_admin = is_admin;
        LocalStorage.set("cms-is_admin", is_admin);
        this.role = role;
        LocalStorage.set("cms-role", role);
        return true;
      } else {
        return false;
      }
    },
    SetToken(token: string) {
      this.token = token;
      LocalStorage.set("Authorization", token);
    },
    setUserRoles(role: string) {
      this.role = role;
    },
    HandleLogout() {
      // const permissionStore = usePermissionStore()
      // permissionStore.ClearMenu()
      SessionStorage.remove("Authorization");
      LocalStorage.remove("Authorization");
      LocalStorage.remove("access_expire");
      LocalStorage.remove("refresh_time");
      LocalStorage.remove("cms-username");
      LocalStorage.remove("cms-realname");
      LocalStorage.remove("cms-is_admin");
      LocalStorage.remove("cms-role");
      Cookies.remove("cms-token");
      // 字典不删除
      // LocalStorage.remove('cms-dict')
      this.token = "";
      this.token_expire = 0;
      this.username = "";
      this.realname = "";
      this.is_admin = false;
      this.role = "";
    },
    GetToken() {
      if (Cookies.get("cms-token")) {
        return Cookies.get("cms-token");
      } else if (LocalStorage.getItem("Authorization")) {
        return LocalStorage.getItem("Authorization");
      } else {
        return this.token;
      }
    },
    GetUsername() {
      if (this.username) {
        return this.username;
      } else if (LocalStorage.getItem("cms-username")) {
        return LocalStorage.getItem("cms-username");
      } else {
        return "";
      }
    },
    GetUserInfo() {
      if (this.username === "") {
        this.username = LocalStorage.getItem("cms-username");
      }
      if (this.realname === "") {
        this.realname = LocalStorage.getItem("cms-realname");
      }
      if (this.token === "") {
        if (Cookies.get("cms-token")) {
          this.token = Cookies.get("cms-token");
        } else {
          this.token = LocalStorage.getItem("Authorization");
        }
      }
      if (this.role === "") {
        this.token = LocalStorage.getItem("cms-role");
      }
      const res: User = {
        token: this.token,
        token_expire: this.token_expire,
        username: this.username,
        realname: this.realname,
        is_admin: this.is_admin,
        role: this.role,
        refresh_time: null,
        id: null,
      };
      return res;
    },
    GetAdmin() {
      if (this.is_admin) {
        return this.is_admin;
      } else if (LocalStorage.getItem("cms-is_admin")) {
        return LocalStorage.getItem("cms-is_admin");
      } else {
        return "";
      }
    },
    GetUserRole(): string | null {
      if (this.role) {
        return this.role;
      } else if (LocalStorage.getItem("cms-role")) {
        return LocalStorage.getItem("cms-role");
      } else {
        return "";
      }
    },
    setLogout() {
      this.username = "";
      this.role = "";
      Cookies.remove("cms-token");
      SessionStorage.remove("user");
      SessionStorage.clear();

      const tagViewStore = useTagViewStore();
      tagViewStore.removeAllTagView();

      router.push({ name: "Login" });
    },
  },
});
