import { defineStore } from "pinia";
import { postAction } from "src/api/manage.ts";
import { asyncRoutesChildren } from "src/router/routes";
import { useUserStore } from "src/stores/user";
import { Route } from "src/types";
import { ArrayToTree } from "src/utils/arrayAndTree";
import { HandleRouter, transformMenuToRoutes } from "src/utils/router";

export const useRouterStore = defineStore("routes", {
  state: () => ({
    permissionRoutes: [] as Route[],
    sidebarMenu: [] as Route[],
    visibleMenu: [] as Route[],
  }),

  getters: {
    getPermissionRoutes(state): Route[] {
      return state.permissionRoutes;
    },
    getVisibleMenu(state): Route[] {
      return state.visibleMenu;
    },
  },

  actions: {
    async setRoutes(routes: Route[]) {
      this.permissionRoutes = routes;
    },
    async getUserMenu() {
      const { data, code } = await postAction("/api/menu/current", {});
      if (code === 200) {
        const userMenu = HandleRouter(data);
        this.InitPermissionMenu(userMenu);
        const noHiddenMenu = data.filter((item: any) => {
          return item.hidden === "no";
        });
        const deepVisibleMenu = JSON.parse(JSON.stringify(noHiddenMenu));
        const visibleMenu = ArrayToTree(deepVisibleMenu, "name", "parent");
        this.InitVisibleMenu(transformMenuToRoutes(visibleMenu));
        return userMenu;
      } else {
        const userStore = useUserStore();
        userStore.HandleLogout();
        return;
      }
    },
    InitPermissionMenu(routes: Route[]) {
      const menu: Route[] = asyncRoutesChildren;
      const push = function (routes: Route[]) {
        routes.forEach((route) => {
          if (route.children && route.children.length > 0) {
            push(route.children);
          } else {
            const { meta, name, path, fullPath, component } = route;
            menu.push({ meta, name, path, fullPath, component });
          }
        });
      };
      push(routes);
      this.permissionRoutes = menu;
    },
    InitVisibleMenu(visibleMenu: Route[]) {
      visibleMenu.unshift(asyncRoutesChildren[0]);
      this.visibleMenu = visibleMenu;
    },
  },
});
