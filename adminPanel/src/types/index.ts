import {
  <PERSON><PERSON>eta,
  RouteR<PERSON>ord<PERSON>ame,
  RouteRecordRedirectOption,
} from "vue-router";

export interface Route {
  name: string;
  path: string;
  fullPath?: string;
  redirect?: RouteRecordRedirectOption | undefined;
  component?: any;
  children?: Route[];
  meta?: RouteMeta;
  props?: boolean | Record<string, any> | ((to: any) => Record<string, any>);
}

export interface RouteData {
  title: string;
  fullPath: string;
  icon?: string;
  keepAlive?: boolean;
  name: RouteRecordName | null | undefined;
  isHidden?: unknown;
}

export interface User {
  _id: string;
  username: string;
  password?: string;
  realname?: string;
  is_admin: boolean;
  is_active: boolean;
  role?: string;
  group_id?: string;
}

export interface LoginData {
  username: string;
  password: string;
  captcha_str: string;
  captcha_id: string;
}

export interface LoginRes {
  token: string;
  exp: number;
}

export interface responseData<T = any> {
  code: number;
  data: T | undefined;
  msg: string;
}
