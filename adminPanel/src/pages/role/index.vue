<template>
  <BaseContent scrollable>
    <div class="row q-ma-md">
      <div class="col">
        <q-table v-model:pagination="pagination" row-key="id" separator="cell" :rows="tableData" :columns="columns"
          :rows-per-page-options="pageOptions" :loading="loading" @request="onRequest">
          <template #top="props">
            <q-btn color="primary" :label="$t('admin.Add') + ' ' + $t('admin.Role')" @click="showAddForm()" />
            <q-space />
            <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'" class="q-ml-md"
              @click="props.toggleFullscreen" />
          </template>
          <template #body-cell-stable="props">
            <q-td :props="props">
              <DictStatus :dict-code="'stable'" :dict-status="props.row.stable" />
            </q-td>
          </template>
          <template #body-cell-actions="props">
            <q-td :props="props">
              <div class="q-gutter-xs">
                <q-btn color="primary" :label="$t('admin.Edit')" @click="showEditForm(props.row)" />
                <q-btn color="warning" :label="$t('admin.Role') + $t('admin.User')" @click="showRoleUser(props.row)" />
                <q-btn color="warning" :label="$t('admin.Role') + $t('admin.Permission')"
                  @click="showRolePermission(props.row)" />
                <q-btn color="negative" :label="$t('admin.Delete')" @click="handleDelete(props.row)" />
              </div>
            </q-td>
          </template>
        </q-table>
      </div>
    </div>
    <RecordDetail ref="recordDetailDialog" @handleFinish="handleFinish" />
    <RolePermissionDialog ref="rolePermissionDialog" @handleFinish="handleFinish" />
    <RoleUserDialog ref="roleUserDialog" />
  </BaseContent>
</template>

<script setup>
import { useQuasar } from "quasar";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import useTableData from "src/composables/useTableData";
import { computed, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import RecordDetail from "./modules/recordDetail.vue";
import RolePermissionDialog from './modules/RolePermissionDialog.vue'
import RoleUserDialog from './modules/RoleUserDialog.vue'
import DictStatus from 'src/components/DictStatus/index.vue'

const $q = useQuasar();
const { t } = useI18n();
const url = {
  list: "/api/role/list",
  item: "/api/role",
  create: "/api/role",
  edit: "/api/role",
  delete: "/api/role",
};
const columns = computed(() => {
  return [
    { name: 'order', align: 'center', label: t('admin.Order'), field: 'order' },
    { name: 'code', align: 'center', label: t('admin.Role') + t('admin.Code'), field: 'code' },
    { name: 'name', align: 'center', label: t('admin.Role') + t('admin.Name'), field: 'name' },
    { name: 'stable', align: 'center', label: t('admin.Stable'), field: 'stable' },
    { name: 'desc', align: 'center', label: t('admin.Description'), field: 'desc' },
    { name: 'actions', align: 'center', label: t('admin.Actions'), field: 'actions' },
  ]
});
const {
  pagination,
  pageOptions,
  loading,
  tableData,
  recordDetailDialog,
  onRequest,
  getTableData,
  showAddForm,
  showEditForm,
  handleDelete,
  handleFinish,
} = useTableData(url);

onMounted(() => {
  pagination.value.order = "order";
  getTableData();
});

const rolePermissionDialog = ref(null)
const showRolePermission = (row) => {
  console.log(row)
  rolePermissionDialog.value.show(row)
}
const roleUserDialog = ref(null)
const showRoleUser = (row) => {
  roleUserDialog.value.show(row)
}
</script>
