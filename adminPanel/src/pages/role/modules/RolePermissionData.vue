<template>
  <div class="items-center column">
    <div class="justify-center row" style="width: 100%">
      <q-btn color="primary" :disable="row.role_code === 'administrator'" @click="handleDataPermission">
        {{ $t('Save') }}
      </q-btn>
    </div>

    <q-select
      v-if="dict?.length" v-model="deptDataPermissionType"
      :options="dict" emit-value
      map-options :label="$t('DeptDataPermissionType')"
      style="width: 100%"
      @update:model-value="checkCustom"
    >
      <template #option="scope">
        <q-item v-ripple v-bind="scope.itemProps">
          <q-item-section>
            <q-item-label>
              {{ scope.opt.label }}
            </q-item-label>
            <q-item-label caption>
              {{ scope.opt.value }}
            </q-item-label>
          </q-item-section>
        </q-item>
      </template>
    </q-select>

    <q-card-section
      v-if="tableData.length !== 0 && deptDataPermissionType === 'deptDataPermissionType_custom'" style="width: 100%; max-height: 70vh"
      class="scroll"
    >
      <q-tree
        v-if="tableData.length" v-model:ticked="ticked"
        style="width: 100%" :nodes="tableData"
        default-expand-all
        node-key="dept_code" label-key="name"
        selected-color="primary" tick-strategy="strict"
      >
        <template #default-header="prop">
          <div class="items-center row">
            <div class="text-weight-bold">{{ prop.node.dept_name }}</div>
          </div>
        </template>
      </q-tree>
    </q-card-section>
    <q-inner-loading :showing="loading">
      <q-spinner-gears size="50px" color="primary" />
    </q-inner-loading>
  </div>
</template>

<script setup>
import useTableData from 'src/composables/useTableData'
import { useQuasar } from 'quasar'
import { postAction } from 'src/api/manage'
import { onMounted, ref, toRefs } from 'vue'

const $q = useQuasar()
const url = {
  list: 'api/system/company/dept/tree',
  roleDeptDataEdit: 'api/system/sys_role/dept',
}
const props = defineProps({
  row: {
    type: Object,
    required: true,
  }
})
const { row } = toRefs(props)
const dict = [
  { 'label': '全部部门数据权限', 'value': 'deptDataPermissionType_all' },
  { 'label': '用户本人数据权限', 'value': 'deptDataPermissionType_user' },
  { 'label': '用户所在部门数据权限', 'value': 'deptDataPermissionType_dept' },
  { 'label': '用户所在部门及子部门数据权限', 'value': 'deptDataPermissionType_deptAndChildren' },
  { 'label': '自定义部门数据权限', 'value': 'deptDataPermissionType_custom' },
]
const {
  dictOptions,
  pagination,
  loading,
  tableData,
  getTableData,
} = useTableData(url)

const ticked = ref([])
const deptDataPermissionType = ref('')

onMounted(async() => {
  pagination.value.rowsPerPage = 99999
  deptDataPermissionType.value = row.value.dept_data_permission_type
  if (row.value.dept_data_permission_custom !== '') {
    ticked.value = row.value.dept_data_permission_custom.split(',')
  }
  if (row.value.dept_data_permission_type === 'deptDataPermissionType_custom') {
    getTableData()
  }
})
const checkCustom = (value) => {
  if (value === 'deptDataPermissionType_custom') {
    getTableData()
  }
}
const handleDataPermission = () => {
  let custom = ''
  if (deptDataPermissionType.value === 'deptDataPermissionType_custom') {
    custom = ticked.value.join(',')
  }
  else {
    ticked.value = []
  }
  postAction(url.roleDeptDataEdit, {
    role_code: row.value.role_code,
    dept_data_permission_type: deptDataPermissionType.value,
    dept_data_permission_custom: custom,
  }).then((res) => {
    if (res.code === 200) {
      $q.notify({
        type: 'positive',
        message: res.msg,
      })
    }
  })
}
</script>
