<template>
  <q-dialog v-model="roleUserVisible" position="right">
    <q-card style="min-width: 800px; max-width: 80vw; height: 100%">
      <q-card-section>
        {{ record.role_name }}
      </q-card-section>
      <q-table v-model:pagination="pagination" row-key="id" separator="cell" :rows="tableData" :columns="columns"
        :rows-per-page-options="pageOptions" :loading="loading" @request="onRequest">
        <template #top="props">
          <q-btn dense color="primary" :label="$t('admin.Add') + $t('admin.User')" @click="showAddUserForm()" />
          <q-space />
        </template>

        <template #body-cell-actions="props">
          <q-td :props="props">
            <div class="q-gutter-xs">
              <q-btn v-if="props.row.username !== 'admin'" dense color="negative" :label="$t('admin.Delete')"
                @click="handleRemove(props.row)" />
            </div>
          </q-td>
        </template>
      </q-table>
    </q-card>
    <SelectUserDialog ref="selectUserDialog" selection="multiple" @handleSelectUser="handleSelectUser" />
  </q-dialog>
</template>

<script setup>
import { useQuasar } from "quasar";
import { deleteAction, postAction } from "src/api/manage.ts";
import SelectUserDialog from "src/components/SelectUser/SelectUserDialog.vue";
import useTableData from "src/composables/useTableData";
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";

const $q = useQuasar();
const { t } = useI18n();
const url = {
  list: "api/user/role",
  item: "api/system/sys_role/user",
};
const columns = computed(() => {
  return [
    {
      name: "username",
      align: "center",
      label: t("admin.Username"),
      field: "username",
    },
    {
      name: "is_admin",
      align: "center",
      label: t("admin.Admin"),
      field: "is_admin",
    },
    {
      name: "mobile",
      align: "center",
      label: t("admin.Mobile"),
      field: "mobile",
    },
    {
      name: "realname",
      align: "center",
      label: t("admin.RealName"),
      field: "realname",
    },
    {
      name: "group.name",
      align: "center",
      label: t("admin.Group") + t("admin.Name"),
      field: "group.name",
    },
    {
      name: "actions",
      align: "center",
      label: t("admin.Actions"),
      field: "actions",
    },
  ];
});
const {
  pagination,
  queryParams,
  pageOptions,
  loading,
  tableData,
  onRequest,
  getTableData,
} = useTableData(url);

const roleUserVisible = ref(false);

const record = ref({});

const show = (row) => {
  tableData.value = [];
  record.value = row;
  queryParams.value.id = { id: row.id };
  pagination.value.page.limit = 50;
  roleUserVisible.value = true;
  getTableData();
};
defineExpose({
  show,
});

const selectUserDialog = ref(null);
const showAddUserForm = () => {
  selectUserDialog.value.show(tableData.value);
};
const handleRemove = (row) => {
  $q.dialog({
    title: t("admin.Confirm"),
    message: t("admin.Confirm") + t("admin.Delete") + "?",
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    if (record.value.roleCode === "administrator" && row.username === "admin") {
      $q.notify({
        type: "negative",
        message: t("admin.CanNotDeleteThis"),
      });
      return false;
    }
    const res = await deleteAction(url.item, {
      role_code: record.value.code,
      ids: [row.id],
    });
    if (res.code === 200) {
      $q.notify({
        type: "positive",
        message: res.msg,
      });
    }
    getTableData();
  });
};
const handleSelectUser = (event) => {
  const userIdList = [];
  for (const i of event) {
    userIdList.push(i.id);
  }
  console.log(userIdList, record.value);
  postAction(url.item, {
    role_code: record.value.code,
    role_id: record.value.id,
    ids: userIdList,
  }).then((res) => {
    if (res.code === 200) {
      $q.notify({
        type: "positive",
        message: res.msg,
      });
    }
    getTableData();
  });
};
</script>
