<template>
  <div class="items-center column">
    <div class="justify-between row" style="width: 100%">
      <q-btn color="negative" @click="handleClear">
        {{ $t('admin.Clear') + $t('admin.All') }}
      </q-btn>
      <q-btn color="negative" @click="handleAll(tableData)">
        {{ $t('admin.Select') + $t('admin.All') }}
      </q-btn>
      <q-btn color="primary" :disable="row.role_code === 'administrator'" @click="handleRoleMenu">
        {{ $t('admin.Save') }}
      </q-btn>
    </div>
    <q-card-section style="width: 100%; max-height: 70vh" class="scroll">
      <q-tree v-if="menuTree.length !== 0" v-model:ticked="ticked" dense style="width: 100%" :nodes="menuTree"
        default-expand-all node-key="id" label-key="name" selected-color="primary" tick-strategy="strict">
        <template #default-header="prop">
          <div class="items-center row">
            <q-icon :name="prop.node.icon || 'share'" size="28px" class="q-mr-sm" />
            <div class="text-weight-bold">{{ $t('router.' + prop.node.title) }}</div>
          </div>
        </template>
      </q-tree>
    </q-card-section>
    <q-inner-loading :showing="loading">
      <q-spinner-gears size="50px" color="primary" />
    </q-inner-loading>
  </div>

</template>

<script setup>
import useTableData from 'src/composables/useTableData'
import { useQuasar } from 'quasar'
import { postAction } from 'src/api/manage'
import { computed, onMounted, ref, toRefs } from 'vue'

const $q = useQuasar()
const url = {
  list: 'api/menu/list',
  roleMenuList: 'api/menu/role',
  relateMenuItem: 'api/role/relate/menu',
}

const props = defineProps({
  row: {
    type: Object,
    required: true,
  }
})
const { row } = toRefs(props)

const {
  loading,
  tableData,
  getTableData,
} = useTableData(url)

const menuTree = computed(() => {
  // 如果是administrator角色，那么禁用角色管理编辑，保证可以访问此界面。
  if (row.value.code === 'administrator' && tableData.value.length) {
    console.log(tableData.value)
    for (const m of tableData.value) {
      if (m.name === 'roleList') {
        m.disabled = true
      }
    }
  }
  return tableData.value
})

onMounted(async () => {
  await getTableData()
  getRoleMenuList()
})

const ticked = ref([])
const getRoleMenuList = () => {
  // 每次获取前，清空ticked
  ticked.value = []
  postAction(url.roleMenuList, {
    id: row.value.id,
  }).then((res) => {
    if (res.code === 200) {
      res.data.forEach((item) => {
        ticked.value.push(item.id)
      })
      console.log(ticked.value)
    }
  })
}
const handleRoleMenu = () => {
  const roleMenu = []
  for (const i of ticked.value) {
    roleMenu.push(i)
  }
  postAction(url.relateMenuItem, {
    from: row.value.id,
    to: roleMenu,
  }).then((res) => {
    if (res.code === 200) {
      $q.notify({
        type: 'positive',
        message: res.msg,
      })
      getRoleMenuList()
    }
  })
}
const handleClear = () => {
  ticked.value = []
}

const handleAll = (row) => {
  row.forEach((item) => {
    if (item.children) {
      handleAll(item.children)
    }
    ticked.value.push(item.code)
  })
}
</script>
