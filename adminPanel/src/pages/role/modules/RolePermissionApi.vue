<template>
  <div class="items-center column">
    <div class="justify-between row" style="width: 100%">
      <q-btn color="negative" @click="handleClear">
        {{ $t("admin.Clear") + $t("admin.All") }}</q-btn>
      <q-btn color="negative" @click="handleAll">
        {{ $t("admin.Select") + $t("admin.All") }}</q-btn>
      <q-btn color="primary" :disable="row.role_code === 'administrator'" @click="handleRoleApi">
        {{ $t("admin.Save") }}
      </q-btn>
    </div>

    <q-card-section style="width: 100%; max-height: 70vh" class="scroll">
      <q-splitter v-model="splitterModel">
        <template #before>
          <q-tabs v-model="apiTab" dense vertical class="text-grey" active-color="primary" indicator-color="primary">
            <q-tab v-for="(item, index) in apiData" :key="index" :name="item.group"
              :label="item.group + getThisTickedNumber(item)" />
          </q-tabs>
        </template>
        <template #after>
          <q-tab-panels v-model="apiTab" animated swipeable vertical transition-prev="jump-up"
            transition-next="jump-up">
            <q-tab-panel v-for="(item, index) in apiData" :key="index" :name="item.group">
              <q-tree v-if="item.children.length !== 0" v-model:ticked="ticked" dense :nodes="item.children"
                default-expand-all node-key="trueId" selected-color="primary" tick-strategy="strict">
                <template #default-header="prop">
                  <div class="row items-center">
                    <q-chip v-if="prop.node.group.substring(0, 7) === 'plugin-'" color="accent" text-color="white"
                      dense>
                      {{ prop.node.group }}
                    </q-chip>
                    <q-chip v-else color="primary" text-color="white" dense>
                      {{ prop.node.group }}
                    </q-chip>
                    <q-chip v-if="prop.node.method === 'POST'" color="primary" text-color="white" dense>
                      {{ prop.node.method }}
                    </q-chip>
                    <q-chip v-if="prop.node.method === 'GET'" color="positive" text-color="white" dense>
                      {{ prop.node.method }}
                    </q-chip>
                    <q-chip v-if="prop.node.method === 'DELETE'" color="negative" text-color="white" dense>
                      {{ prop.node.method }}
                    </q-chip>
                    <q-chip v-if="prop.node.method === 'PUT'" color="warning" text-color="white" dense>
                      {{ prop.node.method }}
                    </q-chip>
                    <div class="text-weight-bold">
                      {{ prop.node.path }}
                    </div>
                    <!--                    <span class="text-weight-light text-black">-->
                    <!--                      （{{ prop.node.memo }}）-->
                    <!--                    </span>-->
                  </div>
                </template>
              </q-tree>
            </q-tab-panel>
          </q-tab-panels>
        </template>
      </q-splitter>
    </q-card-section>
    <q-inner-loading :showing="loading">
      <q-spinner-gears size="50px" color="primary" />
    </q-inner-loading>
  </div>
</template>

<script setup>
import { useQuasar } from "quasar";
import { postAction } from "src/api/manage.ts";
import useTableData from "src/composables/useTableData";
import { computed, onMounted, ref, toRefs } from "vue";

const $q = useQuasar();
const splitterModel = ref(20);
const url = {
  list: "api/permission/list",
  rolePermissionList: "api/permission/role",
  relatePermissionItem: "api/role/relate/permission",
};

const props = defineProps({
  row: {
    type: Object,
    required: true,
  },
});
const { row } = toRefs(props);

const { pagination, loading, tableData, getTableData } = useTableData(url);

onMounted(() => {
  pagination.value.rowsPerPage = 0;
  getTableData();
  getRoleApiList();
});

const apiDataTrue = ref([]);
const apiTab = ref("");

const apiData = computed(() => {
  if (tableData.value.length) {
    const data = tableData.value;
    for (const item of data) {
      item.trueId = "g:" + item.group + "p:" + item.path + "m:" + item.method;
    }
    apiDataTrue.value = data;
    const apiTree = [];
    for (const d of data) {
      if (apiTree.find((item) => item.group === d.group) === undefined) {
        apiTree.push({
          group: d.group,
          children: [],
        });
      }
    }
    for (const d of data) {
      for (const a of apiTree) {
        if (a.group === d.group) {
          a.children.push(d);
        }
      }
    }
    apiTab.value = apiTree[0].group;
    // 如果是administrator角色，那么禁用所有API编辑，保证API可以拥有全部调用权限。
    if (row.value.role_code === "administrator") {
      for (const a of apiTree) {
        for (const i of a.children) {
          i.disabled = true;
        }
      }
    }
    return apiTree;
  }
  return [];
});
const ticked = ref([]);

const getThisTickedNumber = computed(() => {
  return (api) => {
    const allNumber = api.children.length;
    var tickedNumber = 0;
    for (const t of ticked.value) {
      if (api.children.find((item) => item.trueId === t) !== undefined) {
        tickedNumber++;
      }
    }
    return "(" + tickedNumber + "/" + allNumber + ")";
  };
});

const getRoleApiList = () => {
  // 每次获取前，清空ticked
  ticked.value = [];
  postAction(url.rolePermissionList, {
    id: row.value.id,
  }).then((res) => {
    console.log(res);
    if (res.code === 200 && res.data) {
      res.data.forEach((item) => {
        ticked.value.push(
          "g:" + item.group + "p:" + item.path + "m:" + item.method
        );
      });
    }
  });
};
const handleRoleApi = () => {
  const rolePermission = [];
  tableData.value.forEach((item) => {
    for (const t of ticked.value) {
      if (t === item.trueId) {
        rolePermission.push(item.id);
      }
    }
  });
  postAction(url.relatePermissionItem, {
    from: row.value.id,
    to: rolePermission,
  }).then((res) => {
    if (res.code === 200) {
      $q.notify({
        type: "positive",
        message: res.msg,
      });
      getRoleApiList();
    }
  });
};
const handleClear = () => {
  ticked.value = [];
};
const handleAll = () => {
  ticked.value = [];
  tableData.value.forEach((item) => {
    ticked.value.push(
      "g:" + item.group + "p:" + item.path + "m:" + item.method
    );
  });
};
</script>
