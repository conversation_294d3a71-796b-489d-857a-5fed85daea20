<template>
  <q-dialog v-model="recordDetailVisible" position="top">
    <q-card style="width: 800px; max-width: 80vw;">
      <q-card-section>
        <div class="text-h6">
          {{ formTypeName }} {{ $t('admin.Role') }}:
          {{ recordDetail.value.name }}
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-form ref="recordDetailForm">
          <gqa-form-top :record-detail="recordDetail" />
          <div class="row">
            <q-input v-model.number="recordDetail.value.order" class="col q-mx-sm" type="number"
              :rules="[val => val >= 1 || '排序必须大于0']" label="排序" />
          </div>
          <div class="row">
            <q-input v-model="recordDetail.value.name" class="col q-mx-sm" label="角色名称"
              :rules="[val => val && val.length > 0 || '必须输入角色名称']" />
          </div>
          <div class="row">
            <q-input v-model="recordDetail.value.code" class="col q-mx-sm" label="系统编码(英文)"
              :rules="[val => val && val.length > 0 || '必须输入角色系统编码']" />
          </div>
          <div class="row">
            <q-input v-model="recordDetail.value.desc" class="col q-mx-sm" label="角色描述" />
          </div>
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn :label="'保存' + formTypeName" color="primary" @click="handleAddOrEdit" />
        <q-btn v-close-popup label="取消" color="negative" />
      </q-card-actions>

      <q-inner-loading :showing="loading">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
import useRecordDetail from 'src/composables/useRecordDetail'

const emit = defineEmits(['handleFinish'])
const url = {
  item: '/api/role',
  create: '/api/role',
  edit: '/api/role',
}
const {
  formType,
  formTypeName,
  recordDetail,
  recordDetailVisible,
  loading,
  show,
  recordDetailForm,
  handleAddOrEdit
} = useRecordDetail(url, emit)

defineExpose({
  show,
  formType,
  recordDetail
})
</script>
