<template>
  <BaseContent scrollable>
    <div class="row q-ma-md">
      <div class="col">
        <div class="items-center row q-gutter-md" style="margin-bottom: 10px">
          <q-input v-model="queryParams.name" style="width: 20%" :label="t('admin.Product') + t('admin.Name')" />
          <q-btn color="primary" :label="t('admin.Search')" @click="handleSearch" />
          <q-btn color="primary" :label="t('admin.Reset')" @click="resetSearch" />
        </div>
        <q-table v-model:pagination="pagination" row-key="id" separator="cell" :rows="tableData" :columns="columns"
          :rows-per-page-options="pageOptions" :loading="loading" @request="onRequest">
          <template #top="props">
            <q-btn color="primary" :label="t('admin.Add') + ' ' + t('admin.Product')" @click="addProduct()" />
            <q-space />
            <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'" class="q-ml-md"
              @click="props.toggleFullscreen" />
          </template>

          <template #body-cell-category="props">
            <q-td :props="props">
              <div v-if="props.row.category.name">
                {{ props.row.category.name }}
              </div>
              <div v-else>未设置</div>
            </q-td>
          </template>

          <template #body-cell-model="props">
            <q-td :props="props">
              <div v-if="props.row.model">
                {{ props.row.model }}
              </div>
              <div v-else>未设置</div>
            </q-td>
          </template>

          <template #body-cell-price="props">
            <q-td :props="props">
              <div v-if="props.row.salesPrice">
                {{ keepNum(props.row.salesPrice) }}
              </div>
              <div v-else>未设置</div>
            </q-td>
          </template>

          <template #body-cell-is_active="props">
            <q-td :props="props">
              <q-chip v-if="props.row.is_active" color="positive" class="text-white">启用</q-chip>
              <q-chip v-else>停用</q-chip>
            </q-td>
          </template>

          <template #body-cell-created_at="props">
            <q-td :props="props">
              {{ showDateTime(props.row.created_at) }}
            </q-td>
          </template>

          <template #body-cell-actions="props">
            <q-td :props="props">
              <div class="q-gutter-xs">
                <q-btn-group>
                  <q-btn size="sm" color="positive" :label="t('admin.Sort')" @click="showEditForm(props.row)" />
                  <q-btn size="sm" color="primary" :label="t('admin.View') + t('admin.Detail')"
                    @click="handleDetail(props.row)" />
                  <q-btn size="sm" color="negative" :label="t('admin.Delete')" @click="handleDelete(props.row)" />
                </q-btn-group>
              </div>
            </q-td>
          </template>
        </q-table>
      </div>
    </div>
    <QuickRecord ref="recordDetailDialog" @handleFinish="handleFinish" />
  </BaseContent>
</template>

<script setup>
import { useQuasar } from "quasar";
import { putAction } from "src/api/manage.ts";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import useTableData from "src/composables/useTableData";
import QuickRecord from "src/pages/product/modules/recordQuickDetail.vue";
import { FormatDateTime } from "src/utils/date";
import { computed, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";

const router = useRouter();
const $q = useQuasar();
const { t } = useI18n();
const url = {
  list: "/api/business/product/list",
  create: "/api/business/product",
  edit: "/api/business/product",
  delete: "/api/business/product",
};
const columns = computed(() => {
  return [
    {
      name: "name",
      required: true,
      align: "center",
      label: t("admin.Product") + t("admin.Name"),
      field: "name",
    },
    {
      name: "serial",
      align: "center",
      label: t("admin.Product") + t("admin.Serial"),
      field: "serial",
    },
    {
      name: "model",
      align: "center",
      label: t("admin.Model"),
      field: "model",
    },
    {
      name: "category",
      align: "center",
      label: t("admin.Category"),
      field: "category",
    },
    {
      name: "price",
      align: "center",
      label: t("admin.Price"),
      field: "price",
    },
    { name: "sort", align: "center", label: t("admin.Sort"), field: "sort" },
    {
      name: "is_active",
      align: "center",
      label: t("admin.Status"),
      field: "is_active",
    },
    {
      name: "created_at",
      align: "center",
      label: t("admin.CreatedAt"),
      field: "created_at",
    },
    {
      name: "actions",
      required: true,
      align: "center",
      label: t("admin.Actions"),
      field: "actions",
    },
  ];
});
const {
  pagination,
  queryParams,
  pageOptions,
  loading,
  tableData,
  onRequest,
  getTableData,
  handleSearch,
  handleDelete,
  resetSearch,
  showEditForm,
  recordDetailDialog,
  handleFinish,
} = useTableData(url);

onMounted(() => {
  pagination.value = {
    sortBy: "created_at",
    descending: true,
    rowsPerPage: 100,
  };
  getTableData();
});

const handleDetail = (item) => {
  router.push({ name: "ProductDetail", query: { id: item.id } });
};

const addProduct = () => {
  router.push({ name: "ProductCreate" });
};

const itemStatus = ref();
const handleDetailStatus = async (item) => {
  if (item.status === "on") {
    itemStatus.value = "off";
  } else {
    itemStatus.value = "on";
  }
  const { code, message } = await putAction(url.status, {
    id: item.id,
    status: itemStatus.value,
  });
  if (code === 200) {
    $q.notify({
      type: "positive",
      message: message,
    });
    await getTableData();
  } else {
    $q.notify({
      type: "warning",
      message: message,
    });
  }
};
const showDateTime = computed(() => {
  return (datetime) => {
    return FormatDateTime(datetime);
  };
});

const keepNum = computed(() => {
  return (num) => {
    return Number(num).toFixed(2);
  };
});
</script>
