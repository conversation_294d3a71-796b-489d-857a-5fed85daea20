<template>
  <q-dialog v-model="recordDetailVisible" position="right" persistent>
    <q-card style="width: 80vw; max-width: 80vw">
      <q-card-section>
        <div class="text-h6">
          {{ formTypeName }}
          {{ $t("admin.Product") + $t("admin.Attributes") }}:
          {{ recordDetail.value.name }}
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-form ref="recordDetailForm">
          <div class="row">
            <q-input
              v-model="recordDetail.value.name"
              class="col q-mx-sm"
              :label="t('admin.Name')"
            />
            <q-input
              v-model="recordDetail.value.value"
              class="col q-mx-sm"
              :label="t('admin.Attributes') + t('admin.Value')"
            />
            <q-input
              v-if="recordDetail.value.code === 'default'"
              v-model.number="recordDetail.value.order"
              class="col q-mx-sm"
              :label="t('admin.Sort')"
            />
          </div>
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn
          :label="t('admin.Save')"
          color="primary"
          @click="handleAddOrEdit"
        />
        <q-btn v-close-popup :label="t('admin.Cancel')" color="negative" />
      </q-card-actions>

      <q-inner-loading :showing="loading">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
import useRecordDetail from "src/composables/useRecordDetail";
import { ref, toRefs } from "vue";
import { useI18n } from "vue-i18n";

const props = defineProps({
  parentId: {
    type: Number,
    required: false,
    default: 0,
  },
  language: {
    type: String,
    required: false,
    default: "default",
  },
});
const rowIndex = ref();
const { parentId, language } = toRefs(props);
const { t } = useI18n();
const emit = defineEmits(["handleFinish"]);
const url = {
  item: "/api/business/productAttributes",
  create: "/api/business/productAttributes",
  edit: "/api/business/productAttributes",
};

const show = (row, idx) => {
  loading.value = true;
  if (row) {
    recordDetail.value = row;
    rowIndex.value = idx;
  } else {
    recordDetail.value = {};
    rowIndex.value = -1;
    recordDetail.value.order = 0;
  }
  if (parentId.value > 0) {
    recordDetail.value.parent_id = parentId.value;
  }
  if (language.value === "default") {
    recordDetail.value.code = language.value;
  }
  if (row.parent) {
    recordDetail.value.order = row.parent.sort;
  }
  loading.value = false;
  recordDetailVisible.value = true;
  console.log(recordDetail.value);
};

const {
  loading,
  formType,
  formTypeName,
  recordDetail,
  recordDetailVisible,
  recordDetailForm,
} = useRecordDetail(url, emit);

const handleAddOrEdit = () => {
  emit("handleFinish", recordDetail.value, rowIndex.value);
  recordDetailVisible.value = false;
};

defineExpose({
  show,
  formType,
});
</script>
