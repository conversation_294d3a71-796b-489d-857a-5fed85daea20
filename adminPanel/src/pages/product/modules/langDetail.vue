<template>
  <q-dialog v-model="recordDetailVisible" position="right" persistent>
    <q-card style="width: 80vw; max-width: 80vw">
      <q-card-section>
        <div class="text-h6">
          {{ formTypeName }} {{ t("admin.Product") + $t("Translation") }}:
          {{ recordDetail.value.name }}
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-form ref="recordDetailForm">
          <div class="row">
            <q-input
              v-model="recordDetail.value.name"
              class="col-12 q-mx-sm"
              :label="t('admin.Name')"
            />
            <q-input
              v-model="recordDetail.value.seo_kw"
              class="col-12 q-mx-sm"
              :label="t('admin.Seo')"
            />
            <q-input
              v-model="recordDetail.value.desc"
              class="col-12 q-mx-sm"
              :label="t('admin.Desc')"
            />
            <q-input
              v-model="recordDetail.value.remark"
              class="col-12 q-mx-sm"
              :label="t('admin.Remark')"
            />
            <Tinymce
              :value="recordDetail.value.specification"
              class="col-12"
              @getContent="getSpecification"
            />
            <Tinymce
              :value="recordDetail.value.content"
              class="col-12"
              @getContent="getContent"
            />
          </div>
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn
          :label="t('admin.Save')"
          color="primary"
          @click="handleAddOrEdit"
        />
        <q-btn v-close-popup :label="t('admin.Cancel')" color="negative" />
      </q-card-actions>

      <q-inner-loading :showing="loading">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
import Tinymce from "src/components/Editor/TinyMce.vue";
import useRecordDetail from "src/composables/useRecordDetail";
import { ref } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const emit = defineEmits(["handleFinish"]);
const url = {
  item: "/api/business/product/lang",
};
const selectLang = ref();

const show = (row) => {
  loading.value = true;
  if (row) {
    recordDetail.value = row;
  } else {
    recordDetail.value = {};
    selectLang.value = -1;
  }
  loading.value = false;
  recordDetailVisible.value = true;
};

const {
  loading,
  formType,
  formTypeName,
  recordDetail,
  recordDetailVisible,
  recordDetailForm,
} = useRecordDetail(url, emit);

const handleAddOrEdit = () => {
  emit("handleFinish", recordDetail.value, selectLang.value);
  recordDetailVisible.value = false;
};

const getContent = (v) => {
  recordDetail.value.content = v;
};

const getSpecification = (v) => {
  recordDetail.value.specification = v;
};
defineExpose({
  show,
  formType,
});
</script>
