<template>
  <q-dialog v-model="recordDetailVisible" position="right" persistent :allow-focus-outside="true">
    <q-card style="width: 50vw">
      <q-card-section>
        <div class="text-h6">
          {{ recordDetail.value.name }}
        </div>
      </q-card-section>

      <q-card-section>
        <q-form ref="recordDetailForm">
          <div class="row">
            <q-input v-model.number="recordDetail.value.currency" maxlength="50" counter class="col q-mx-sm"
              :label="t('admin.Currency')" />
            <q-input v-model.number="recordDetail.value.sales_price" type="number" class="col q-mx-sm"
              :label="t('admin.Sales') + t('admin.Price')" />
            <q-input v-model.number="recordDetail.value.market_price" type="number" class="col q-mx-sm"
              :label="t('admin.Market') + t('admin.Price')" />
          </div>
          <div class="row">
            <q-input v-model.number="recordDetail.value.order" type="number" class="col q-mx-sm"
              :label="t('admin.ShowIndex') + t('admin.Sort')" />
          </div>
        </q-form>
      </q-card-section>
      <q-card-actions align="right">
        <q-btn :label="t('admin.Save')" color="primary" @click="handleProductAction" />
        <q-btn v-close-popup :label="t('admin.Cancel')" color="negative" />
      </q-card-actions>

      <q-inner-loading :showing="loading">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { useQuasar } from "quasar";
import { putAction } from "src/api/manage.ts";
import useRecordDetail from "src/composables/useRecordDetail";
import { useI18n } from "vue-i18n";

const $q = useQuasar();
const { t } = useI18n();
const emit = defineEmits(["handleFinish"]);
const url = {
  item: "/api/business/product",
  sort: "/api/business/product/quickUpdate",
};

const {
  formType,
  recordDetail,
  recordDetailVisible,
  loading,
  recordDetailForm,
} = useRecordDetail(url, emit);

const show = (row) => {
  recordDetail.value = {};
  loading.value = true;
  recordDetailVisible.value = true;
  if (row && row.id) {
    recordDetail.value = row;
  } else {
    recordDetail.value = {};
  }
  recordDetailVisible.value = true;
  loading.value = false;
};

defineExpose({
  show,
  formType,
});

const handleProductAction = async () => {
  const success = await recordDetailForm.value.validate();
  if (success) {
    if (url === undefined || !url.item) {
      $q.notify({
        type: "negative",
        message: "请先配置url",
      });
      return;
    }
    const { message } = await putAction(url.sort, recordDetail.value);
    $q.notify({
      type: "negative",
      message: message,
    });
    recordDetailVisible.value = false;
    emit("handleFinish");
  } else {
    $q.notify({
      type: "negative",
      message: t("admin.FixForm"),
    });
  }
};
</script>
