<template>
  <q-dialog v-model="recordDetailVisible" position="right" persistent>
    <q-card style="width: 80vw; max-width: 80vw">
      <q-card-section>
        <div class="text-h6">
          {{ formTypeName }} {{ t("admin.Product") + $t("Sku") }}:
          {{ recordDetail.value.name }}
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-form ref="recordDetailForm">
          <div class="row">
            <q-input
              v-model="recordDetail.value.name"
              class="col-12 q-mx-sm"
              :label="t('admin.Name')"
            />
            <q-input
              v-model="recordDetail.value.model"
              class="col-12 q-mx-sm"
              :label="t('admin.Model')"
            />
            <q-input
              v-model="recordDetail.value.specification"
              class="col-12 q-mx-sm"
              :label="t('admin.Specification')"
            />
            <q-input
              v-model="recordDetail.value.size"
              class="col-12 q-mx-sm"
              :label="t('admin.Size')"
            />
            <q-input
              v-model="recordDetail.value.package"
              class="col-12 q-mx-sm"
              :label="t('admin.Package')"
            />
            <q-input
              v-model.number="recordDetail.value.sales_price"
              type="number"
              class="col-12 q-mx-sm"
              :label="t('admin.Sales') + t('admin.Price')"
            />
            <q-input
              v-model.number="recordDetail.value.market_price"
              type="number"
              class="col-12 q-mx-sm"
              :label="t('admin.Market') + t('admin.Price')"
            />
          </div>
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn
          :label="t('admin.Save')"
          color="primary"
          @click="handleAddOrEdit"
        />
        <q-btn v-close-popup :label="t('admin.Cancel')" color="negative" />
      </q-card-actions>

      <q-inner-loading :showing="loading">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
import useRecordDetail from "src/composables/useRecordDetail";
import { ref } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const emit = defineEmits(["handleFinish"]);
const url = {
  item: "system/product/v1/product/attr",
};
const rowIndex = ref();

const show = (row, idx) => {
  loading.value = true;
  if (row) {
    recordDetail.value = row;
    rowIndex.value = idx;
  } else {
    recordDetail.value = {};
    rowIndex.value = -1;
  }
  loading.value = false;
  recordDetailVisible.value = true;
};

const {
  loading,
  formType,
  formTypeName,
  recordDetail,
  recordDetailVisible,
  recordDetailForm,
} = useRecordDetail(url, emit);

const handleAddOrEdit = () => {
  emit("handleFinish", recordDetail.value, rowIndex.value);
  recordDetailVisible.value = false;
};

defineExpose({
  show,
  formType,
});
</script>
