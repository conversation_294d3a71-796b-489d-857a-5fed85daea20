<template>
  <q-dialog v-model="recordDetailVisible" position="right" persistent :allow-focus-outside="true">
    <q-card style="width: 80vw; max-width: 80vw">
      <q-card-section>
        <div class="text-h6">
          {{ formTypeName }} {{ $t("Product") }}:
          {{ recordDetail.value.name }}
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-form ref="recordDetailForm">
          <gqa-form-top :record-detail="recordDetail" />
          <div v-if="formType === 'add'">
            <div class="row q-my-md">
              <q-file v-model="imageList" name="img_files" filled multiple use-chips label="上传产品图片" />
            </div>
            <div v-if="imageData.length" class="row">
              <q-img v-for="item in imageData" :src="item.url" spinner-color="white"
                style="height: 140px; max-width: 150px" class="q-ma-md" />
            </div>
          </div>
          <q-separator />
          <div class="row">
            <SelectCategory v-model:selectCategoryId="recordDetail.value.category_code"
              v-model:selectCategoryName="recordDetail.value.category_name" selection="single" class="col q-mx-sm" />
            <SelectSystemCategory v-model:selectCategoryId="recordDetail.value.sys_category_code"
              v-model:selectCategoryName="recordDetail.value.sys_category_name" selection="single"
              class="col q-mx-sm" />
          </div>
          <div class="row">
            <q-input v-model.number="recordDetail.value.sales_price" class="col q-mx-sm" :label="$t('SalesPrice')" />
            <q-input v-model="recordDetail.value.market_price" class="col q-mx-sm" :label="$t('MarketPrice')" />
          </div>
          <div class="row">
            <q-input v-model="recordDetail.value.name" class="col q-mx-sm" :label="t('admin.Name')" />
          </div>
          <div class="row">
            <q-input v-model="recordDetail.value.brand" class="col q-mx-sm" :label="$t('Brand')" />
            <q-input v-model="recordDetail.value.model" class="col q-mx-sm" :label="$t('Model')" />
            <q-input v-model="recordDetail.value.size" class="col q-mx-sm" :label="$t('Size')" />
          </div>
          <div class="row">
            <q-input v-model="recordDetail.value.barcode" class="col q-mx-sm" :label="$t('Barcode')" />
            <q-input v-model="recordDetail.value.unit" class="col q-mx-sm" :label="$t('Unit')" />
          </div>

          <div v-if="formType === 'add'" class="row">
            <q-field dense class="col q-mx-sm" :label="$t('Status')" stack-label>
              <template #control>
                <q-option-group v-model="recordDetail.value.status" :options="dict" color="primary" inline />
              </template>
            </q-field>
          </div>
          <q-input v-model="recordDetail.value.memo" :label="$t('Memo')" class="q-mx-sm" />
          <q-input v-model="recordDetail.value.desc" :label="$t('Desc')" class="q-mx-sm" />

          <q-separator />

          <div v-if="formType === 'add'" class="row q-my-md">
            <q-btn label="添加属性字段" color="primary" @click="addAttrInfo" />
          </div>
          <div v-if="formType === 'add'" class="row q-my-md">
            <div v-if="attrList.length" class="col">
              <div v-for="(item, index) in attrList" :key="index" class="row q-my-sm">
                <div class="col q-mx-md">
                  <q-input v-model="item.name" :label="t('admin.Name')" />
                </div>
                <div class="col q-mx-md">
                  <q-input v-model="item.value" :label="$t('Value')" />
                </div>
                <div class="col-1 q-mx-md">
                  <q-btn label="移除" size="xz" color="negative" @click="removeAttrInfo(index)" />
                </div>
              </div>
            </div>
          </div>
          <q-separator />
          <div class="row q-my-md">
            <Tinymce :value="recordDetail.value.content" class="col" @getContent="getContent" />
          </div>
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn :label="$t('Save')" color="primary" @click="handleProductAction" />
        <q-btn v-close-popup :label="$t('Cancel')" color="negative" />
      </q-card-actions>

      <q-inner-loading :showing="loading">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { useQuasar } from "quasar";
import { postAction } from "src/api/manage.ts";
import Tinymce from "src/components/Editor/TinyMce.vue";
import SelectCategory from "src/components/SelectCategory/index.vue";
import useRecordDetail from "src/composables/useRecordDetail";
import { computed, reactive, ref } from "vue";
import { useI18n } from "vue-i18n";

const $q = useQuasar();
const { t } = useI18n();
const emit = defineEmits(["handleFinish"]);
const url = {
  item: "v1/business/com/product",
  upload: "v1/business/com/product/image",
  uploadSku: "v1/business/com/product/sku/image",
  attrBatch: "v1/business/com/product/attributes/batch",
  attrSkuBatch: "v1/business/com/product/sku/attributes/batch",
};
const dict = [
  { label: "启用", value: "on" },
  { label: "停用", value: "off" },
];

const imageList = ref();
const attrList = reactive([]);

const addAttrInfo = () => {
  attrList.push({
    value: "",
    name: "",
  });
};
const removeAttrInfo = (index) => {
  attrList.splice(index, 1);
  console.log(attrList);
};

const imageData = computed(() => {
  const res = reactive([]);
  if (imageList.value) {
    imageList.value.forEach((item) => {
      console.log(item);
      const tmp = {
        name: item.name,
        url: URL.createObjectURL(item),
      };
      res.push(tmp);
    });
  }
  return res;
});

const {
  formType,
  formTypeName,
  recordDetail,
  recordDetailVisible,
  loading,
  show,
  recordDetailForm,
  handleAddOrEdit,
} = useRecordDetail(url, emit);

defineExpose({
  show,
  formType,
});

const handleProductAction = async () => {
  const success = await recordDetailForm.value.validate();
  if (success) {
    if (formType.value === "edit") {
      await handleAddOrEdit();
    } else if (formType.value === "add") {
      if (url === undefined || !url.item) {
        $q.notify({
          type: "negative",
          message: "请先配置url",
        });
        return;
      }
      const { code, data, message } = await postAction(
        url.item,
        recordDetail.value,
      );
      if (code === 200) {
        if (imageList.value && data.records.id) {
          imageList.value.forEach((item) => {
            const uploadData = new FormData();
            uploadData.append("file", item);
            uploadData.append("parentId", data.records.id);
            postAction(url.upload, uploadData);
            postAction(url.uploadSku, uploadData);
          });
        }
        if (attrList.values && data.records.id) {
          const AttrInfo = {
            parentId: data.records.id,
            attr_list: attrList,
          };
          postAction(url.attrBatch, AttrInfo);
          postAction(url.attrSkuBatch, AttrInfo);
        }
        $q.notify({
          type: "positive",
          message: message,
        });
      }
      recordDetailVisible.value = false;
    } else {
      $q.notify({
        type: "negative",
        message: t("CanNotAddOrEdit"),
      });
    }
    emit("handleFinish");
  } else {
    $q.notify({
      type: "negative",
      message: "请检查表单信息是否正确",
    });
  }
};

const getContent = (v) => {
  recordDetail.value.content = v;
};

// const handleUpload = (item) => {
//   postAction(url.upload, item).then((res) => {
//     if (res.code === 200) {
//       return res
//     }
//   })
// }
</script>
