<template>
  <BaseContent scrollable>
    <div class="row q-pa-md">
      <q-card v-if="itemDetail?.id" class="col q-mr-sm">
        <q-card-section>
          <div class="row">
            <div class="text-h6 col">
              {{ t("admin.Product") + t("admin.Detail") }}:
              {{ itemDetail.name }}
            </div>
          </div>
          <div class="row">
            <div class="col">
              <q-btn-group push>
                <q-btn size="sm" push label="修改基本信息" icon="edit" color="secondary" @click="editProductDetail" />
                <q-btn size="sm" color="primary" icon="file_open"
                  :label="t('admin.Add') + t('admin.Product') + t('admin.Sku')" @click="addSkuForm" />
              </q-btn-group>
            </div>
          </div>
          <div class="row q-my-md">
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">创建时间</div>
                <div class="col-8">
                  {{ showDateTime(itemDetail.created_at) }}
                </div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">更新时间</div>
                <div class="col-8">
                  {{ showDateTime(itemDetail.updated_at) }}
                </div>
              </div>
            </div>
          </div>
          <div class="row q-my-md">
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">产品条码</div>
                <div class="col-8">
                  <span v-if="itemDetail.barcode">{{
                    itemDetail.barcode
                  }}</span>
                  <span v-else>未设置</span>
                </div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">产品排序</div>
                <div class="col-8">
                  <span v-if="itemDetail.sort">{{ itemDetail.sort }}</span>
                  <span v-else>未设置</span>
                </div>
              </div>
            </div>
          </div>
          <div class="row q-my-md">
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">产品品牌</div>
                <div class="col-8">
                  <span v-if="itemDetail.brand">{{ itemDetail.brand }}</span>
                  <span v-else>未设置</span>
                </div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">计量单位</div>
                <div class="col-8">
                  <span v-if="itemDetail.unit">{{ itemDetail.unit }}</span>
                  <span v-else>未设置</span>
                </div>
              </div>
            </div>
          </div>
          <div class="row q-my-md">
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">产品规格</div>
                <div class="col-8">
                  <span v-if="itemDetail.model">{{ itemDetail.model }}</span>
                  <span v-else>未设置</span>
                </div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">产品尺寸</div>
                <div class="col-8">
                  <span v-if="itemDetail.pack_size">{{
                    itemDetail.pack_size
                  }}</span>
                  <span v-else>未设置</span>
                </div>
              </div>
            </div>
          </div>
          <div class="row q-my-md">
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">运输尺寸</div>
                <div class="col-8">
                  <span v-if="itemDetail.delivery_size">{{
                    itemDetail.delivery_size
                  }}</span>
                  <span v-else>未设置</span>
                </div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">产品重量</div>
                <div class="col-8">
                  <span v-if="itemDetail.weight">{{ itemDetail.weight }}</span>
                  <span v-else>未设置</span>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>
      <q-card class="col">
        <q-card-section>
          <div class="row">
            <div class="text-h6 col">
              {{ t("admin.Product") + t("admin.Image") + t("admin.List") }}
            </div>
            <div class="col-auto">
              <q-btn color="primary" size="sm" :label="t('admin.Add') + t('admin.Product') + t('admin.Image')"
                @click="showUploader" />
            </div>
          </div>
          <div v-if="imgList?.length" class="row">
            <div class="col-6">
              <ImagePreview :src="currentImg.link" :thumb-src="currentImg.link" :height="'120'" :max-width="'180'"
                :preview="true" />
            </div>
            <div class="col-6 q-pa-md">
              <div v-for="(item, index) in imgList" :key="item.id" class="row">
                <q-btn-group class="q-mb-xs">
                  <q-btn size="sm" :label="'图片序号：' + String(index + 1)" @click="changeImg(item)" />
                  <q-btn v-if="!item.is_cover" size="sm" color="primary" :label="t('admin.Set') + t('admin.Cover')"
                    @click="setCover(item)" />
                  <q-btn v-else :label="t('admin.Cover') + t('admin.Image')" size="sm" :disable="true"
                    @click="setCover(item)" />
                  <q-btn color="negative" :label="t('admin.Delete')" size="sm" @click="handleImageDelete(item)" />
                </q-btn-group>
              </div>
            </div>
          </div>
          <span v-else>
            <q-icon name="warning" />{{ t("admin.NotFound") }}</span>
        </q-card-section>
      </q-card>
    </div>

    <div class="row q-pa-md">
      <div class="col">
        <div class="text-h6">多语言信息</div>
        <q-separator />
        <q-tabs v-model="selectLang" dense align="left" active-color="primary" indicator-color="primary"
          :breakpoint="0">
          <q-tab name="default" :label="$t('themeSetting.Default')" @click="switchLanguage('default')" />
          <q-tab v-for="item in langList" :name="item.code" :label="item.name" @click="switchLanguage(item.code)" />
        </q-tabs>
      </div>
    </div>
    <div class="row q-pa-md">
      <AttrList v-if="itemDetail?.id" ref="attributeDetail" :parent-id="itemDetail.id" :language="selectLang" />
    </div>
    <div class="row q-pa-md">
      <q-card class="col">
        <q-card-section>
          <div class="text-h6">
            {{ t("admin.Product") + t("admin.Sku") + t("admin.List") }}
          </div>
        </q-card-section>
        <q-card-section>
          <SkuList v-if="itemDetail?.id" ref="productSku" :company="itemDetail.company_uuid" :parent-id="itemDetail.id"
            :code="selectLang" />
        </q-card-section>
      </q-card>
    </div>
    <div class="row q-pa-md">
      <q-card v-if="itemDetail?.id" class="col">
        <q-card-section>
          <div class="row q-my-md">
            <div class="text-h6 col" style="margin-bottom: 10px">
              {{ t("admin.Product") + t("admin.Name") }}:
              <span v-if="itemDetail.name">
                {{ itemDetail.name }}
              </span>
              <span v-else>
                <span class="no-translate">未有相关翻译</span>
              </span>
            </div>
            <div class="col-auto">
              <q-btn-group push>
                <q-btn size="sm" push label="修改翻译信息" icon="edit" color="secondary" @click="editProductLangDetail" />
              </q-btn-group>
            </div>
          </div>
          <div class="row q-ma-md">
            <div class="col-2 row-title">购买链接</div>
            <div class="col">
              {{ itemDetail.buy_link }}
              <q-btn v-if="itemDetail.buy_link" size="sm" class="q-mx-md" push
                :label="t('admin.Test') + t('admin.BuyLink')" color="positive" :title="itemDetail.buy_link"
                @click="viewBuyLink(itemDetail.buy_link)" />
            </div>
          </div>
          <div class="row q-ma-md">
            <div class="col-2 row-title">货币单位</div>
            <div class="col">
              <span v-if="itemDetail.currency">{{ itemDetail.currency }}</span>
              <span v-else>未设置</span>
            </div>
          </div>
          <div class="row q-ma-md">
            <div class="col-2 row-title">销售价格</div>
            <div class="col-8">
              <span v-if="itemDetail.sales_price" class="price">{{
                itemDetail.sales_price
              }}</span>
              <span v-else>未设置</span>
            </div>
          </div>
          <div class="row q-ma-md">
            <div class="col-2 row-title">市场价格</div>
            <div class="col-8">
              <span v-if="itemDetail.market_price" class="price">{{
                itemDetail.market_price
              }}</span>
              <span v-else>未设置</span>
            </div>
          </div>
          <div class="row q-ma-md">
            <div class="col-2 row-title">产品原产地</div>
            <div class="col">
              <span v-if="itemDetail.source_area">
                {{ itemDetail.source_area }}
              </span>
              <span v-else>
                <span class="no-translate">未有相关翻译</span>
              </span>
            </div>
          </div>
          <div class="row q-ma-md">
            <div class="col-2 row-title">产品关键词</div>
            <div class="col">
              <span v-if="itemDetail.seo_kw">
                {{ itemDetail.seo_kw }}
              </span>
              <span v-else>
                <span class="no-translate">未有相关翻译</span>
              </span>
            </div>
          </div>
          <div class="row q-ma-md">
            <div class="col-2 row-title">产品简述</div>
            <div class="col">
              <span v-if="itemDetail.desc">
                {{ itemDetail.desc }}
              </span>
              <span v-else>
                <span class="no-translate">未有相关翻译</span>
              </span>
            </div>
          </div>
          <div class="row q-ma-md">
            <div class="col-2 row-title">产品备注</div>
            <div class="col">
              <span v-if="itemDetail.remark">
                {{ itemDetail.remark }}
              </span>
              <span v-else>
                <span class="no-translate">未有相关翻译</span>
              </span>
            </div>
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row">
            <q-tabs v-model="tab" dense class="text-grey" active-color="primary" indicator-color="primary"
              align="justify" narrow-indicator>
              <q-tab name="content" :label="t('admin.Content')" />
              <q-tab name="specification" :label="t('admin.Specification')" />
            </q-tabs>
          </div>
          <div class="row">
            <q-tab-panels v-model="tab" animated>
              <q-tab-panel name="content">
                <div class="row">
                  <div v-if="itemDetail?.content" v-html="itemDetail.content" />
                  <span v-else>
                    <q-icon name="warning" />{{ t("admin.NotFound") }}</span>
                </div>
              </q-tab-panel>

              <q-tab-panel name="specification">
                <div v-if="itemDetail?.content" v-html="itemDetail.specification" />
                <span v-else>
                  <q-icon name="warning" />{{ t("admin.NotFound") }}</span>
              </q-tab-panel>
            </q-tab-panels>
          </div>
        </q-card-section>
      </q-card>
    </div>
    <SelectUpload v-if="itemDetail?.id" ref="uploadImage" :url="url.imgUpload" :data-type="'productImage'" :max-size="3"
      :multiple="true" :parent-id="itemDetail.id" @finishUpload="handleImageList(route.query.id)" />
  </BaseContent>
</template>

<script setup>
import { Dialog, Notify } from "quasar";
import { deleteAction, postAction } from "src/api/manage.ts";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import ImagePreview from "src/components/ImgPreview/index.vue";
import SelectUpload from "src/components/SelectUpload/index.vue";
import SkuList from "src/pages/product/Sku/index.vue";
import AttrList from "src/pages/product/modules/attributesList.vue";
import { useSettingStore } from "src/stores/settings";
import { FormatTimeStamp } from "src/utils/date";
import { computed, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute, useRouter } from "vue-router";

const langList = computed(() => {
  return useSettingStore().GetLanguageList();
});
const selectLang = ref("default");
const tab = ref("content");
const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const url = {
  get: "/api/business/product/lang",
  create: "/api/business/product",
  edit: "/api/business/product",
  delete: "/api/business/product",
  attrList: "/api/business/productAttributes/list",
  attr: "/api/business/productAttributes",
  skuList: "/api/business/productSku/list",
  setCover: "/api/business/productImage/cover",
  imgUpload: "/api/business/productImage",
  imgList: "/api/business/productImage/list",
};

const itemDetail = ref();
const imgList = ref();
const currentImg = ref();

onMounted(async () => {
  if (route.query.id) {
    await handleDetail(route.query.id);
    await handleImageList(route.query.id);
    await freshSkuData();
    await freshAttrData();
  } else {
    itemDetail.value = {};
    Notify.create({
      type: "warning",
      message: "信息查询失败，请重试",
      position: "top-right",
    });
  }
});

const showDateTime = computed(() => {
  return (datetime) => {
    return FormatTimeStamp(datetime);
  };
});

const switchLanguage = async (item) => {
  selectLang.value = item;
  await handleDetail(route.query.id);
  await freshSkuData();
  await freshAttrData();
};

const handleDetail = async (id) => {
  const { code, data } = await postAction(url.get, {
    id: Number(id),
    code: selectLang.value,
  });
  if (code === 200) {
    itemDetail.value = data;
    Notify.create({
      type: "positive",
      message: "信息查询成功",
      position: "top-right",
    });
  } else {
    itemDetail.value = {};
    Notify.create({
      type: "warning",
      message: "信息查询失败，请重试",
      position: "top-right",
    });
  }
};

const handleImageList = async (id) => {
  const { code, data } = await postAction(url.imgList, {
    parent_id: Number(id),
    sort_by: "id",
  });
  if (code === 200) {
    imgList.value = data.data;
    currentImg.value = data.data[0];
  } else {
    imgList.value = {};
  }
};

const changeImg = async (row) => {
  currentImg.value = row;
};

const setCover = async (item) => {
  Dialog.create({
    title: t("admin.Confirm"),
    message: t("admin.Confirm") + t("admin.Set") + t("admin.Cover") + "?",
    persistent: false,
    ok: {
      push: true,
      color: "negative",
      label: t("admin.Confirm"),
    },
  }).onOk(async () => {
    const { code, message } = await postAction(url.setCover, {
      id: item.id,
      parent_id: item.parent_id,
    });
    if (code === 200) {
      Notify.create({
        type: "positive",
        message: message,
        position: "top-right",
      });
      await handleImageList(route.query.id);
    } else {
      Notify.create({
        type: "negative",
        message: message,
        position: "top-right",
      });
    }
  });
};

const handleImageDelete = async (item) => {
  Dialog.create({
    title: t("admin.Confirm"),
    message: t("admin.Confirm") + t("admin.Delete") + t("admin.Image") + "?",
    persistent: true,
    ok: {
      push: true,
      color: "negative",
      label: t("admin.Confirm"),
    },
  }).onOk(async () => {
    const { code, message } = await deleteAction(url.imgUpload, {
      id: item.id,
    });
    if (code === 200) {
      Notify.create({
        type: "positive",
        message: message,
        position: "top-right",
      });
      await handleImageList(route.query.id);
    }
  });
};

const uploadImage = ref();
const showUploader = () => {
  uploadImage.value.show();
};

const productSku = ref();
const freshSkuData = async () => {
  await productSku.value.freshData(selectLang.value);
};

const addSkuForm = async () => {
  productSku.value.showAddForm();
};

const attributeDetail = ref();
const freshAttrData = async () => {
  await attributeDetail.value.freshData(selectLang.value);
};

const editProductDetail = () => {
  router.push({ name: "ProductEdit", query: { id: itemDetail.value.id } });
};

const editProductLangDetail = () => {
  router.push({
    name: "ProductLangEdit",
    query: { id: itemDetail.value.id, code: selectLang.value },
  });
};

const viewBuyLink = (link) => {
  window.open(link, "_blank");
};
</script>

<style scoped lang="scss">
.product-detail {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;

  .title-place {
    font-weight: 600;
    color: rgb(58, 111, 204);
  }

  .product-detail-row {
    padding: 5px;
  }

  .product-detail-label:after {
    content: "=";
    display: inline-block;
    padding: 0 12px;
    color: #26ceba;
  }

  .product-detail-value:before {
    content: "( ";
    color: #ffc069;
  }

  .product-detail-value:after {
    content: " )";
    color: #ffc069;
  }
}

.attr-label:after {
  content: "=";
  display: inline-block;
  padding: 0 12px;
  color: #26ceba;
}

.filename-btn {
  font-size: 12px;
  overflow: hidden;
  max-width: 220px;
}

.no-translate {
  color: #e67e23;
  font-weight: 600;
}

.price {
  font-size: 18px;
  color: #e67e23;
  font-weight: 600;
}

:deep(img) {
  max-width: 100%;
}
</style>
