<template>
  <base-content scrollable padding>
    <div class="row q-ma-sm">
      <div class="col">
        <div class="row q-mb-sm">
          <q-input
            v-model="groupInput"
            style="width: 20%"
            class="q-mx-sm"
            :label="t('admin.ApiGroup')"
          />
          <q-input
            v-model="queryParams.method"
            style="width: 20%"
            class="q-mx-sm"
            :label="t('admin.Api') + t('admin.Method')"
          />
          <q-btn-group>
            <q-btn
              color="primary"
              size="md"
              :label="t('admin.Search')"
              @click="handleSearch"
            />
            <q-btn
              color="warning"
              size="md"
              :label="t('admin.Reset')"
              @click="resetSearch"
            />
          </q-btn-group>
        </div>

        <q-table
          v-model:pagination="pagination"
          row-key="id"
          separator="cell"
          :rows="tableData"
          :columns="columns"
          :rows-per-page-options="pageOptions"
          :loading="loading"
          @request="onRequest"
        >
          <template #top="props">
            <q-btn
              color="primary"
              :label="t('admin.Add') + ' ' + t('admin.Api')"
              @click="showAddForm()"
            />
            <q-space />
            <q-btn
              flat
              round
              dense
              :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
              class="q-ml-md"
              @click="props.toggleFullscreen"
            />
          </template>

          <template #body-cell-actions="props">
            <q-td :props="props">
              <div class="q-gutter-xs">
                <q-btn
                  color="primary"
                  :label="t('admin.Edit')"
                  @click="showEditForm(props.row)"
                />
                <q-btn
                  color="negative"
                  :label="t('admin.Delete')"
                  @click="handleDelete(props.row)"
                />
              </div>
            </q-td>
          </template>
        </q-table>
        <RecordDetail ref="recordDetailDialog" @handleFinish="handleFinish" />
      </div>
    </div>
  </base-content>
</template>

<script setup>
defineOptions({ name: "MenuList" });
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import useTableData from "src/composables/useTableData";
import RecordDetail from "src/pages/permission/modules/recordDetail.vue";
import { computed, onMounted } from "vue";
import { useI18n } from "vue-i18n";
const url = {
  list: "/api/permission/list",
  item: "/api/permission",
};

const {
  pagination,
  queryParams,
  pageOptions,
  loading,
  tableData,
  recordDetailDialog,
  showAddForm,
  showEditForm,
  onRequest,
  getTableData,
  handleSearch,
  resetSearch,
  handleFinish,
  handleDelete,
} = useTableData(url);

onMounted(async () => {
  pagination.value.sortBy = "id";
  await getTableData();
});

const { t } = useI18n();

const columns = computed(() => {
  return [
    { name: "name", align: "center", label: t("admin.Name"), field: "name" },
    {
      name: "group",
      align: "center",
      label: t("admin.Api") + t("admin.Group"),
      field: "group",
    },
    {
      name: "method",
      align: "center",
      label: t("admin.Api") + t("admin.Method"),
      field: "method",
    },
    {
      name: "path",
      align: "center",
      label: t("admin.Api") + t("admin.Path"),
      field: "path",
    },
    {
      name: "backup",
      align: "center",
      label: t("admin.Backup"),
      field: "backup",
    },
    {
      name: "actions",
      align: "center",
      label: t("admin.Actions"),
      field: "actions",
    },
  ];
});

const groupInput = computed({
  get: () => {
    return queryParams.value.group?.replace(/^'|'$/g, '') || ''
  },
  set: (val) => {
    queryParams.value.group = val ? `'${val}'` : ''
  }
})
</script>
