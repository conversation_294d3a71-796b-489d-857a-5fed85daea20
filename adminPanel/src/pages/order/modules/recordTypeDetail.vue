<template>
  <q-dialog
    v-model="recordDetailVisible"
    position="right"
    persistent
    :allow-focus-outside="true"
  >
    <q-card style="width: 80vw; max-width: 80vw">
      <q-card-section>
        <div class="text-h6">
          {{ formTypeName }} {{ $t("router.Navigator") }}:
          {{ recordDetail.value.name }}
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-form ref="recordDetailForm">
          <q-form ref="recordDetailForm">
            <div class="row">
              <q-input
                v-model="recordDetail.value.nav_type"
                class="col q-mx-sm"
                :label="t('admin.Type')"
              />
            </div>
            <div class="row q-my-md">
              <q-tabs
                v-model="tab"
                dense
                class="text-grey"
                active-color="primary"
                indicator-color="primary"
                align="justify"
                narrow-indicator
              >
                <q-tab name="default" :label="$t('themeSetting.Default')" />
                <q-tab
                  v-for="item in langList"
                  :name="item.code"
                  :label="item.name"
                />
              </q-tabs>
            </div>

            <q-tab-panels v-model="tab" animated>
              <q-tab-panel
                v-for="item in recordDetail.value.lang"
                :name="item.code"
              >
                <div class="row">
                  <q-input
                    v-model="item.name"
                    class="col q-mx-sm"
                    :label="t('admin.Name')"
                  />
                </div>
              </q-tab-panel>
            </q-tab-panels>
          </q-form>
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn
          :label="t('admin.Save')"
          color="primary"
          @click="handleCreateAction"
        />
        <q-btn v-close-popup :label="t('admin.Cancel')" color="negative" />
      </q-card-actions>

      <q-inner-loading :showing="loading">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { useQuasar } from "quasar";
import useRecordDetail from "src/composables/useRecordDetail";
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";
import { postAction, putAction } from "../../../api/manage";
import { useSettingStore } from "../../../stores/settings";

const $q = useQuasar();
const { t } = useI18n();
const emit = defineEmits(["handleFinish"]);
const url = {
  list: "/api/website/navigationType/list",
  item: "/api/website/navigationType",
  create: "/api/website/navigationType",
  edit: "/api/website/navigationType",
  delete: "/api/website/navigationType",
};
const langList = computed(() => {
  return useSettingStore().GetLanguageList();
});

const {
  formType,
  formTypeName,
  recordDetail,
  recordDetailVisible,
  loading,
  recordDetailForm,
} = useRecordDetail(url, emit);
const tab = ref("default");
const lang = ref([]);

const show = async (row) => {
  tab.value = "default";
  lang.value = [];
  loading.value = true;
  if (formType.value === "add") {
    recordDetail.value = {
      is_show: true,
      name: "",
      nav_type: "",
      stable: false,
    };
    lang.value.push({ code: "default", name: "" });
    langList.value.map((item) => {
      lang.value.push({ code: item.code, name: "" });
    });
    recordDetail.value.lang = lang;
  } else {
    recordDetail.value = row;
    if (!recordDetail.value.lang) {
      langList.value.map((item) => {
        lang.value.push({ code: item.code, name: "" });
      });
      recordDetail.value.lang = lang.value;
    } else {
      lang.value = recordDetail.value.lang;
      langList.value.map((item) => {
        let check = false;
        recordDetail.value.lang.map((i) => {
          if (i.code === item.code) {
            check = true;
          }
        });
        if (!check) {
          lang.value.push({ code: item.code, name: "" });
        }
      });
      recordDetail.value.lang = lang.value;
    }
    recordDetail.value.lang.unshift({
      code: "default",
      name: recordDetail.value.name,
    });
  }
  recordDetailVisible.value = true;
  loading.value = false;
};

const handleCreateAction = async () => {
  const success = await recordDetailForm.value.validate();
  if (success) {
    if (formType.value === "edit") {
      if (url === undefined || !url.edit) {
        $q.notify({
          type: "negative",
          message: "请先配置url",
        });
        return;
      }
      const { code, msg } = await putAction(url.create, recordDetail.value);
      if (code === 200) {
        $q.notify({
          type: "positive",
          message: msg,
        });
      }
      emit("handleFinish");
      recordDetailVisible.value = false;
    } else if (formType.value === "add") {
      if (url === undefined || !url.create) {
        $q.notify({
          type: "negative",
          message: "请先配置url",
        });
        return;
      }
      const { code, msg } = await postAction(url.create, recordDetail.value);
      if (code === 200) {
        $q.notify({
          type: "positive",
          message: msg,
        });
      }
      recordDetailVisible.value = false;
    } else {
      $q.notify({
        type: "negative",
        message: t("CanNotAddOrEdit"),
      });
    }
    emit("handleFinish");
  } else {
    $q.notify({
      type: "negative",
      message: "请检查表单信息是否正确",
    });
  }
};

defineExpose({
  show,
  formType,
});
</script>
