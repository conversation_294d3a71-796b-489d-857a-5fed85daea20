<template>
  <BaseContent scrollable>
    <div v-if="itemDetail?.id" class="q-pa-md">
      <!-- 订单标题 -->
      <div class="row q-ma-sm">
        <div class="text-h5 col" style="margin-bottom: 10px">
          订单详情 - {{ itemDetail.serial }}
          <q-chip square outline text-color="white" :color="getStatusColor(itemDetail.status)">
            {{ getStatusText(itemDetail.status) }}
          </q-chip>
        </div>
      </div>

      <!-- 基本信息 -->
      <div class="row q-mb-md">
        <div class="col-12">
          <q-card>
            <q-card-section>
              <div class="text-h6 q-mb-md q-mt-lg">基本信息</div>
              <div class="row q-my-md">
                <div class="col-3">
                  <div class="row">
                    <div class="col-4 row-title">订单编号</div>
                    <div class="col-8">{{ itemDetail.serial }}</div>
                  </div>
                </div>
                <div class="col-3">
                  <div class="row">
                    <div class="col-4 row-title">平台名称</div>
                    <div class="col-8">{{ itemDetail.platform_name || '-' }}</div>
                  </div>
                </div>
                <div class="col-3">
                  <div class="row">
                    <div class="col-4 row-title">平台订单号</div>
                    <div class="col-8">{{ itemDetail.platform_order_serial || '-' }}</div>
                  </div>
                </div>
                <div class="col-3">
                  <div class="row">
                    <div class="col-4 row-title">导入批次</div>
                    <div class="col-8">{{ itemDetail.import_record || '-' }}</div>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>

      <!-- 客户信息 -->
      <div class="row q-mb-md">
        <div class="col-6 q-px-sm">
          <q-card>
            <q-card-section>
              <div class="text-h6 q-mb-md q-mt-lg">客户信息</div>
              <div class="row q-my-md">
                <div class="col-6">
                  <div class="row">
                    <div class="col-4 row-title">客户姓名</div>
                    <div class="col-8">{{ itemDetail.customer || '-' }}</div>
                  </div>
                </div>
                <div class="col-6">
                  <div class="row">
                    <div class="col-4 row-title">客户电话</div>
                    <div class="col-8">{{ itemDetail.customer_phone || '-' }}</div>
                  </div>
                </div>
              </div>
              <div class="row q-my-md">
                <div class="col-6">
                  <div class="row">
                    <div class="col-4 row-title">收货电话</div>
                    <div class="col-8">{{ itemDetail.receive_phone || '-' }}</div>
                  </div>
                </div>
                <div class="col-6">
                  <div class="row">
                    <div class="col-4 row-title">收货地址</div>
                    <div class="col-8">{{ itemDetail.address || '-' }}</div>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- 金额信息 -->
        <div class="col-6 q-px-sm">
          <q-card>
            <q-card-section>
              <div class="text-h6 q-mb-md q-mt-lg">金额信息</div>
              <div class="row q-my-md">
                <div class="col-6">
                  <div class="row">
                    <div class="col-4 row-title">订单金额</div>
                    <div class="col-8">¥{{ formatAmount(itemDetail.amount) }}</div>
                  </div>
                </div>
                <div class="col-6">
                  <div class="row">
                    <div class="col-4 row-title">快递费用</div>
                    <div class="col-8">¥{{ formatAmount(itemDetail.express_fee) }}</div>
                  </div>
                </div>
              </div>
              <div class="row q-my-md">
                <div class="col-6">
                  <div class="row">
                    <div class="col-4 row-title">平台费用</div>
                    <div class="col-8">¥{{ formatAmount(itemDetail.platform_fee_total) }}</div>
                  </div>
                </div>
                <div class="col-6">
                  <div class="row">
                    <div class="col-4 row-title">总支付金额</div>
                    <div class="col-8 text-weight-bold text-primary">¥{{ formatAmount(itemDetail.total_payment) }}</div>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>

      <!-- 支付和物流信息 -->
      <div class="row q-mb-md">
        <div class="col-6 q-px-sm">
          <q-card>
            <q-card-section>
              <div class="text-h6 q-mb-md q-mt-lg">支付信息</div>
              <div class="row q-my-md">
                <div class="col-6">
                  <div class="row">
                    <div class="col-4 row-title">支付方式</div>
                    <div class="col-8">{{ itemDetail.pay_type || '-' }}</div>
                  </div>
                </div>
                <div class="col-6">
                  <div class="row">
                    <div class="col-4 row-title">支付时间</div>
                    <div class="col-8">{{ formatDateTime(itemDetail.pay_time) }}</div>
                  </div>
                </div>
              </div>
              <div class="row q-my-md">
                <div class="col-12">
                  <div class="row">
                    <div class="col-2 row-title">支付信息</div>
                    <div class="col-10">{{ itemDetail.pay_info || '-' }}</div>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- 物流信息 -->
        <div class="col-6 q-px-sm">
          <q-card>
            <q-card-section>
              <div class="text-h6 q-mb-md q-mt-lg">物流信息</div>
              <div class="row q-my-md">
                <div class="col-6">
                  <div class="row">
                    <div class="col-4 row-title">快递公司</div>
                    <div class="col-8">{{ itemDetail.express_company || '-' }}</div>
                  </div>
                </div>
                <div class="col-6">
                  <div class="row">
                    <div class="col-4 row-title">快递单号</div>
                    <div class="col-8">{{ itemDetail.express_order || '-' }}</div>
                  </div>
                </div>
              </div>
              <div class="row q-my-md">
                <div class="col-6">
                  <div class="row">
                    <div class="col-4 row-title">发货时间</div>
                    <div class="col-8">{{ formatDateTime(itemDetail.delivery_time) }}</div>
                  </div>
                </div>
                <div class="col-6">
                  <div class="row">
                    <div class="col-4 row-title">签收时间</div>
                    <div class="col-8">{{ formatDateTime(itemDetail.sign_time) }}</div>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>

      <!-- 订单商品明细 -->
      <div class="row q-mb-md">
        <div class="col-12">
          <q-card>
            <q-card-section>
              <div class="text-h6 q-mb-md q-mt-lg">订单商品明细</div>
              <q-table :rows="infoList" :columns="columns" row-key="id" separator="cell" dense flat :loading="loading"
                :pagination="{ rowsPerPage: 0 }" hide-pagination>
                <template #body-cell-total_sales_price="props">
                  <q-td :props="props">
                    ¥{{ formatAmount(props.row.total_sales_price) }}
                  </q-td>
                </template>

                <template #body-cell-total_cost_price="props">
                  <q-td :props="props">
                    ¥{{ formatAmount(props.row.total_cost_price) }}
                  </q-td>
                </template>

                <template #body-cell-sales_price="props">
                  <q-td :props="props">
                    ¥{{ formatAmount(props.row.sales_price) }}
                  </q-td>
                </template>

                <template #body-cell-cost_price="props">
                  <q-td :props="props">
                    ¥{{ formatAmount(props.row.cost_price) }}
                  </q-td>
                </template>

                <template #body-cell-platform_fee="props">
                  <q-td :props="props">
                    ¥{{ formatAmount(props.row.platform_fee) }}
                  </q-td>
                </template>

                <template #body-cell-discount="props">
                  <q-td :props="props">
                    ¥{{ formatAmount(props.row.discount) }}
                  </q-td>
                </template>
              </q-table>
            </q-card-section>
          </q-card>
        </div>
      </div>
    </div>
  </BaseContent>
</template>

<script setup>
import { Notify } from "quasar";
import { getActionByPath, postAction } from "src/api/manage.ts";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import { FormatTimeStamp } from "src/utils/date";
import { computed, onMounted, ref } from "vue";
import { useRoute } from "vue-router";

const route = useRoute();
const url = {
  item: "/api/sales_order",
  info: "/api/sales_order_info/list",
};

const itemDetail = ref({});
const infoList = ref([]);
const loading = ref(false);

// 订单商品明细表格列定义
const columns = computed(() => {
  return [
    { name: "product_serial", align: "center", label: "商品编号", field: "product_serial" },
    { name: "product_name", align: "center", label: "商品名称", field: "product_name" },
    { name: "product_model", align: "center", label: "商品型号", field: "product_model" },
    { name: "product_type", align: "center", label: "商品类型", field: "product_type" },
    { name: "quantity", align: "center", label: "数量", field: "quantity" },
    { name: "sales_price", align: "center", label: "销售单价", field: "sales_price" },
    { name: "cost_price", align: "center", label: "成本单价", field: "cost_price" },
    { name: "total_sales_price", align: "center", label: "销售总价", field: "total_sales_price" },
    { name: "total_cost_price", align: "center", label: "成本总价", field: "total_cost_price" },
    { name: "platform_fee", align: "center", label: "平台费用", field: "platform_fee" },
    { name: "discount", align: "center", label: "折扣", field: "discount" },
    { name: "express_company", align: "center", label: "快递公司", field: "express_company" },
    { name: "express_order", align: "center", label: "快递单号", field: "express_order" },
    { name: "system_remark", align: "center", label: "系统备注", field: "system_remark" },
  ];
});

// 状态字典
const statusDict = [
  { value: "draft", label: "草稿", color: "grey" },
  { value: "new", label: "新建", color: "blue" },
  { value: "processing", label: "处理中", color: "orange" },
  { value: "shipped", label: "已发货", color: "purple" },
  { value: "delivered", label: "已送达", color: "teal" },
  { value: "completed", label: "已完成", color: "green" },
  { value: "cancelled", label: "已取消", color: "red" },
];

onMounted(async () => {
  if (route.query.id) {
    await handleDetail();
    await getInfoList();
  } else {
    itemDetail.value = {};
    Notify.create({
      type: "warning",
      message: "信息查询失败，请重试",
      position: "top-right",
    });
  }
});

// 获取销售订单详情
const handleDetail = async () => {
  try {
    loading.value = true;
    const { code, data } = await getActionByPath(url.item, [route.query.id]);
    if (code === 200) {
      itemDetail.value = data;
      Notify.create({
        type: "positive",
        message: "订单信息加载成功",
        position: "top-right",
      });
    } else {
      itemDetail.value = {};
      Notify.create({
        type: "warning",
        message: "订单信息查询失败，请重试",
        position: "top-right",
      });
    }
  } catch (error) {
    console.error('获取订单详情失败:', error);
    itemDetail.value = {};
    Notify.create({
      type: "negative",
      message: "订单信息加载失败",
      position: "top-right",
    });
  } finally {
    loading.value = false;
  }
};

// 获取销售订单商品明细
const getInfoList = async () => {
  try {
    const { code, data } = await postAction(url.info, {
      params: [{ var: "order_serial", val: itemDetail.value.platform_order_serial }],
      page: {
        page: 1,
        limit: 0,
      },
    });
    if (code === 200) {
      infoList.value = data.data || [];
    } else {
      infoList.value = [];
      Notify.create({
        type: "warning",
        message: "商品明细查询失败",
        position: "top-right",
      });
    }
  } catch (error) {
    console.error('获取商品明细失败:', error);
    infoList.value = [];
  }
};

// 获取状态颜色
const getStatusColor = (status) => {
  const statusItem = statusDict.find((item) => item.value === status);
  return statusItem ? statusItem.color : "grey";
};

// 获取状态文本
const getStatusText = (status) => {
  const statusItem = statusDict.find((item) => item.value === status);
  return statusItem ? statusItem.label : (status || '未知');
};

// 格式化金额
const formatAmount = (amount) => {
  if (!amount) return "0";
  return Number(amount).toLocaleString();
};

// 格式化日期时间
const formatDateTime = (datetime) => {
  if (!datetime) return '-';
  return FormatTimeStamp(datetime);
};
</script>

<style scoped lang="scss">
.row-title {
  font-weight: 600;
  color: #666;
  padding: 8px 0;
}

.text-h6 {
  color: #1976d2;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 8px;
}

.q-card {
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.text-primary {
  color: #1976d2 !important;
}

.text-weight-bold {
  font-weight: 600;
}
</style>
