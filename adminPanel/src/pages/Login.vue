<template>
  <div class="flex justify-center items-center" style="height: 100%">
    <corner-bottom class="wave fit bg-black" />
    <div class="col-6 flex justify-center items-center" v-show="$q.screen.gt.sm">
      <q-skeleton type="text" height="70%" width="50%" v-if="!isLottieFinished" />
      <LottieWeb :path="defaultOptions.path" @isLottieFinish="handleLottieFinish" />
    </div>

    <q-card flat class="row" style="border-radius: 20px; width: 85%; max-width: 400px">
      <div class="absolute-top-left q-pa-md">
        <dark-mode />
      </div>
      <div class="col flex justify-center items-center q-pa-md">
        <q-card-section>
          <ToolbarTitle title="CMS管理后台" size="20px" />
        </q-card-section>
        <q-card-section align="center" class="fit q-gutter-y-sm">
          <q-form ref="loginForm" class="custom-form-error-message">
            <q-input v-model.trim="loginData.username" placeholder="请输入账号" dense clearable outlined no-error-icon
              lazy-rules :rules="[(val) => !!val || '请输入账号']" />
            <q-input v-model="loginData.password" placeholder="请输入密码" dense outlined no-error-icon
              :type="isPwd ? 'password' : 'text'" lazy-rules :rules="[(val) => !!val || '请输入密码']">
              <template v-slot:append>
                <q-icon :name="isPwd ? 'visibility_off' : 'visibility'" class="cursor-pointer"
                  @click="isPwd = !isPwd" />
              </template>
            </q-input>
            <q-input v-model.trim="loginData.captcha_str" :disable="loading" outlined dense no-error-icon
              :placeholder="$t('router.Captcha')" :rules="[
                (val) => (val && val.length > 0) || $t('router.NeedInput'),
              ]">
              <template #after>
                <q-btn padding="none" style="width: 120px; height: 100%" @click="getCaptcha">
                  <q-img :src="captchaImage" />
                </q-btn>
              </template>
            </q-input>
            <div class="column q-gutter-y-md q-mt-none">
              <q-checkbox v-model="rememberMe" :disable="loading" :label="$t('router.RememberMe')" dense
                @update:model-value="changeRememberMe" />
            </div>
            <q-btn class="full-width" size="1.2em" rounded unelevated color="primary" :loading="loading"
              @click="onSubmit">
              登入
            </q-btn>
            <q-banner v-if="message !== ''" inline-actions dense rounded class="text-red">
              {{ message }}
            </q-banner>
          </q-form>
          <q-separator spaced="lg" />
          <q-card-section class="text-caption text-grey-7">
            <span>some descriptions</span>
          </q-card-section>
        </q-card-section>
      </div>
    </q-card>
  </div>
</template>

<script setup>
// Import necessary libraries and components
import { QForm } from "quasar";
import { getAction } from "src/api/manage.ts";
import CornerBottom from "src/components/Login/CornerBottom.vue";
import LottieWeb from "src/components/LottieWeb/LottieWeb.vue";
import { useUserStore } from "src/stores/user";
import { onMounted, reactive, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
// Define component options
defineOptions({ name: "Login" });

// Setup user store, router, and message reference
const router = useRouter();
const route = useRoute();
const userStore = useUserStore();
const message = ref("");
const rememberMe = ref(true);
const captchaImage = ref("");
const loading = ref(false);
const isPwd = ref(true);
const loginForm = ref(null);

// Lottie configuration
const isLottieFinished = ref(false);
const defaultOptions = ref({
  path: "https://assets1.lottiefiles.com/packages/lf20_gzl797gs.json",
  loop: true,
  animationSpeed: 1,
});
const handleLottieFinish = () => {
  isLottieFinished.value = true;
};

// Login data and configuration
const loginData = reactive({
  username: "",
  password: "",
  captcha_str: "",
  captcha_id: "",
});
const getCaptcha = async () => {
  const { code, data } = await getAction("/public/captcha", "");
  console.log(data);
  if (code === 200) {
    captchaImage.value = data?.captcha_img;
    loginData.captcha_id = data.captcha_id;
  }
};
const changeRememberMe = (value) => {
  userStore.ChangeRememberMe(value);
};

onMounted(() => {
  getCaptcha();
});

const onSubmit = async () => {
  loading.value = true;
  const res = await userStore.HandleLogin(loginData);
  if (res) {
    loading.value = false;
    await router.push(route.query.redirect || "/");
    loginData.captcha = "";
    loading.value = false;
  } else {
    loginData.captcha = "";
    loading.value = false;
    await getCaptcha();
  }
};
</script>

<style scoped>
.wave {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: -1;
}
</style>
