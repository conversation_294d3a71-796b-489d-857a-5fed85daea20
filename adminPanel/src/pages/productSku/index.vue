<template>
  <BaseContent scrollable>
    <div class="row q-ma-md">
      <div class="col">
        <q-table v-model:pagination="pagination" row-key="id" separator="cell" :rows="tableData" :columns="columns"
          :rows-per-page-options="pageOptions" :loading="loading" @request="onRequest">
          <template #top="props">
            <q-btn color="primary" :label="t('admin.Add') + t('admin.Information')" @click="createArticleDraft" />
            <q-space />
            <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'" class="q-ml-md"
              @click="props.toggleFullscreen" />
          </template>

          <template #body-cell-fix_top="props">
            <q-td :props="props">
              <q-chip v-if="props.row.fix_top" square outline color="positive" label="是" size="sm" />
              <q-chip v-else square outline label="否" size="sm" />
            </q-td>
          </template>

          <template #body-cell-category="props">
            <q-td :props="props">
              <q-chip v-if="props.row.category_id" square outline color="positive" size="sm"
                :label="props.row.category.name" />
              <q-chip v-else square outline size="sm" label="未分配" />
            </q-td>
          </template>

          <template #body-cell-weight="props">
            <q-td :props="props">
              <span class="weight-size">{{ props.row.weight }}</span>
            </q-td>
          </template>

          <template #body-cell-created_at="props">
            <q-td :props="props">
              {{ showDateTime(props.row.created_at) }}
            </q-td>
          </template>

          <template #body-cell-actions="props">
            <q-td :props="props">
              <div class="q-gutter-xs">
                <q-btn-group>
                  <q-btn color="primary" size="xs" :label="t('admin.View')" @click="handleDetail(props.row)" />
                  <q-btn v-if="props.row.fix_top" color="warning" :label="t('admin.Cancel')" size="xs"
                    @click="handleDetailStatus(props.row)" />
                  <q-btn v-else color="positive" :label="t('admin.FixTop')" size="xs"
                    @click="handleDetailStatus(props.row)" />
                  <q-btn color="negative" size="xs" :label="t('admin.Delete')" @click="handleDelete(props.row)" />
                </q-btn-group>
              </div>
            </q-td>
          </template>
        </q-table>
      </div>
    </div>
  </BaseContent>
</template>

<script setup>
import { useQuasar } from "quasar";
import { putAction, postAction } from "src/api/manage.ts";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import useTableData from "src/composables/useTableData";
import { FormatDateTime } from "src/utils/date";
import { computed, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";

const router = useRouter();
const $q = useQuasar();
const { t } = useI18n();
const url = {
  list: "/api/article/list",
  create: "/api/article/draft",
  delete: "/api/article",
  status: "/api/article/status",
};
const columns = computed(() => {
  return [
    {
      name: "title",
      required: true,
      align: "left",
      label: t("admin.Title"),
      field: "title",
    },
    {
      name: "category",
      required: true,
      align: "center  ",
      label: t("admin.Category"),
      field: "category",
    },
    {
      name: "fix_top",
      align: "center",
      label: t("admin.Category") + t("admin.FixTop"),
      field: "fix_top",
    },
    {
      name: "weight",
      align: "center",
      label: t("admin.Category") + t("admin.Sort"),
      field: "weight",
    },
    {
      name: "created_at",
      align: "center",
      label: t("admin.CreatedAt"),
      field: "created_at",
    },
    {
      name: "actions",
      required: true,
      align: "center",
      label: t("admin.Actions"),
      field: "actions",
    },
  ];
});
const {
  pagination,
  queryParams,
  pageOptions,
  loading,
  tableData,
  onRequest,
  getTableData,
  handleFinish,
  handleDelete,
} = useTableData(url);

onMounted(() => {
  pagination.value.order = "created_at";
  pagination.value.sort = "desc";
  getTableData();
});

const handleDetail = (item) => {
  router.push({ name: "CompanyInfoDetail", query: { id: item.id } });
};
const createArticleDraft = async () => {
  try {
    const { code, data, msg } = await postAction(url.create, {});
    if (code === 200 && data) {
      router.push({
        name: 'articleCreate',
        query: { id: data }
      });
    } else {
      $q.notify({
        type: 'warning',
        message: msg || t('admin.CreateFailed')
      });
    }
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: error.message || t('admin.CreateFailed')
    });
  }
};

const itemStatus = ref();
const handleDetailStatus = async (item) => {
  itemStatus.value = !item.is_fixed;
  const { code, message } = await putAction(url.status, {
    id: item.id,
    is_fixed: itemStatus.value,
  });
  if (code === 200) {
    $q.notify({
      type: "positive",
      message: message,
    });
    await getTableData();
  } else {
    $q.notify({
      type: "warning",
      message: message,
    });
  }
};
const showDateTime = computed(() => {
  return (datetime) => {
    return FormatDateTime(datetime);
  };
});
</script>

<style lang="scss" scoped>
.weight-size {
  font-size: 18px;
  font-weight: 500;
  color: #ee9900;
}
</style>
