<template>
  <BaseContent scrollable>
    <div class="q-ma-md">
      <div class="row">
        <div class="col-9 q-mr-sm">
          <q-card v-if="itemDetail?.id">
            <q-card-section>
              <div class="row q-my-sm">
                <div class="text-h6 col" style="margin-bottom: 10px">
                  {{ t("admin.Article") + t("admin.Title") }}:
                  {{ itemDetail.title }}
                  <q-chip v-if="itemDetail.fix_top" square outline color="positive" label="目录置顶" size="sm"
                    class="q-mx-md" />
                </div>
              </div>
              <div class="row q-my-md">
                <div class="col-2 row-title">目录名称</div>
                <div class="col-auto">
                  {{ itemDetail.category.name }}
                </div>
              </div>
              <div class="row q-my-md">
                <div class="col-2 row-title">文章创建人</div>
                <div class="col-auto">
                  {{ itemDetail.creator_name }}
                </div>
              </div>
              <div class="row q-my-md">
                <div class="col-2 row-title">文章索引</div>
                <div class="col-auto">
                  {{ itemDetail.slogan }}
                </div>
              </div>
              <div class="row q-my-md">
                <div class="col-2 row-title">文章SEO关键词</div>
                <div class="col-auto">
                  {{ itemDetail.seo_kw }}
                </div>
              </div>
              <div class="row q-my-md">
                <div class="col-2 row-title">创建时间</div>
                <div class="col-auto">
                  {{ showDateTime(itemDetail.created_at) }}
                </div>
              </div>
              <div class="row q-my-md">
                <div class="col-2 row-title">更新时间</div>
                <div class="col-auto">
                  {{ showDateTime(itemDetail.updated_at) }}
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
        <div class="col q-mr-sm">
          <q-card v-if="itemDetail?.id" class="card-size">
            <q-card-section>
              <div class="row q-my-sm">
                <div class="text-h6 col">
                  {{ t("admin.Article") + t("admin.Cover") }}
                  <span>图片限制最大4mb，建议图片最好不要超过1.5mb</span>
                </div>
                <div class="col-auto">
                  <q-btn color="primary" size="sm" :label="t('admin.Change') + t('admin.Article') + t('admin.Cover')
                    " @click="showUploader" />
                </div>
              </div>
              <div class="row">
                <ImagePreview :src="itemDetail.link" :thumb-src="itemDetail.thumb_link" :height="'90'"
                  :max-width="'180'" :preview="true" class="pic-size" />
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>

      <div class="row q-pa-md">
        <div class="col">
          <div class="text-h6">多语言信息</div>
          <q-separator />
          <q-tabs v-model="tab" dense align="left" active-color="primary" indicator-color="primary" :breakpoint="0">
            <q-tab name="default" :label="$t('themeSetting.Default')" />
            <q-tab v-for="item in langList" :name="item.code" :label="item.name" />
          </q-tabs>
        </div>
      </div>
      <q-tab-panels v-if="itemDetail?.id" v-model="tab" animated class="col">
        <q-tab-panel v-for="item in itemDetail.lang" :key="item.id" :name="item.code">
          <q-btn color="primary" label="编辑内容" @click="editArticleLang(item)" />
          <q-card class="q-my-md">
            <q-card-section>
              <div class="row q-my-md">
                <div class="col-2 row-title">文章标题</div>
                <div class="col-auto">
                  {{ item.title }}
                </div>
              </div>
              <div class="row q-my-md">
                <div class="col-2 row-title">文章索引</div>
                <div class="col-auto">
                  {{ item.slogan }}
                </div>
              </div>
              <div class="row q-my-md">
                <div class="col-2 row-title">文章SEO关键词</div>
                <div class="col-auto">
                  {{ item.seo_kw }}
                </div>
              </div>
            </q-card-section>
            <q-card-section>
              <div class="text-h6">
                {{ t("admin.Article") + t("admin.Content") }}
              </div>
            </q-card-section>
            <q-card-section>
              <div v-if="item?.content" v-html="item.content" />
              <span v-else>
                <q-icon name="warning" />{{ t("admin.NotFound") }}</span>
            </q-card-section>
          </q-card>
        </q-tab-panel>
      </q-tab-panels>
    </div>
    <SelectUpload v-if="itemDetail?.id" ref="uploadImage" :url="url.imgUpload" :data-type="'article'"
      :image-type="'cover'" :multiple="true" :parent-id="itemDetail.id" @finishUpload="handleDetail(route.query.id)" />
  </BaseContent>
</template>

<script setup>
import { Notify } from "quasar";
import { deleteAction, getAction } from "src/api/manage.ts";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import ImagePreview from "src/components/ImgPreview/index.vue";
import SelectUpload from "src/components/SelectUpload/index.vue";
import { useSettingStore } from "src/stores/settings";
import { FormatTimeStamp } from "src/utils/date";
import { computed, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute, useRouter } from "vue-router";

const langList = computed(() => {
  return useSettingStore().GetLanguageList();
});
const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const url = {
  item: "/api/info/article",
  imgUpload: "/api/info/article/image",
};
const tab = ref("default");
const itemDetail = ref();

onMounted(async () => {
  if (route.query.id) {
    await handleDetail(route.query.id);
  } else {
    itemDetail.value = {};
    Notify.create({
      type: "warning",
      message: "信息查询失败，请重试",
      position: "top-right",
    });
  }
});

const showDateTime = computed(() => {
  return (datetime) => {
    return FormatTimeStamp(datetime);
  };
});

const handleDetail = async (id) => {
  const { code, data } = await getAction(url.item, { id: id });
  if (code === 200) {
    itemDetail.value = data;
    Notify.create({
      type: "positive",
      message: "信息查询成功",
      position: "top-right",
    });
  } else {
    itemDetail.value = {};
    Notify.create({
      type: "warning",
      message: "信息查询失败，请重试",
      position: "top-right",
    });
  }
};

const handleImageDelete = async (item) => {
  const { code, message } = await deleteAction(url.imgUpload, { id: item.id });
  if (code === 200) {
    Notify.create({
      type: "positive",
      message: message,
      position: "top-right",
    });
    await handleDetail(route.query.id);
  }
};

const uploadImage = ref();
const showUploader = () => {
  uploadImage.value.show();
};

const editArticleLang = (item) => {
  router.push({
    name: "CompanyInfoEdit",
    query: { id: item.id },
    state: { data: JSON.stringify(item) },
  });
};
</script>

<style scoped lang="scss">
.product-detail {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;

  .title-place {
    font-weight: 600;
    color: rgb(58, 111, 204);
  }

  .product-detail-row {
    padding: 5px;
  }

  .product-detail-label:after {
    content: "=";
    display: inline-block;
    padding: 0 12px;
    color: #26ceba;
  }

  .product-detail-value:before {
    content: "( ";
    color: #ffc069;
  }

  .product-detail-value:after {
    content: " )";
    color: #ffc069;
  }
}

.card-size {
  height: 100%;
}

.pic-size {
  width: 100%;
  height: 100%;
}

.attr-label:after {
  content: "=";
  display: inline-block;
  padding: 0 12px;
  color: #26ceba;
}

:deep(img) {
  max-width: 100%;
}
</style>
