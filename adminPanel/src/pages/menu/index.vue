<template>
  <base-content scrollable>
    <div class="q-gutter-md q-ma-md">
      <q-btn
        color="primary"
        :label="t('admin.Add') + t('admin.Root') + t('admin.Menu')"
        class="q-my-md"
        @click="showAddParentForm()"
      />
      <q-hierarchy
        v-if="tableData"
        separator="cell"
        dense
        :columns="columns"
        :data="tableData"
      >
        <template #body="props">
          <TreeTd :tree-td="props" first-td="order" class="text-center" />
          <td class="text-center">
            <q-icon :name="props.item.icon" />
          </td>
          <td class="text-center">
            {{ t("router." + props.item.title) }}
          </td>
          <td class="text-center">
            {{ props.item.name }}
          </td>
          <td class="text-center">
            {{ props.item.path }}
          </td>
          <td class="text-center">
            {{ props.item.redirect }}
          </td>
          <td class="text-center">
            {{ props.item.component }}
          </td>
          <td class="text-center">
            {{ props.item.is_link }}
          </td>
          <td class="text-center">
            {{ props.item.hidden }}
          </td>
          <td class="text-center">
            <div class="q-gutter-xs">
              <q-btn-group>
                <q-btn
                  size="md"
                  dense
                  color="primary"
                  :label="t('admin.Edit')"
                  @click="showEditForm(props.item)"
                />
                <q-btn
                  size="md"
                  dense
                  color="positive"
                  :label="
                    t('admin.Add') + t('admin.Children') + t('admin.Menu')
                  "
                  @click="showAddChildrenForm(props.item.name)"
                />
                <q-btn
                  size="md"
                  dense
                  color="negative"
                  :label="t('admin.Delete')"
                  @click="handleDelete(props.item)"
                />
              </q-btn-group>
            </div>
          </td>
        </template>
      </q-hierarchy>
      <q-card v-else class="q-py-md text-center">
        <h5>暂无目录信息</h5>
      </q-card>
      <RecordDetail ref="recordDetailDialog" @handleFinish="handleFinish" />
    </div>
  </base-content>
</template>

<script setup>
defineOptions({ name: "MenuList" });
import { useQuasar } from "quasar";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import TreeTd from "src/components/TreeTd/index.vue";
import useTableData from "src/composables/useTableData";
import RecordDetail from "src/pages/menu/modules/recordDetail.vue";
import { computed, onMounted } from "vue";
import { useI18n } from "vue-i18n";
const url = {
  list: "/api/menu/list",
  item: "/api/menu",
  create: "/api/menu",
  edit: "/api/menu",
  delete: "/api/menu",
};

const {
  pagination,
  queryParams,
  pageOptions,
  loading,
  tableData,
  recordDetailDialog,
  showAddForm,
  showEditForm,
  onRequest,
  getTableData,
  handleSearch,
  handleFinish,
  handleDelete,
} = useTableData(url);

onMounted(async () => {
  // queryParams.value.params = [
  //   {var: 'name', val: 'menuList'}
  // ]
  pagination.value.page = 1;
  pagination.value.order_by = "order";
  await getTableData();
});

const $q = useQuasar();
const { t } = useI18n();

const showAddParentForm = () => {
  recordDetailDialog.value.isParent = true;
  showAddForm();
};

const showAddChildrenForm = (name) => {
  recordDetailDialog.value.formType = "add";
  recordDetailDialog.value.isParent = false;
  recordDetailDialog.value.parentName = name;
  recordDetailDialog.value.show();
};

const columns = computed(() => {
  return [
    {
      name: "order",
      align: "center",
      label: t("admin.Order"),
      field: "order",
    },
    { name: "icon", align: "center", label: t("admin.Icon"), field: "icon" },
    {
      name: "name",
      align: "center",
      label: t("admin.Title"),
      field: "title",
    },
    { name: "name", align: "center", label: t("admin.Name"), field: "name" },
    { name: "path", align: "center", label: t("admin.Path"), field: "path" },
    {
      name: "redirect",
      align: "center",
      label: t("admin.Redirect"),
      field: "redirect",
    },
    {
      name: "component",
      align: "center",
      label: t("admin.Component"),
      field: "component",
    },
    {
      name: "is_link",
      align: "center",
      label: t("admin.IsLink"),
      field: "is_link",
    },
    {
      name: "hidden",
      align: "center",
      label: t("admin.Hidden"),
      field: "hidden",
    },
    {
      name: "actions",
      align: "center",
      label: t("admin.Actions"),
      field: "actions",
    },
  ];
});
</script>
