<template>
  <q-page padding>
    <q-card v-if="itemDetail?.name" style="width: 80vw; max-width: 80vw">
      <q-card-section>
        <div class="text-h6">
          {{ $t("Product") }}:
          {{ itemDetail.name }}
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-form ref="recordDetailForm">
          <!--          <gqa-form-top :record-detail="itemDetail" />-->
          <div class="row">
            <q-input v-model="itemDetail.name" maxlength="250" counter class="col q-mx-sm" :label="t('admin.Name')" />
          </div>
          <div class="row">
            <SelectCategory v-model:selectCategoryId="itemDetail.categoryCode"
              v-model:selectCategoryName="itemDetail.categoryName" selection="single" class="col q-mx-sm" />
            <q-input v-model.number="itemDetail.salesPrice" maxlength="250" counter class="col q-mx-sm"
              :label="$t('SalesPrice')" />
            <q-input v-model.number="itemDetail.marketPrice" maxlength="250" counter class="col q-mx-sm"
              :label="$t('MarketPrice')" />
          </div>
          <div class="row">
            <q-input v-model="itemDetail.brand" maxlength="250" counter class="col q-mx-sm" :label="$t('Brand')" />
            <q-input v-model="itemDetail.model" maxlength="250" counter class="col q-mx-sm" :label="$t('Model')" />
            <q-input v-model="itemDetail.size" maxlength="250" counter class="col q-mx-sm" :label="$t('Size')" />
          </div>
          <div class="row">
            <q-input v-model="itemDetail.barcode" maxlength="250" counter class="col q-mx-sm" :label="$t('Barcode')" />
            <q-input v-model="itemDetail.unit" maxlength="250" counter class="col q-mx-sm" :label="$t('Unit')" />
          </div>
          <q-input v-model="itemDetail.memo" maxlength="250" counter :label="$t('Memo')" class="q-mx-sm" />
          <q-input v-model="itemDetail.desc" maxlength="250" counter :label="$t('Desc')" class="q-mx-sm q-mb-md" />
          <div class="row q-my-md">
            <Tinymce :value="itemDetail.content" class="col" @getContent="getContent" />
          </div>
        </q-form>
      </q-card-section>
      <q-separator />

      <q-card-actions align="right">
        <q-btn :label="$t('Save')" color="primary" @click="handleProductAction" />
        <q-btn v-close-popup :label="$t('Cancel')" color="negative" @click="closeTab" />
      </q-card-actions>

      <q-inner-loading :showing="loading">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-page>
</template>

<script setup>
import { Notify } from "quasar";
import { getAction, putAction } from "src/api/manage.ts";
import Tinymce from "src/components/Editor/TinyMce.vue";
import SelectCategory from "src/components/SelectCategory/index.vue";
import { useTagViewStore } from "src/stores/tagView";
import { nextTick, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute, useRouter } from "vue-router";

const tabMenuStore = useTabMenuStore();
const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const url = {
  item: "system/product/v1/product",
};

const itemDetail = ref();

onMounted(() => {
  // getTableData()
  if (route.query.id) {
    handleDetail(route.query.id);
  }
});
const recordDetailForm = ref();
const handleDetail = async (id) => {
  const { code, data } = await getAction(url.item, { id: id });
  if (code === 200) {
    itemDetail.value = data;
    Notify.create({
      type: "positive",
      message: "信息查询成功",
      position: "top-right",
    });
  } else {
    itemDetail.value = {};
    Notify.create({
      type: "warning",
      message: "信息查询失败，请重试",
      position: "top-right",
    });
  }
};

const handleProductAction = async () => {
  const success = await recordDetailForm.value.validate();
  if (success) {
    console.log(itemDetail.value);
    if (url === undefined || !url.item) {
      Notify.create({
        type: "negative",
        message: "请先配置url",
      });
      return;
    }
    const res = await putAction(url.item, itemDetail.value);
    if (res.code === 200) {
      Notify.create({
        type: "positive",
        message: res.msg,
      });
      closeTab();
    }
  } else {
    Notify.create({
      type: "negative",
      message: "请检查表单信息是否正确",
    });
  }
};

const getContent = (v) => {
  itemDetail.value.content = v;
};

const closeTab = () => {
  tabMenuStore.CloseTab(route.fullPath);
  nextTick(() => {
    const currentTab = tabMenuStore.currentTab;
    router.push({ path: currentTab.path, query: currentTab.query });
  });
};
</script>

<style scoped lang="scss">
.product-detail {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;

  .title-place {
    font-weight: 600;
    color: rgb(58, 111, 204);
  }

  .product-detail-row {
    padding: 5px;
  }

  .product-detail-label:after {
    content: "=";
    display: inline-block;
    padding: 0 12px;
    color: #26ceba;
  }

  .product-detail-value:before {
    content: "( ";
    color: #ffc069;
  }

  .product-detail-value:after {
    content: " )";
    color: #ffc069;
  }
}

.attr-label:after {
  content: "=";
  display: inline-block;
  padding: 0 12px;
  color: #26ceba;
}
</style>
