<template>
  <base-content scrollable>
    <div class="row q-gutter-md q-ma-md">
      <div class="col">
        <div class="items-center row q-gutter-md" style="margin-bottom: 10px">
          <q-input v-model="queryParams.name" style="width: 20%" :label="t('admin.Name')" />
          <q-btn color="primary" :label="t('admin.Search')" @click="handleSearch" />
          <q-btn color="primary" :label="t('admin.Reset')" @click="resetSearch" />
        </div>
        <q-table v-model:pagination="pagination" row-key="id" separator="cell" :rows="tableData" :columns="columns"
          :rows-per-page-options="pageOptions" :loading="loading" @request="onRequest">
          <template #top="props">
            <q-btn color="primary" :label="t('admin.Add') + t('admin.Category')" @click="showAddForm" />
            <q-space />
            <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'" class="q-ml-md"
              @click="props.toggleFullscreen" />
          </template>

          <template #body-cell-status="props">
            <q-td :props="props">
              <q-chip v-if="props.row.status === 'on'" color="positive" class="text-white">启用</q-chip>
              <q-chip v-else>停用</q-chip>
            </q-td>
          </template>

          <template #body-cell-actions="props">
            <q-td :props="props">
              <div class="q-gutter-xs">
                <q-btn-group>
                  <q-btn size="sm" color="primary" :label="t('admin.Edit')" @click="showEditForm(props.row)" />
                  <q-btn color="negative" size="sm" :label="t('admin.Delete')" @click="handleDelete(props.row)" />
                </q-btn-group>
              </div>
            </q-td>
          </template>
        </q-table>
        <RecordDetail ref="recordDetailDialog" @handleFinish="handleFinish" />
      </div>
    </div>
  </base-content>
</template>

<script setup>
defineOptions({ name: "CategoryList" });
import { useQuasar } from "quasar";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import useTableData from "src/composables/useTableData";
import RecordDetail from "src/pages/category/modules/recordDetail.vue";
import { computed, onMounted } from "vue";
import { useI18n } from "vue-i18n";

const url = {
  list: "/api/category/list",
  create: "/api/category/create",
  edit: "/api/category/edit",
  delete: "/api/category/delete",
};

const {
  pagination,
  queryParams,
  pageOptions,
  loading,
  tableData,
  recordDetailDialog,
  showAddForm,
  showEditForm,
  onRequest,
  getTableData,
  handleSearch,
  handleFinish,
  handleDelete,
} = useTableData(url);

onMounted(async () => {
  pagination.value.order = "id";
  await getTableData();
});

const $q = useQuasar();
const { t } = useI18n();
const columns = computed(() => {
  return [
    {
      name: "name",
      align: "center",
      label: t("admin.Name"),
      field: "name",
    },
    {
      name: "description",
      align: "center",
      label: t("admin.Description"),
      field: "description",
    },
    {
      name: "status",
      align: "center",
      label: t("admin.Status"),
      field: "status",
    },
    {
      name: "actions",
      align: "center",
      label: t("admin.Actions"),
      field: "actions",
    },
  ];
});
</script>
