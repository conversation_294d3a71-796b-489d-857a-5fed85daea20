<template>
  <base-content scrollable>
    <div class="q-pa-md q-gutter-sm">
      <q-btn
        class="q-pa-lg"
        color="primary"
        label="查看合同列表"
        @click="viewContract"
      />
      <q-btn
        class="q-pa-lg"
        color="secondary"
        label="查看订单列表"
        @click="viewOrder"
      />
      <q-btn
        class="q-pa-lg"
        color="amber"
        label="导入订单"
        @click="importOrder"
      />
    </div>
  </base-content>
</template>

<script lang="ts" setup>
defineOptions({ name: 'Dashboard' })
// import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import BaseContent from 'src/components/BaseContent/BaseContent.vue'

const router = useRouter()

const viewContract = () => {
  router.push({ name: 'contractList' })
}

const viewOrder = () => {
  router.push({ name: 'orderList' })
}

const importOrder = () => {
  router.push({ name: 'importRecord' })
}

</script>
