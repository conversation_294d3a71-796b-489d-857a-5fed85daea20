<template>
  <q-page padding>
    <q-card v-if="itemDetail?.id">
      <q-card-section>
        <div class="row q-my-md">
          <div class="text-h5 col" style="margin-bottom: 10px">
            {{ $t("Product") + $t("Detail") }}:
            {{ itemDetail.name }}
            <q-chip square outline text-color="white" :color="itemDetail.status === 'on' ? 'positive' : 'negative'">
              {{ t(itemDetail.status) }}
            </q-chip>
          </div>
          <div class="col-auto">
            <q-btn-group push>
              <q-btn size="sm" push label="修改基本信息" icon="edit" color="secondary" @click="editProductDetail" />
              <q-btn size="sm" push label="添加产品属性" icon="add" color="orange" @click="showAttribute" />
              <q-btn size="sm" push label="添加SKU" icon="file_open" color="primary" @click="showAddSkuForm" />
            </q-btn-group>
          </div>
        </div>
        <div class="row q-my-md">
          <div class="col">
            <div class="row">
              <div class="col-4 row-title">创建时间</div>
              <div class="col-8">
                {{ showDateTime(itemDetail.created_at) }}
              </div>
            </div>
          </div>
          <div class="col">
            <div class="row">
              <div class="col-4 row-title">更新时间</div>
              <div class="col-8">
                {{ showDateTime(itemDetail.updated_at) }}
              </div>
            </div>
          </div>
        </div>
        <div class="row q-my-md">
          <div class="col">
            <div class="row">
              <div class="col-4 row-title">产品品牌</div>
              <div class="col-8">
                {{ itemDetail.brand }}
              </div>
            </div>
          </div>
          <div class="col">
            <div class="row">
              <div class="col-4 row-title">计量单位</div>
              <div class="col-8">
                {{ itemDetail.unit }}
              </div>
            </div>
          </div>
        </div>
        <div class="row q-my-md">
          <div class="col">
            <div class="row">
              <div class="col-4 row-title">产品规格</div>
              <div class="col-8">
                {{ itemDetail.model }}
              </div>
            </div>
          </div>
          <div class="col">
            <div class="row">
              <div class="col-4 row-title">产品尺寸</div>
              <div class="col-8">
                {{ itemDetail.unit }}
              </div>
            </div>
          </div>
        </div>
        <div class="row q-my-md">
          <div class="col">
            <div class="row">
              <div class="col-2 row-title">产品简述</div>
              <div class="col">
                {{ itemDetail.desc }}
              </div>
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card>
    <q-card class="q-my-md">
      <q-card-section>
        <div class="row">
          <div class="text-h6 col">
            {{ t("Product") + t("Image") + t("List") }}
          </div>
          <div class="col-auto">
            <q-btn color="primary" size="sm" :label="t('admin.Add') + $t('Product') + $t('Image')"
              @click="showUploader" />
          </div>
        </div>
      </q-card-section>
      <q-card-section>
        <q-table v-if="itemDetail?.attachment" grid row-key="id" :rows="itemDetail?.attachment" :hide-pagination="true">
          <template #item="props">
            <div class="q-pa-xs col-xs-6 col-sm-4 col-md-2">
              <q-card>
                <q-card-section class="text-center">
                  <ImagePreview :src="props.row.url" :height="'90'" :max-width="'180'" :preview="true" />
                </q-card-section>
                <q-card-section>
                  <q-btn-group>
                    <q-btn v-if="props.row.sort !== 999" color="primary" :label="$t('Set') + $t('Cover')" size="xs"
                      @click="setCover(props.row)" />
                    <q-btn v-else :label="$t('Cover') + $t('Image')" size="xs" :disable="true"
                      @click="setCover(props.row)" />
                    <q-btn color="negative" :label="$t('Delete')" size="xs" @click="handleImageDelete(props.row)" />
                  </q-btn-group>
                </q-card-section>
              </q-card>
            </div>
          </template>
        </q-table>
        <span v-else> <q-icon name="warning" />{{ t("admin.NotFound") }}</span>
      </q-card-section>
    </q-card>
    <q-card class="q-my-md">
      <q-card-section>
        <div class="row">
          <div class="text-h6 col">
            {{ t("Product") + t("Attribute") + t("List") }}
          </div>
          <div class="col-auto">
            <q-btn color="primary" size="sm" :label="t('admin.Add') + $t('Product') + $t('Attribute')"
              @click="showAttribute" />
          </div>
        </div>
      </q-card-section>
      <q-card-section>
        <q-table v-if="itemDetail?.attribute" row-key="id" grid :hide-pagination="true" :rows="itemDetail.attribute">
          <template #item="props">
            <div class="q-pa-xs col-xs-6 col-sm-4 col-md-4">
              <q-card>
                <q-card-section>
                  <div class="row">
                    <div class="col-3 attr-label">
                      {{ props.row.name }}
                    </div>
                    <div class="col">
                      {{ props.row.value }}
                    </div>
                    <div class="col-auto">
                      <q-btn-group push>
                        <q-btn size="xs" color="primary" icon="edit" @click="showAttribute(props.row)" />
                        <q-btn size="xs" color="negative" icon="delete" @click="deleteAttribute(props.row)" />
                      </q-btn-group>
                    </div>
                  </div>
                </q-card-section>
              </q-card>
            </div>
          </template>
        </q-table>
        <span v-else> <q-icon name="warning" />{{ t("admin.NotFound") }}</span>
      </q-card-section>
    </q-card>
    <q-card class="q-my-md">
      <q-card-section>
        <div class="text-h6">
          {{ t("Product") + t("Sku") + t("List") }}
        </div>
      </q-card-section>
      <q-card-section>
        <SkuList v-if="itemDetail?.id" ref="productSku" :company="itemDetail.company_uuid" :parent-id="itemDetail.id" />
      </q-card-section>
    </q-card>
    <q-card class="q-my-md">
      <q-card-section>
        <div class="text-h6">
          {{ t("Product") + t("Content") }}
        </div>
      </q-card-section>
      <q-card-section>
        <div v-if="itemDetail?.content" v-html="itemDetail.content" />
        <span v-else> <q-icon name="warning" />{{ t("admin.NotFound") }}</span>
      </q-card-section>
    </q-card>
    <SelectUpload v-if="itemDetail?.id" ref="uploadImage" :url="url.imgUpload" :data-type="'productImage'"
      :multiple="true" :parent-id="itemDetail.id" @finishUpload="handleDetail(route.query.id)" />
    <AttributeDetail v-if="itemDetail?.id" ref="attributeDetail" :parent-id="itemDetail.id"
      @handleFinish="handleDetail(route.query.id)" />
    <!--    <record-detail ref="recordDetailDialog" @handleFinish="handleDetail(route.query.id)" />-->
  </q-page>
</template>

<script setup>
import { Notify } from "quasar";
import { deleteAction, getAction, postAction } from "src/api/manage.ts";
import ImagePreview from "src/components/ImgPreview/index.vue";
import SelectUpload from "src/components/SelectUpload/index.vue";
import { FormatTimeStamp } from "src/utils/date.js";
import { computed, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute, useRouter } from "vue-router";
import AttributeDetail from "./modules/attributeDetail.vue";

const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const url = {
  item: "system/product/v1/product",
  attr: "system/product/v1/product/attr/list",
  skuList: "system/product/v1/productSku/list",
  setCover: "",
  imgUpload: "/system/product/v1/attachment",
};

const itemDetail = ref();

onMounted(async () => {
  console.log(route.fullPath, "当前route");
  if (route.query.id) {
    await handleDetail(route.query.id);
    await freshSkuData();
  } else {
    itemDetail.value = {};
    Notify.create({
      type: "warning",
      message: "信息查询失败，请重试",
      position: "top-right",
    });
  }
});

const showDateTime = computed(() => {
  return (datetime) => {
    return FormatTimeStamp(datetime);
  };
});

const handleDetail = async (id) => {
  const { code, data } = await getAction(url.item, { id: id });
  if (code === 200) {
    itemDetail.value = data;
    Notify.create({
      type: "positive",
      message: "信息查询成功",
      position: "top-right",
    });
  } else {
    itemDetail.value = {};
    Notify.create({
      type: "warning",
      message: "信息查询失败，请重试",
      position: "top-right",
    });
  }
};

const setCover = async (item) => {
  const { code, message } = await postAction(url.setCover, {
    id: item.id,
    parentId: item.parentId,
  });
  if (code === 200) {
    Notify.create({
      type: "positive",
      message: message,
      position: "top-right",
    });
    await handleDetail(route.query.id);
  } else {
    Notify.create({
      type: "negative",
      message: message,
      position: "top-right",
    });
  }
};

const handleImageDelete = async (item) => {
  const { code, message } = await deleteAction(url.imgUpload, { id: item.id });
  if (code === 200) {
    Notify.create({
      type: "positive",
      message: message,
      position: "top-right",
    });
    await handleDetail(route.query.id);
  }
};

const uploadImage = ref();
const showUploader = () => {
  uploadImage.value.show();
};

const productSku = ref();
const freshSkuData = () => {
  productSku.value.freshData();
};
const showAddSkuForm = () => {
  productSku.value.showAddForm();
};

const attributeDetail = ref();
const showAttribute = (row) => {
  if (row.id) {
    attributeDetail.value.formType = "edit";
    attributeDetail.value.show(row);
  } else {
    attributeDetail.value.formType = "add";
    attributeDetail.value.show();
  }
};
const deleteAttribute = async (row) => {
  const { code, message } = await deleteAction(url.attr, row);
  if (code === 200) {
    Notify.create({
      type: "positive",
      message: message,
      position: "top-right",
    });
    await handleDetail(route.query.id);
  }
};

const recordDetailDialog = ref(null);
const showEditForm = () => {
  recordDetailDialog.value.formType = "edit";
  recordDetailDialog.value.show(itemDetail.value);
};

const editProductDetail = () => {
  router.push({ name: "productEdit", query: { id: itemDetail.value.id } });
};
</script>

<style scoped lang="scss">
.product-detail {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;

  .title-place {
    font-weight: 600;
    color: rgb(58, 111, 204);
  }

  .product-detail-row {
    padding: 5px;
  }

  .product-detail-label:after {
    content: "=";
    display: inline-block;
    padding: 0 12px;
    color: #26ceba;
  }

  .product-detail-value:before {
    content: "( ";
    color: #ffc069;
  }

  .product-detail-value:after {
    content: " )";
    color: #ffc069;
  }
}

.attr-label:after {
  content: "=";
  display: inline-block;
  padding: 0 12px;
  color: #26ceba;
}

:deep(img) {
  max-width: 100%;
}
</style>
