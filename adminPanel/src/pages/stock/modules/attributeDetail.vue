<template>
  <q-dialog v-model="recordDetailVisible" position="right" persistent>
    <q-card style="width: 80vw; max-width: 80vw">
      <q-card-section>
        <div class="text-h6">
          {{ formTypeName }} {{ $t("Product") + $t("Attribute") }}:
          {{ recordDetail.value.name }}
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-form ref="recordDetailForm">
          <gqa-form-top :record-detail="recordDetail" />
          <div class="row">
            <q-input
              v-model="recordDetail.value.name"
              class="col q-mx-sm"
              :label="t('admin.Name')"
            />
            <q-input
              v-model="recordDetail.value.value"
              class="col q-mx-sm"
              :label="$t('Value')"
            />
            <q-input
              v-model.number="recordDetail.value.order"
              class="col q-mx-sm"
              :label="$t('Sort')"
            />
          </div>
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn :label="$t('Save')" color="primary" @click="handleAddOrEdit" />
        <q-btn v-close-popup :label="$t('Cancel')" color="negative" />
      </q-card-actions>

      <q-inner-loading :showing="loading">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
import useRecordDetail from "src/composables/useRecordDetail";
import { toRefs } from "vue";
import { useI18n } from "vue-i18n";

const props = defineProps({
  parentId: {
    type: Number,
    required: true,
  },
});
const { parentId } = toRefs(props);
const { t } = useI18n();
const emit = defineEmits(["handleFinish"]);
const url = {
  item: "system/product/v1/product/attr",
};

const show = (row) => {
  loading.value = true;
  if (row && row.id) {
    recordDetail.value = row;
  } else {
    recordDetail.value = {};
  }
  loading.value = false;
  recordDetail.value.parentId = parentId;
  recordDetailVisible.value = true;
};

const {
  loading,
  formType,
  formTypeName,
  recordDetail,
  recordDetailVisible,
  recordDetailForm,
  handleAddOrEdit,
} = useRecordDetail(url, emit);

defineExpose({
  show,
  formType,
});
</script>
