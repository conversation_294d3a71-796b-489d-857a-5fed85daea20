<template>
  <div v-if="itemDetail?.id" class="q-pa-md">
    <div class="row q-ma-sm">
      <div class="text-h5 col" style="margin-bottom: 10px">
        {{ itemDetail.name }}
        <q-chip square outline text-color="white" :color="getStatusColor(itemDetail.status)">
          {{ getStatusText(itemDetail.status) }}
        </q-chip>
      </div>
      <div class="col-auto">
        <q-btn-group push>
          <q-btn push label="审核合同" icon="edit" color="secondary" @click="reviewContract" />
          <q-btn push label="查看附件" icon="attachment" color="orange" @click="showAttachment" />
          <q-btn push label="打印合同" icon="print" color="primary" @click="printContract" />
        </q-btn-group>
      </div>
    </div>
    <div class="row">
      <div class="col-6 q-px-sm">
        <q-card>
          <q-card-section>
            <!-- 财务信息 -->
            <div class="text-h6 q-mb-md">财务信息</div>
            <div class="row q-my-md">
              <div class="col">
                <div class="row">
                  <div class="col-4">确认额度</div>
                  <div class="col-8">
                    ¥{{ formatAmountWithDecimals(itemDetail.confirm_quota) }}
                  </div>
                </div>
              </div>
              <div class="col">
                <div class="row">
                  <div class="col-4">已用额度</div>
                  <div class="col-8">
                    ¥{{ formatAmountWithDecimals(itemDetail.used_quota) }}
                  </div>
                </div>
              </div>
            </div>
            <div class="row q-my-md">
              <div class="col">
                <div class="row">
                  <div class="col-4">服务费率</div>
                  <div class="col-8">{{ formatPercentage(itemDetail.profit_calc_fee) }}%</div>
                </div>
              </div>
              <div class="col">
                <div class="row">
                  <div class="col-4">计算周期</div>
                  <div class="col-8">
                    {{ itemDetail.profit_calc_period }}
                  </div>
                </div>
              </div>
            </div>
            <div class="row q-my-md">
              <div class="col">
                <div class="row">
                  <div class="col-4">违约金费率</div>
                  <div class="col-8">
                    {{ formatPercentage(itemDetail.penalty_calc_fee) }}%
                  </div>
                </div>
              </div>
              <div class="col">
                <div class="row">
                  <div class="col-4">违约金计算周期</div>
                  <div class="col-8">
                    {{ itemDetail.penalty_calc_period }}
                  </div>
                </div>
              </div>
            </div>

          </q-card-section>
        </q-card>
      </div>
      <div class="col-6 q-px-sm">
        <!-- 资金使用汇总情况 -->
        <q-card>
          <q-card-section>
            <div class="text-h6 q-mb-md">资金使用汇总情况</div>
            <div class="row q-my-md">
              <div class="col-4">合同订单总金额</div>
              <div class="col-8  text-orange-10 text-subtitle2">
                {{ formatAmountWithDecimals(unPayOrderTotal.total_amount) }}（元）
              </div>
            </div>
            <div class="row q-my-md">
              <div class="col-4">已绑定金额</div>
              <div class="col-8  text-orange-10 text-subtitle2">
                {{ formatAmountWithDecimals(unPayOrderTotal.paid_amount) }}（元）
              </div>
            </div>
            <div class="row q-my-md">
              <div class="col-4">未绑定金额</div>
              <div class="col-8  text-orange-10 text-subtitle2">
                {{ formatAmountWithDecimals(unPayOrderTotal.unpay_amount) }}（元）
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
    <!-- 资金使用历史情况 -->
    <div class="row q-my-md">
      <div class="col-12 q-px-sm">
        <q-card>
          <q-card-section>
            <div class="text-h6 q-mb-md q-mt-lg">资金使用历史情况</div>

            <q-table v-model:pagination="repaymentPagination" row-key="id" separator="cell" :rows="repaymentTableData"
              :columns="repaymentColumns" :rows-per-page-options="repaymentPageOptions" :loading="repaymentLoading"
              @request="onRepaymentRequest" dense>

              <template #top="props">
                <q-btn color="primary" label="创建还款计划" @click="handleInitRepayment" />
                <q-space />
                <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'" class="q-ml-md"
                  @click="props.toggleFullscreen" />
              </template>

              <template #body-cell-status="props">
                <q-td :props="props">
                  <q-chip :color="getRepayStatusColor(props.row.status)" text-color="white">
                    {{ getRepayStatusText(props.row.status) }}
                  </q-chip>
                </q-td>
              </template>

              <template #body-cell-actions="props">
                <q-td :props="props">
                  <div class="q-gutter-xs">
                    <q-btn-group>
                      <q-btn size="sm" color="primary" label="查看明细" @click="showRepaymentDetail(props.row)" />
                    </q-btn-group>
                  </div>
                </q-td>
              </template>
            </q-table>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- 审核合同弹窗 -->
    <q-dialog v-model="showReviewDialog" persistent>
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">审核合同</div>
          <div class="text-subtitle2 q-mt-sm">确认要审核合同 "{{ itemDetail?.name }}" 吗？</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">审核结果：</div>
            <q-option-group v-model="reviewOption" :options="reviewOptions" color="primary" type="radio" />
          </div>

          <div>
            <q-input v-model="reviewRemark" type="textarea" label="审核备注" placeholder="请输入审核意见..." rows="3" outlined />
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="grey" @click="cancelReview" />
          <q-btn unelevated label="确认" :color="reviewOption === 'approve' ? 'positive' : 'negative'"
            @click="confirmReview" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 附件列表弹窗 -->
    <ShowAttachmentList ref="showAttachmentDialog" />

  </div>
</template>

<script setup>
import { Notify } from "quasar";
import { postAction } from "src/api/manage.ts";
import { FormatDateTime } from "src/utils/date";
import { computed, onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import useTableData from "src/composables/useTableData";
import ShowAttachmentList from "./modules/showAttachmentList.vue";

// Props定义
const props = defineProps({
  itemId: {
    type: String,
    required: true
  },
  itemDetail: {
    type: Object,
    required: true
  }
});

const router = useRouter();

const url = {
  item: "/api/financial_contract",
  review: "/api/financial_contract/update",
  repayment: "/api/repayment/list",
  repayment_create: "/api/repayment",
  order: "/api/sales_order/list",
  attachment: "/api/attachment/list",
  upload: "/api/upload", // 待配置
  totalPayment: "/api/sales_order/count_payment"
};

const itemDetail = ref();
const unPayOrder = ref([]);
const unPayOrderTotal = ref(0);

// 审核弹窗相关变量
const showReviewDialog = ref(false);
const reviewOption = ref('approve');
const reviewRemark = ref('');
const reviewOptions = [
  { label: '审核通过', value: 'approve' },
  { label: '审核拒绝', value: 'reject' }
];

// 还款表格的分页状态
const repaymentPagination = ref({
  sortBy: 'created_at',
  descending: true,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0
});
const repaymentPageOptions = ref([10, 30, 50, 100]);
const repaymentLoading = ref(false);
const repaymentTableData = ref([]);
const unPayOrderParams = ref({
  page: {
    page: 1,
    limit: 20,
  },
  options: {
    order_by: "created_at",
    desc: true,
  },
  params: []
});
// 还款表格的columns定义，基于RepaymentResponse结构体
const repaymentColumns = computed(() => {
  return [
    {
      name: "serial",
      align: "center",
      label: "还款计划编号",
      field: "serial"
    },
    {
      name: "profit_amount",
      align: "center",
      label: "预计利润",
      field: "profit_amount",
      format: val => val ? `¥${formatAmountWithDecimals(val)}` : '-'
    },
    {
      name: "principal_amount",
      align: "center",
      label: "预计本金",
      field: "principal_amount",
      format: val => val ? `¥${formatAmountWithDecimals(val)}` : '-'
    },
    {
      name: "total_amount",
      align: "center",
      label: "预计总额",
      field: "total_amount",
      format: val => val ? `¥${formatAmountWithDecimals(val)}` : '-'
    },
    {
      name: "profit_remain",
      align: "center",
      label: "剩余利润",
      field: "profit_remain",
      format: val => val ? `¥${formatAmountWithDecimals(val)}` : '-'
    },
    {
      name: "principal_remain",
      align: "center",
      label: "剩余本金",
      field: "principal_remain",
      format: val => val ? `¥${formatAmountWithDecimals(val)}` : '-'
    },
    {
      name: "total_remain",
      align: "center",
      label: "剩余总额",
      field: "total_remain",
      format: val => val ? `¥${formatAmountWithDecimals(val)}` : '-'
    },
    {
      name: "begin_date",
      align: "center",
      label: "开始日期",
      field: "begin_date"
    },
    {
      name: "end_date",
      align: "center",
      label: "结束日期",
      field: "end_date"
    },
    {
      name: "status",
      align: "center",
      label: "计划状态",
      field: "status"
    },
    {
      name: "created_at",
      align: "center",
      label: "创建时间",
      field: "created_at",
      format: val => val ? FormatDateTime(val) : '-'
    },
    {
      name: "actions",
      align: "center",
      label: "操作",
      field: "actions"
    },
  ];
});

// 状态字典
const statusDict = [
  { label: "草稿", value: "draft" },
  { label: "待审核", value: "new" },
  { label: "已审核", value: "processing" },
  { label: "待还款", value: "pending" },
  { label: "部分还款", value: "partial" },
  { label: "已完成", value: "completed" },
  { label: "逾期", value: "overdue" },
];

// 还款表格的请求函数
const onRepaymentRequest = async (props) => {
  repaymentLoading.value = true;
  repaymentTableData.value = [];

  // 组装分页和过滤条件
  const allParams = {
    options: {
      order_by: props.pagination.sortBy,
      desc: props.pagination.descending,
    },
    page: {
      page: props.pagination.page,
      limit: props.pagination.rowsPerPage,
    },
    params: [
      {
        var: "contract_id",
        val: itemDetail.value?.id || props.itemId
      }
    ]
  };

  try {
    const { code, data } = await postAction(url.repayment, allParams);
    if (code === 200) {
      // 更新分页信息
      repaymentPagination.value = props.pagination;
      repaymentPagination.value.rowsNumber = data.total;
      repaymentTableData.value = data.data;
    } else {
      Notify.create({
        type: "warning",
        message: "还款信息查询失败，请重试",
        position: "top-right",
      });
    }
  } catch (error) {
    console.error('获取还款数据失败:', error);
    Notify.create({
      type: "negative",
      message: "获取还款数据失败",
      position: "top-right",
    });
  } finally {
    repaymentLoading.value = false;
  }
};

// 获取还款表格数据
const getRepaymentTableData = () => onRepaymentRequest({ pagination: repaymentPagination.value });

onMounted(async () => {
  if (props.itemId) {
    itemDetail.value = props.itemDetail;

    // 初始化还款表格并获取数据
    await getRepaymentTableData();
    await getUnPayOrder();
    await countUnPayOrder();
  } else {
    repaymentTableData.value = [];
    Notify.create({
      type: "warning",
      message: "还款信息查询失败，请重试",
      position: "top-right",
    });
  }
});



// 获取状态颜色
const getStatusColor = (status) => {
  const statusItem = statusDict.find((item) => item.value === status);
  return statusItem ? statusItem.color : "grey";
};

// 获取状态文本
const getStatusText = (status) => {
  const statusItem = statusDict.find((item) => item.value === status);
  return statusItem ? statusItem.label : status;
};

// 获取还款状态颜色
const getRepayStatusColor = (repayStatus) => {
  const statusItem = statusDict.find((item) => item.value === repayStatus);
  return statusItem ? statusItem.color : "grey";
};

// 获取还款状态文本
const getRepayStatusText = (repayStatus) => {
  const statusItem = statusDict.find((item) => item.value === repayStatus);
  return statusItem ? statusItem.label : (repayStatus || '未知');
};

// 格式化金额（保留两位小数）
const formatAmountWithDecimals = (amount) => {
  if (!amount) return "0.00";
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 格式化百分比（保留两位小数）
const formatPercentage = (value) => {
  if (!value) return "0.00";
  return (Number(value) * 100).toFixed(2);
};



// 计算周期字典
const calcPeriodDict = Object.freeze({
  "单次": "ONCE",
  "按周": "WEEK",
  "按天": "DAY",
  "按月": "MONTH",
  "按年": "YEAR",
  "按季度": "QUARTER"
});

// 处理无效值的辅助函数
const handleInvalidPeriod = () => {
  console.warn(`无效的计算周期值: ${itemDetail.value.profit_calc_period}`);
  return "DAY"; // 默认返回'按天'周期
}

const handleInitRepayment = async () => {
  // 构建RepaymentCreate数据结构
  const repaymentData = {
    serial: null,                    // 还款计划编号
    contract_id: props.itemId,     // 合作项目ID
    profit_calc_fee: itemDetail.value.profit_calc_fee, // 利润计算费用
    penalty_calc_fee: itemDetail.value.penalty_calc_fee, // 违约金计算费用
    profit_calc_period: calcPeriodDict[itemDetail.value.profit_calc_period] || handleInvalidPeriod(),
    status: "draft",                 // 状态设置为草稿
  };

  // 使用url.create创建还款数据
  const res = await postAction(url.repayment_create, repaymentData);

  if (res.code === 200) {
    router.push({
      path: "/repayment/detail",
      query: { id: res.data },
    });
  } else {
    // 创建失败，使用默认数据
    Notify.create({
      type: "warning",
      message: "创建还款计划失败，请重试",
      position: "top-right",
    });
  }
};

const showRepaymentDetail = (row) => {
  router.push({
    path: "/repayment/detail",
    query: { id: row.id },
  });
};

const getUnPayOrder = async () => {
  unPayOrderParams.value.params.push({
    var: "contract_id",
    val: props.itemId
  });
  unPayOrderParams.value.params.push({
    var: "repayment_id",
    val: "repayment:None"
  });
  const { code, data } = await postAction(url.order, unPayOrderParams.value);
  if (code === 200) {
    unPayOrder.value = data;
  } else {
    unPayOrder.value = [];
    Notify.create({
      type: "warning",
      message: "未还订单查询失败，请重试",
      position: "top-right",
    });
  }
};

const countUnPayOrder = async () => {
  const { code, data } = await postAction(url.totalPayment, { id: props.itemId });
  if (code === 200) {
    unPayOrderTotal.value = data;
  } else {
    unPayOrderTotal.value = 0;
    Notify.create({
      type: "warning",
      message: "未还订单统计失败，请重试",
      position: "top-right",
    });
  }
}

// 打开审核弹窗
const reviewContract = () => {
  // 重置表单数据
  reviewOption.value = 'approve';
  reviewRemark.value = '';
  showReviewDialog.value = true;
}

// 取消审核
const cancelReview = () => {
  showReviewDialog.value = false;
  reviewOption.value = 'approve';
  reviewRemark.value = '';
}

// 确认审核
const confirmReview = async () => {
  try {
    // 构建请求数据
    const requestData = {
      confirm: reviewOption.value === 'approve', // true为审核通过，false为审核拒绝
      remark: reviewRemark.value || '' // 审核备注
    };

    // 发送请求到后端
    const response = await postAction(`${url.item}/${itemDetail.value.id}`, requestData);

    if (response.code === 200) {
      Notify.create({
        type: 'positive',
        message: `合同${reviewOption.value === 'approve' ? '审核通过' : '审核拒绝'}成功`,
        position: 'top-right'
      });

      // 关闭弹窗
      showReviewDialog.value = false;

      // 刷新页面数据
      window.location.reload();
    } else {
      Notify.create({
        type: 'negative',
        message: response.msg || '审核操作失败',
        position: 'top-right'
      });
    }
  } catch (error) {
    console.error('审核合同失败:', error);
    Notify.create({
      type: 'negative',
      message: '审核操作失败，请重试',
      position: 'top-right'
    });
  }
}

// 查看附件功能
const showAttachmentDialog = ref();
const showAttachment = () => {
  showAttachmentDialog.value.show(itemDetail.value);
}

// 打印合同功能
const printContract = () => {
  // TODO: 实现打印合同功能
  window.print();
}

</script>

<style scoped lang="scss">
.row-title {
  font-weight: 600;
  color: #666;
  padding: 8px 0;
}

.contract-desc {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
}

.text-h6 {
  color: #1976d2;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 8px;
}

:deep(img) {
  max-width: 100%;
}

@media print {
  .q-btn-group {
    display: none;
  }
}
</style>
