<template>
  <base-content scrollable>
    <div class="row q-gutter-md q-ma-md">
      <div class="col">
        <div class="items-center row q-gutter-md" style="margin-bottom: 10px">
          <q-input v-model="queryParams.serial" style="width: 20%" label="合同编号" />
          <q-input v-model="queryParams.name" style="width: 20%" label="合同名称" />
          <q-btn color="primary" label="搜索" @click="handleSearch" />
          <q-btn color="primary" label="重置" @click="resetSearch" />
        </div>
        <q-table v-model:pagination="pagination" row-key="id" separator="cell" :rows="tableData" :columns="columns"
          :rows-per-page-options="pageOptions" :loading="loading" @request="onRequest">
          <template #top="props">
            <q-btn color="primary" label="创建合同" @click="createContract" />
            <q-space />
            <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'" class="q-ml-md"
              @click="props.toggleFullscreen" />
          </template>

          <template #body-cell-status="props">
            <q-td :props="props">
              <q-chip
                :color="getStatusColor(props.row.status)"
                text-color="white"
              >
                {{ getStatusText(props.row.status) }}
              </q-chip>
            </q-td>
          </template>

          <template #body-cell-actions="props">
            <q-td :props="props">
              <div class="q-gutter-xs">
                <q-btn-group>
                  <q-btn size="sm" color="primary" label="查看明细" @click="showDetail(props.row)" />
                </q-btn-group>
              </div>
            </q-td>
          </template>
        </q-table>
      </div>
    </div>
  </base-content>
</template>

<script setup>
defineOptions({ name: "FinancialContractList" });

import BaseContent from "src/components/BaseContent/BaseContent.vue";
import useTableData from "src/composables/useTableData";
import { computed, onMounted } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();

const url = {
  list: "/api/financial_contract/list",
  create: "/api/financial_contract",
  edit: "/api/financial_contract",
  delete: "/api/financial_contract",
};

const {
  pagination,
  queryParams,
  pageOptions,
  loading,
  tableData,
  onRequest,
  getTableData,
  handleSearch,
} = useTableData(url);

// 金融合同状态字典
const statusDict = [
  { value: "draft", label: "草稿", color: "grey" },
  { value: "new", label: "待审核", color: "blue" },
  { value: "processing", label: "已审核", color: "green" },
  { value: "done", label: "已完成", color: "teal" },
  { value: "expired", label: "已过期", color: "red" },
];

// 获取状态颜色
const getStatusColor = (status) => {
  const statusItem = statusDict.find((item) => item.value === status);
  return statusItem ? statusItem.color : "grey";
};

// 获取状态文本
const getStatusText = (status) => {
  const statusItem = statusDict.find((item) => item.value === status);
  return statusItem ? statusItem.label : status || '未知';
};

onMounted(async () => {
  pagination.value.sortBy = "created_at";
  pagination.value.descending = true;
  await getTableData();
});

const columns = computed(() => {
  return [
    { name: "name", align: "center", label: "合同名称", field: "name" },
    {
      name: "serial",
      align: "center",
      label: "合同编号",
      field: "serial",
    },
    {
      name: "applier",
      align: "center",
      label: "申请人",
      field: "applier",
    },
    {
      name: "contract_type",
      align: "center",
      label: "合同类型",
      field: "contract_type",
    },
    {
      name: "begin_time",
      align: "center",
      label: "开始时间",
      field: "begin_time",
    },
    {
      name: "end_time",
      align: "center",
      label: "结束时间",
      field: "end_time",
    },
    {
      name: "funder",
      align: "center",
      label: "资金方",
      field: "funder",
    },
    {
      name: "status",
      align: "center",
      label: "合同状态",
      field: "status",
    },
    {
      name: "actions",
      align: "center",
      label: "操作",
      field: "actions",
    },
  ];
});
const createContract = () => {
  router.push({
    name: "contractCreate",
  });
};
const showDetail = (row) => {
  router.push({
    name: "contractDetail",
    query: { id: row.id },
  });
};
</script>
