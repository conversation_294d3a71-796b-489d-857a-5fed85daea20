<template>
  <base-content scrollable>
    <q-card class="q-ma-md">
      <q-card-section class="row">
        <div class="text-h6 q-mx-sm col">
          管理还款计划
          <q-badge v-if="itemDetail.status" :color="getStatusColor(itemDetail.status)"
            :label="getStatusLabel(itemDetail.status)" class="q-ml-sm" />
        </div>
        <div class="col-auto">
          <q-btn label="编辑基本信息" color="primary" class="q-mx-sm" @click="handleEditRepayment" />
          <q-btn v-if="itemDetail.status === 'new'" label="审核还款计划" icon="edit" color="secondary" class="q-mx-sm"
            @click="reviewRepayment" />
          <q-btn
            v-if="itemDetail.status === 'processing' || itemDetail.status === 'pending' || itemDetail.status === 'partial'"
            label="还款" icon="payment" color="positive" class="q-mx-sm" @click="addRepayment" />
          <q-btn v-close-popup label="关闭页面" color="negative" class="q-mx-sm" @click="closeTab" />
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <!-- 基本信息 -->
        <div class="row q-gutter-md q-mb-md">
          <div class="col flex flex-column">
            <div class="title-width q-mx-md text-blue-10">还款计划编号</div>
            <div class="text-h6 text-orange-10 ">{{ itemDetail.serial || '系统自动生成' }}</div>
          </div>
          <div class="col flex flex-column">
            <div class="title-width q-mx-md text-blue-10">支取本金金额</div>
            <div class="text-h6 text-orange-10 ">{{ itemDetail.principal_amount ?
              `¥${parseFloat(itemDetail.principal_amount).toFixed(2)}` : '0.00' }}（元）</div>
          </div>
          <div class="col flex flex-column">
            <div class="row">
              <div class="title-width q-mx-md text-blue-10">关联订单金额</div>
              <div class="text-h6 text-orange-10">{{ formatAmountWithDecimals(itemDetail.target_amount ?
                itemDetail.target_amount : 0) }}（元）
              </div>
            </div>
          </div>
        </div>

        <!-- 时间限制 -->
        <div class="row q-gutter-md q-mb-md">
          <div class="col flex flex-column">
            <div class="title-width q-mx-md text-blue-10">开始日期</div>
            <div class="text-h6 text-orange-10 ">{{ itemDetail.begin_date || '未设置' }}</div>
          </div>
          <div class="col flex flex-column">
            <div class="title-width q-mx-md text-blue-10">结束日期</div>
            <div class="text-h6 text-orange-10 ">{{ itemDetail.end_date || '未设置' }}</div>
          </div>
          <div class="col flex flex-column">
            <div class="title-width q-mx-md text-blue-10">宽限天数</div>
            <div class="text-h6 text-orange-10 ">{{ itemDetail.grace_period || 0 }} 天</div>
          </div>
        </div>

        <!-- 利息计算配置 -->
        <div class="row q-gutter-md q-mb-md">
          <div class="col flex flex-column">
            <div class="title-width q-mx-md text-blue-10">利息计算费率</div>
            <div class="text-h6 text-orange-10 ">{{ itemDetail.profit_calc_fee ? `${(itemDetail.profit_calc_fee *
              100).toFixed(2)}%` : '0.00%' }}</div>
            <div class="text-caption text-grey-6">年化利率</div>
          </div>
          <div class="col flex flex-column">
            <div class="title-width q-mx-md text-blue-10">利息计算周期</div>
            <div class="text-h6 text-orange-10 ">{{ getCalcPeriodLabel(itemDetail.profit_calc_period) }}</div>
          </div>
          <div class="col flex flex-column">
            <div class="title-width q-mx-md text-blue-10">违约金计算费率</div>
            <div class="text-h6 text-orange-10 ">{{ itemDetail.penalty_calc_fee ? `${(itemDetail.penalty_calc_fee *
              100).toFixed(4)}%` : '0.0000%' }}</div>
            <div class="text-caption text-grey-6">日息利率</div>
          </div>
        </div>

        <!-- 剩余金额信息 (仅当状态不是draft或new时显示) -->
        <div v-if="itemDetail.status && itemDetail.status !== 'draft' && itemDetail.status !== 'new'"
          class="row q-gutter-md q-mb-md">
          <div class="col flex flex-column">
            <div class="title-width q-mx-md text-blue-10">应付利息</div>
            <div class="text-h6 text-orange-10">{{ itemDetail.profit_remain ?
              `¥${formatAmountWithDecimals(itemDetail.profit_remain)}` : '¥0.00' }}</div>
            <div class="text-caption text-grey-6">当前剩余利息金额</div>
          </div>
          <div class="col flex flex-column">
            <div class="title-width q-mx-md text-blue-10">应付本金</div>
            <div class="text-h6 text-orange-10">{{ itemDetail.principal_remain ?
              `¥${formatAmountWithDecimals(itemDetail.principal_remain)}` : '¥0.00' }}</div>
            <div class="text-caption text-grey-6">当前剩余本金金额</div>
          </div>
          <div class="col flex flex-column">
            <div class="title-width q-mx-md text-blue-10">应付总额</div>
            <div class="text-h6 text-orange-10">{{ itemDetail.total_remain ?
              `¥${formatAmountWithDecimals(itemDetail.total_remain)}` : '¥0.00' }}</div>
            <div class="text-caption text-grey-6">当前剩余总金额</div>
          </div>
        </div>

        <!-- 备注信息 -->
        <div class="row">
          <div class="col-12">
            <div class="text-body1 q-pa-md bg-grey-1 rounded-borders border"
              style="min-height: 80px; white-space: pre-wrap; border-color: #e0e0e0;">
              {{ itemDetail.remark || '无备注信息' }}
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- 标签页组件 -->
    <q-card class="q-ma-md">
      <q-tabs v-model="tab" dense class="text-grey q-pa-sm" active-color="primary" indicator-color="primary"
        align="start">
        <q-tab name="orders" label="关联订单列表" />
        <q-tab name="repayment_logs" label="还款记录列表" />
      </q-tabs>

      <q-tab-panels v-model="tab" animated>
        <!-- 关联订单列表标签页 -->
        <q-tab-pane name="orders">
          <q-card-section>
            <div class="row">
              <div class="col"><q-btn label="关联订单" color="primary" @click="getOrderList" /></div>
              <div class="col-auto">
                <div class="col flex flex-column">
                  <div class="row">
                    <div class="q-mx-md text-blue-10">订单金额统计：</div>
                    <div class="text-orange-10">{{ formatAmountWithDecimals(itemDetail.target_amount ?
                      itemDetail.target_amount : 0) }}（元）</div>
                  </div>
                </div>
              </div>
            </div>
          </q-card-section>
          <q-card-section>
            <q-table :rows="relatedOrders" :columns="orderColumns" :loading="ordersLoading" :pagination="pagination"
              @request="handleOrderRequest" row-key="id" flat bordered>
              <!-- 状态列自定义显示 -->
              <template v-slot:body-cell-status="props">
                <q-td :props="props">
                  <q-badge :color="getOrderStatusColor(props.value)" :label="getOrderStatusLabel(props.value)" />
                </q-td>
              </template>

              <!-- 操作列 -->
              <template v-slot:body-cell-actions="props">
                <q-td :props="props">
                  <q-btn flat dense color="primary" icon="visibility" size="sm" @click="handleViewOrder(props.row)">
                    <q-tooltip>查看</q-tooltip>
                  </q-btn>
                  <q-btn flat dense color="negative" icon="remove_circle" size="sm"
                    @click="handleRemoveOrder(props.row)" class="q-ml-xs">
                    <q-tooltip>移除</q-tooltip>
                  </q-btn>
                </q-td>
              </template>

              <!-- 无数据显示 -->
              <template v-slot:no-data>
                <div class="full-width row flex-center text-grey q-gutter-sm">
                  <q-icon size="2em" name="inbox" />
                  <span>暂无关联订单</span>
                </div>
              </template>
            </q-table>
          </q-card-section>
        </q-tab-pane>

        <!-- 还款记录列表标签页 -->
        <q-tab-pane name="repayment_logs">
          <q-card-section>
            <q-table :rows="repaymentLogs" :columns="repaymentLogColumns" :loading="repaymentLogsLoading"
              :pagination="repaymentLogPagination" @request="handleRepaymentLogRequest" row-key="id" flat bordered>

              <!-- 日志类型列自定义显示 -->
              <template v-slot:body-cell-log_type="props">
                <q-td :props="props">
                  <q-badge :color="getLogTypeColor(props.value)" :label="getLogTypeLabel(props.value)" />
                </q-td>
              </template>

              <!-- 日志状态列自定义显示 -->
              <template v-slot:body-cell-log_status="props">
                <q-td :props="props">
                  <q-badge :color="getLogStatusColor(props.value)" :label="getLogStatusLabel(props.value)" />
                </q-td>
              </template>

              <!-- 创建时间列格式化 -->
              <template v-slot:body-cell-created_at="props">
                <q-td :props="props">
                  {{ formatDateTime(props.value) }}
                </q-td>
              </template>

              <!-- 操作列 -->
              <template v-slot:body-cell-actions="props">
                <q-td :props="props">
                  <q-btn flat dense color="primary" icon="visibility" size="sm"
                    @click="handleViewRepaymentLog(props.row)">
                    <q-tooltip>查看详情</q-tooltip>
                  </q-btn>
                </q-td>
              </template>

              <!-- 无数据显示 -->
              <template v-slot:no-data>
                <div class="full-width row flex-center text-grey q-gutter-sm">
                  <q-icon size="2em" name="inbox" />
                  <span>暂无还款记录</span>
                </div>
              </template>
            </q-table>
          </q-card-section>
        </q-tab-pane>
      </q-tab-panels>
    </q-card>

    <SelectOrderList v-if="itemDetail.id" ref="selectOrderListDialogue" :url-list="url.list"
      :contract-id="itemDetail.contract_id" @handleSelectItem="handleSelectOrder" />

    <!-- 还款计划编辑对话框 -->
    <RepaymentEdit ref="repaymentEditDialog" @handleFinish="handleRepaymentSave" />

    <!-- 还款记录弹窗 -->
    <RepaymentLog ref="repaymentLogDialog" @repaymentAdded="handleRepaymentAdded" />

    <!-- 还款记录详情弹窗 -->
    <RepaymentLogDetail ref="repaymentLogDetailDialog" @reviewCompleted="handleReviewCompleted" />

    <!-- 审核还款计划弹窗 -->
    <q-dialog v-model="showReviewDialog" persistent>
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">审核还款计划</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <div class="q-mb-md">
            <q-option-group v-model="reviewOption" :options="reviewOptions" color="primary" inline />
          </div>

          <div class="q-mb-md">
            <q-input v-model="reviewRemark" type="textarea" label="审核备注" placeholder="请输入审核备注（可选）" rows="3" outlined />
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="primary" @click="cancelReview" />
          <q-btn push label="确认" color="primary" @click="confirmReview" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </base-content>
</template>

<script setup>
import { Notify, Dialog } from "quasar";
import { postAction, putAction, getActionByPath } from "src/api/manage.ts";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import { useTagViewStore } from "src/stores/tagView";
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import SelectOrderList from "src/components/SelectOrderList/SelectItemDialog.vue";
import useTableData from "src/composables/useTableData";
import RepaymentEdit from "./modules/repaymentEdit.vue";
import RepaymentLog from "./modules/repaymentLog.vue";
import RepaymentLogDetail from "./modules/repaymentLogDetail.vue";
import { FormatDateTime } from "src/utils/date";

const tabMenuStore = useTagViewStore();
const route = useRoute();
const router = useRouter();
const url = {
  item: "/api/repayment",
  create: "/api/repayment",
  edit: "/api/repayment",
  list: "/api/sales_order/list",
  relate: "/api/repayment/relate",
  unrelate: "/api/repayment/unrelate",
  contract: "/api/financial_contract",
  repayment_log_list: "/api/repayment_log/list"
};

const itemDetail = ref({});

// 标签页控制
const tab = ref('orders');

const selectOrderListDialogue = ref(null);
const repaymentEditDialog = ref(null);
const repaymentLogDialog = ref(null);
const repaymentLogDetailDialog = ref(null);

// 还款记录列表相关变量
const repaymentLogs = ref([]);
const repaymentLogsLoading = ref(false);
const repaymentLogPagination = ref({
  sortBy: 'created_at',
  descending: true,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0
});

// 审核弹窗相关变量
const showReviewDialog = ref(false);
const reviewOption = ref('approve');
const reviewRemark = ref('');
const reviewOptions = [
  { label: '审核通过', value: 'approve' },
  { label: '审核拒绝', value: 'reject' }
];

// 使用 useTableData 管理订单列表
const {
  tableData: relatedOrders,
  pagination,
  loading: ordersLoading,
  queryParams: orderParams,
  onRequest: handleOrderRequest
} = useTableData(url);

// 订单列表表格列定义
const orderColumns = [
  {
    name: 'platform_order_serial',
    label: '平台订单号',
    field: 'platform_order_serial',
    align: 'left',
    sortable: true
  },
  {
    name: 'platform_name',
    label: '平台名称',
    field: 'platform_name',
    align: 'left',
    sortable: true
  },
  {
    name: 'total_payment',
    label: '订单金额',
    field: 'total_payment',
    align: 'right',
    sortable: true,
    format: (val) => val ? `¥${parseFloat(val).toFixed(2)}` : '-'
  },
  {
    name: 'purchase_time',
    label: '下单时间',
    field: 'purchase_time',
    align: 'center',
    sortable: true
  },
  {
    name: 'complete_time',
    label: '完成时间',
    field: 'complete_time',
    align: 'center',
    sortable: true
  },
  {
    name: 'status',
    label: '状态',
    field: 'status',
    align: 'center',
    sortable: true
  },
  {
    name: 'actions',
    label: '操作',
    field: 'actions',
    align: 'center'
  }
];

// 还款记录列表表格列定义
const repaymentLogColumns = [
  {
    name: 'log_type',
    label: '日志类型',
    field: 'log_type',
    align: 'center',
    sortable: true
  },
  {
    name: 'log_value',
    label: '记录值',
    field: 'log_value',
    align: 'right',
    sortable: true,
    format: (val) => val ? `¥${parseFloat(val).toFixed(2)}` : '-'
  },
  {
    name: 'log_date',
    label: '日期',
    field: 'log_date',
    align: 'center',
    sortable: true
  },
  {
    name: 'log_status',
    label: '状态',
    field: 'log_status',
    align: 'center',
    sortable: true
  },
  {
    name: 'creater_name',
    label: '操作人',
    field: 'creater_name',
    align: 'center',
    sortable: true
  },
  {
    name: 'remark',
    label: '备注',
    field: 'remark',
    align: 'left',
    sortable: false
  },
  {
    name: 'created_at',
    label: '创建时间',
    field: 'created_at',
    align: 'center',
    sortable: true
  },
  {
    name: 'actions',
    label: '操作',
    field: 'actions',
    align: 'center'
  }
];

const getOrderList = () => {
  selectOrderListDialogue.value.show();
};

const handleSelectOrder = async (items) => {
  try {
    // 检查是否有选中的订单
    if (!items || items.length === 0) {
      Notify.create({
        type: "warning",
        message: "请选择要关联的订单",
        position: "top-right",
      });
      return;
    }

    // 检查还款计划ID是否存在
    if (!itemDetail.value.id) {
      Notify.create({
        type: "negative",
        message: "还款计划ID不存在，无法关联订单",
        position: "top-right",
      });
      return;
    }

    // 提取订单ID列表
    const orderIds = items.map(item => item.id);

    // 构建请求数据，符合 RelateOrders 结构
    const requestData = {
      id: itemDetail.value.id,  // 还款计划ID
      ids: orderIds             // 订单ID列表
    };

    console.log('关联订单请求数据:', requestData);

    // 调用关联订单API
    const res = await postAction(url.relate, requestData);

    if (res.code === 200) {
      Notify.create({
        type: "positive",
        message: `成功关联 ${items.length} 个订单`,
        position: "top-right",
      });

      // 关联成功后刷新数据
      await handleGetDetail();
      await getRelatedOrderList();
    } else {
      Notify.create({
        type: "negative",
        message: res.msg || "关联订单失败",
        position: "top-right",
      });
    }
  } catch (error) {
    console.error('关联订单出错:', error);
    Notify.create({
      type: "negative",
      message: "关联订单时发生错误，请重试",
      position: "top-right",
    });
  }
}

const contractDetail = ref({});

const getContractDetail = async () => {
  try {
    const { code, data } = await getActionByPath(url.contract, [itemDetail.value.contract_id]);
    if (code === 200) {
      contractDetail.value = data;
    } else {
      contractDetail.value = {};
    }
  } catch (error) {
    console.error('获取合同详情出错:', error);
    contractDetail.value = {};
  }
}

// 获取关联订单列表
const getRelatedOrderList = async () => {
  if (!itemDetail.value.id) return;

  // 设置查询参数
  orderParams.value = {
    repayment_id: itemDetail.value.id
  };

  // 调用表格请求处理函数
  await handleOrderRequest({
    pagination: pagination.value,
    url: url.order,
    params: orderParams.value
  });
};

// 获取还款记录列表
const getRepaymentLogList = async () => {
  if (!itemDetail.value.id) return;

  repaymentLogsLoading.value = true;
  try {
    const logParams = {
      params: [
        { var: 'raw', val: `parent_id = "${itemDetail.value.id}"` },
        { var: 'log_type', val: 'REPAY' }
      ],
      page: { page: 1, limit: 0 },
      options: {
        desc: false,
        order_by: 'created_at'
      }
    }

    const response = await postAction(url.repayment_log_list, logParams);

    if (response.code === 200) {
      repaymentLogs.value = response.data.data || [];
      repaymentLogPagination.value.rowsNumber = response.data.total || 0;
    } else {
      repaymentLogs.value = [];
      repaymentLogPagination.value.rowsNumber = 0;
      Notify.create({
        type: "warning",
        message: response.msg || "获取还款记录失败",
        position: "top-right",
      });
    }
  } catch (error) {
    console.error('获取还款记录出错:', error);
    repaymentLogs.value = [];
    repaymentLogPagination.value.rowsNumber = 0;
    Notify.create({
      type: "negative",
      message: "获取还款记录时发生错误，请重试",
      position: "top-right",
    });
  } finally {
    repaymentLogsLoading.value = false;
  }
};

// 处理还款记录列表请求
const handleRepaymentLogRequest = async (props) => {
  const { page, rowsPerPage } = props.pagination;

  repaymentLogPagination.value.page = page;
  repaymentLogPagination.value.rowsPerPage = rowsPerPage;

  await getRepaymentLogList();
};

// 查看订单详情
const handleViewOrder = (row) => {
  router.push({
    path: "/order/detail",
    query: { id: row.id }
  });
};

// 移除订单关联
const handleRemoveOrder = async (row) => {
  Dialog.create({
    title: "确认移除",
    message: "确定要从还款计划中移除该订单吗？",
    persistent: true,
    ok: {
      push: true,
      color: "negative",
      label: "确认",
    },
    cancel: {
      push: true,
      color: "primary",
      label: "取消",
    },
  }).onOk(async () => {
    try {
      const res = await postAction(url.unrelate, { id: itemDetail.value.id, order_id: row.id });
      if (res.code === 200) {
        Notify.create({
          type: "positive",
          message: "订单已成功移除",
          position: "top-right",
        });
        // 重新获取关联订单列表
        await getRelatedOrderList();
      } else {
        Notify.create({
          type: "negative",
          message: res.msg || "移除订单失败",
          position: "top-right",
        });
      }
    } catch (error) {
      console.error('移除订单出错:', error);
      Notify.create({
        type: "negative",
        message: "移除订单时发生错误，请重试",
        position: "top-right",
      });
    }
  });
}

// 订单状态字典
const orderStatusDict = [
  { label: "待付款", value: "pending_payment" },
  { label: "已付款", value: "paid" },
  { label: "已发货", value: "shipped" },
  { label: "已完成", value: "completed" },
  { label: "已取消", value: "cancelled" },
  { label: "退款中", value: "refunding" },
  { label: "已退款", value: "refunded" },
];

// 获取订单状态标签
const getOrderStatusLabel = (status) => {
  const statusItem = orderStatusDict.find(item => item.value === status);
  return statusItem ? statusItem.label : status;
};

// 获取订单状态颜色
const getOrderStatusColor = (status) => {
  const colorMap = {
    'pending_payment': 'orange',
    'paid': 'blue',
    'shipped': 'purple',
    'completed': 'green',
    'cancelled': 'grey',
    'refunding': 'amber',
    'refunded': 'red'
  };
  return colorMap[status] || 'grey';
};

onMounted(async () => {
  console.log(route.query.id, "当前route");
  if (route.query.id) {
    await handleGetDetail();
    await getContractDetail();
    await getRelatedOrderList();
    await getRepaymentLogList();
  } else {
    closeTab();
  }
});

// 项目状态字典
const statusDict = [
  { label: "草稿", value: "draft" },
  { label: "待审核", value: "new" },
  { label: "已审核", value: "processing" },
  { label: "待还款", value: "pending" },
  { label: "部分还款", value: "partial" },
  { label: "已完成", value: "completed" },
  { label: "逾期", value: "overdue" },
];

// 获取状态标签
const getStatusLabel = (status) => {
  const statusItem = statusDict.find(item => item.value === status);
  return statusItem ? statusItem.label : status;
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    'draft': 'grey',
    'new': 'blue',
    'processing': 'orange',
    'pending': 'purple',
    'partial': 'amber',
    'completed': 'green',
    'overdue': 'red'
  };
  return colorMap[status] || 'grey';
};

// 计算周期字典
const calcPeriodDict = [
  { label: "单次", value: "ONCE" },
  { label: "按天", value: "DAY" },
  { label: "按月", value: "MONTH" },
  { label: "按年", value: "YEAR" },
  { label: "按季度", value: "QUARTER" },
];

// 获取计算周期标签
const getCalcPeriodLabel = (value) => {
  const periodItem = calcPeriodDict.find(item => item.value === value);
  return periodItem ? periodItem.label : value || '未设置';
};

// 还款记录日志类型字典
const logTypeDict = [
  { label: "还款", value: "REPAY" },
  { label: "审核", value: "REVIEW" },
  { label: "系统", value: "SYSTEM" }
];

// 还款记录日志状态字典
const logStatusDict = [
  { label: "新建", value: "new" },
  { label: "审核中", value: "pending" },
  { label: "已通过", value: "approved" },
  { label: "已拒绝", value: "rejected" },
  { label: "已完成", value: "completed" }
];

// 获取日志类型标签
const getLogTypeLabel = (value) => {
  const typeItem = logTypeDict.find(item => item.value === value);
  return typeItem ? typeItem.label : value || '未知';
};

// 获取日志类型颜色
const getLogTypeColor = (value) => {
  const colorMap = {
    'REPAY': 'green',
    'REVIEW': 'blue',
    'SYSTEM': 'grey'
  };
  return colorMap[value] || 'grey';
};

// 获取日志状态标签
const getLogStatusLabel = (value) => {
  const statusItem = logStatusDict.find(item => item.value === value);
  return statusItem ? statusItem.label : value || '未知';
};

// 获取日志状态颜色
const getLogStatusColor = (value) => {
  const colorMap = {
    'new': 'blue',
    'pending': 'orange',
    'approved': 'green',
    'rejected': 'red',
    'completed': 'teal'
  };
  return colorMap[value] || 'grey';
};

// 格式化日期时间
const formatDateTime = (timestamp) => {
  if (!timestamp) return '-';
  return FormatDateTime(timestamp);
};

// 格式化金额（保留两位小数）
const formatAmountWithDecimals = (amount) => {
  if (!amount) return "0.00";
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

const handleGetDetail = async () => {
  const res = await getActionByPath(url.item, [route.query.id]);
  if (res.code === 200) {
    itemDetail.value = res.data;
    // 异步加载关联订单列表
    setTimeout(() => {
      getRelatedOrderList();
    }, 100);
  }
};

// 打开编辑对话框
const handleEditRepayment = () => {
  itemDetail.value.contract = contractDetail.value
  repaymentEditDialog.value.show(itemDetail.value);
};

// 处理还款计划保存
const handleRepaymentSave = async (data) => {
  try {
    delete data.contract;
    const res = await putAction(url.item, data);
    if (res.code === 200) {
      Notify.create({
        type: "positive",
        message: res.msg || "保存成功",
        position: "top-right",
      });
      // 重新获取详情数据
      await handleGetDetail();
    } else {
      Notify.create({
        type: "negative",
        message: res.msg || "保存失败",
        position: "top-right",
      });
    }
  } catch (error) {
    console.error('保存还款计划失败:', error);
    Notify.create({
      type: "negative",
      message: "保存失败，请重试",
      position: "top-right",
    });
  }
};

const closeTab = () => {
  tabMenuStore.removeTagViewByFullPath(route.fullPath);
};

// 审核还款计划
const reviewRepayment = () => {
  showReviewDialog.value = true;
  reviewOption.value = 'approve';
  reviewRemark.value = '';
};

// 取消审核
const cancelReview = () => {
  showReviewDialog.value = false;
  reviewOption.value = 'approve';
  reviewRemark.value = '';
};

// 确认审核
const confirmReview = async () => {
  try {
    // 构建请求数据
    const requestData = {
      confirm: reviewOption.value === 'approve', // true为审核通过，false为审核拒绝
      remark: reviewRemark.value || '' // 审核备注
    };

    // 发送请求到后端
    const response = await postAction(`${url.item}/${itemDetail.value.id}`, requestData);

    if (response.code === 200) {
      Notify.create({
        type: 'positive',
        message: `还款计划${reviewOption.value === 'approve' ? '审核通过' : '审核拒绝'}成功`,
        position: 'top-right'
      });

      // 关闭弹窗
      showReviewDialog.value = false;

      // 刷新页面数据
      await handleGetDetail();
    } else {
      Notify.create({
        type: 'negative',
        message: response.msg || '审核操作失败',
        position: 'top-right'
      });
    }
  } catch (error) {
    console.error('审核还款计划失败:', error);
    Notify.create({
      type: 'negative',
      message: '审核操作失败，请重试',
      position: 'top-right'
    });
  }
};

// 添加还款记录
const addRepayment = () => {
  repaymentLogDialog.value.show(itemDetail.value);
};

// 处理还款记录添加完成
const handleRepaymentAdded = async () => {
  // 重新获取还款计划详情，更新状态和金额
  await handleGetDetail();
  // 刷新还款记录列表
  await getRepaymentLogList();

  Notify.create({
    type: "info",
    message: "还款记录已添加，请等待审核",
    position: "top-right",
  });
};

// 查看还款记录详情
const handleViewRepaymentLog = (logData) => {
  repaymentLogDetailDialog.value.show(logData, itemDetail.value);
};

// 处理审核完成
const handleReviewCompleted = async () => {
  // 刷新还款记录列表
  await getRepaymentLogList();
  // 刷新还款计划详情
  await handleGetDetail();
};
</script>

<style scoped lang="scss">
.title-width {
  font-size: 1.125rem;
  font-weight: 500;
  line-height: 2rem;
  letter-spacing: 0.0125em;
  width: 200px;
}
</style>
