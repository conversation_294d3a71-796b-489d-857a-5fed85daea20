<template>
  <q-dialog v-model="repaymentEditVisible" full-height position="right">
    <q-card style="width: 1000px; max-width: 90vw; height: 100%">
      <q-card-section>
        <div class="text-h6">编辑还款计划</div>
      </q-card-section>

      <q-separator />

      <q-card-section class="scroll">
        <q-form ref="repaymentEditForm">
          <!-- 基本信息 -->
          <div class="text-subtitle1 q-mb-md text-primary">基本信息</div>
          <div class="row q-gutter-md q-mb-md">
            <div class="col-4">
              <q-input v-model="repaymentDetail.serial" label="还款计划编号" outlined dense hint="如果不手动填写计划编号，系统将自动生成" />
            </div>
            <div class="col">
              <q-option-group v-model="repaymentDetail.status" :options="statusDict" color="primary" inline />
            </div>
          </div>

          <!-- 金额信息 -->
          <div class="text-subtitle1 q-mb-md text-primary">金额信息</div>
          <div class="text-subtitle2 q-mb-sm text-warning">
            最大可用合同金额：{{ formatAmount(availableAmount) }}（元）
          </div>
          <div class="row q-gutter-md q-mb-md">
            <div class="col">
              <q-input v-model="repaymentDetail.principal_amount" label="支取本金金额" outlined dense type="number"
                step="0.01" :max="availableAmount" :rules="[
                  val => val !== null && val !== '' || '请输入支取本金金额',
                  val => val > 0 || '支取金额必须大于0',
                  val => val <= availableAmount || `支取金额不能超过可用金额 ${formatAmount(availableAmount)} 元`
                ]" :hint="`可用金额：${formatAmount(availableAmount)} 元`" />
            </div>
            <div class="col">
              <div class="row">
                <div class="col-auto text-h6 text-blue-10">已关联订单金额：</div>
                <div class="col text-h6 text-orange-10">{{ repaymentDetail.target_amount ?
                  repaymentDetail.target_amount : 0 }}（元）</div>
              </div>
            </div>
          </div>

          <!-- 时间限制 -->
          <div class="text-subtitle1 q-mb-md text-primary">时间限制</div>
          <div class="row q-gutter-md q-mb-md">
            <div class="col">
              <q-input v-model="repaymentDetail.begin_date" label="开始日期" outlined dense type="date" />
            </div>
            <div class="col">
              <q-input v-model="repaymentDetail.end_date" label="结束日期" outlined dense type="date" />
            </div>
            <div class="col">
              <q-input v-model="repaymentDetail.grace_period" label="宽限天数" outlined dense type="number" step="1" />
            </div>
          </div>

          <!-- 利润计算配置 -->
          <div class="text-subtitle1 q-mb-md text-primary">利润计算配置</div>
          <div class="row q-gutter-md q-mb-md">
            <div class="col">
              <q-input v-model="profitCalcFeePercent" label="利润计算费率" outlined dense type="number" min="0" max="100"
                suffix="%" hint="输入费用费率（0-100整数，如输入7表示7%即0.07）" :rules="[
                  (val) => (val !== null && val !== undefined && val !== '') || '费用费率为必填项',
                  (val) => (val >= 0 && val <= 100) || '费用费率必须在0-100之间'
                ]" />
            </div>
            <div class="col">
              <q-select v-model="repaymentDetail.profit_calc_period" :options="calcPeriodDict" option-label="label"
                option-value="value" emit-value map-options label="利润计算周期" outlined dense />
            </div>
            <div class="col">
              <q-input v-model="penaltyCalcFeePercent" label="违约金计算费率" outlined dense type="number" min="0" max="100"
                suffix="%" hint="输入违约金费率（0-100整数，如输入5表示5%即0.05）" :rules="[
                  (val) => (val !== null && val !== undefined && val !== '') || '违约金费率为必填项',
                  (val) => (val >= 0 && val <= 100) || '违约金费率必须在0-100之间'
                ]" />
            </div>
          </div>

          <!-- 备注信息 -->
          <div class="text-subtitle1 q-mb-md text-primary">备注信息</div>
          <div class="row ">
            <div class="col-12">
              <q-input v-model="repaymentDetail.remark" label="还款计划备注" outlined dense type="textarea" rows="3" />
            </div>
          </div>
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn label="保存" color="primary" @click="handleSave" />
        <q-btn v-close-popup label="取消" color="negative" />
      </q-card-actions>

      <q-inner-loading :showing="loading">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Notify } from 'quasar'

defineOptions({ name: "RepaymentEditDialog" })

const emit = defineEmits(["handleFinish"])

// 响应式数据
const repaymentEditVisible = ref(false)
const repaymentDetail = ref({})
const loading = ref(false)
const repaymentEditForm = ref()

// 状态字典
const statusDict = [
  { label: "草稿", value: "draft" },
  { label: "待审核", value: "new" },
  { label: "已审核", value: "processing" },
  { label: "待还款", value: "pending" },
  { label: "部分还款", value: "partial" },
  { label: "已完成", value: "completed" },
  { label: "逾期", value: "overdue" },
]

// 计算周期字典
const calcPeriodDict = [
  { label: "单次", value: "ONCE" },
  { label: "按天", value: "DAY" },
  { label: "按月", value: "MONTH" },
  { label: "按年", value: "YEAR" },
  { label: "按季度", value: "QUARTER" },
]

// 计算可用金额
const availableAmount = computed(() => {
  if (!repaymentDetail.value.contract) return 0
  const confirmQuota = Number(repaymentDetail.value.contract.confirm_quota) || 0
  const usedQuota = Number(repaymentDetail.value.contract.used_quota) || 0
  return Math.max(0, confirmQuota - usedQuota)
})

// 格式化金额
const formatAmount = (amount) => {
  if (!amount) return "0"
  return Number(amount).toLocaleString()
}

// 计算属性：将0-100的整数转换为两位小数
const profitCalcFeePercent = computed({
  get: () => {
    // 将小数转换为百分比整数显示（如0.07显示为7）
    return Math.round((repaymentDetail.value.profit_calc_fee || 0) * 100);
  },
  set: (value) => {
    // 将百分比整数转换为小数存储（如7转换为0.07）
    repaymentDetail.value.profit_calc_fee = (value || 0) / 100;
  }
});

const penaltyCalcFeePercent = computed({
  get: () => {
    // 将小数转换为百分比整数显示（如0.05显示为5）
    return Math.round((repaymentDetail.value.penalty_calc_fee || 0) * 100);
  },
  set: (value) => {
    // 将百分比整数转换为小数存储（如5转换为0.05）
    repaymentDetail.value.penalty_calc_fee = (value || 0) / 100;
  }
});

// 显示对话框
const show = (data = {}) => {
  repaymentDetail.value = { ...data }
  repaymentEditVisible.value = true
}

// 保存处理
const handleSave = async () => {
  try {
    const success = await repaymentEditForm.value.validate()
    if (success) {
      loading.value = true
      // 触发父组件的保存事件
      emit('handleFinish', repaymentDetail.value)
      repaymentEditVisible.value = false

      Notify.create({
        type: "positive",
        message: "保存成功",
        position: "top-right",
      })
    } else {
      Notify.create({
        type: "negative",
        message: "请检查表单信息是否正确",
        position: "top-right",
      })
    }
  } catch (error) {
    console.error('保存失败:', error)
    Notify.create({
      type: "negative",
      message: "保存失败，请重试",
      position: "top-right",
    })
  } finally {
    loading.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  show
})
</script>

<style scoped>
.scroll {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}
</style>
