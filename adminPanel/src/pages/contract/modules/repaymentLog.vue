<template>
  <q-dialog v-model="repaymentLogVisible" persistent full-height position="right">
    <q-card style="min-width: 500px; max-width: 600px">
      <q-card-section>
        <div class="text-h6">添加还款记录</div>
      </q-card-section>

      <q-separator />

      <q-card-section class="q-pt-md">
        <q-form @submit="submitRepayment" class="q-gutter-md">
          <!-- 还款金额 -->
          <q-input v-model="formData.log_value" type="number" label="还款金额 *" outlined :rules="[
            val => val !== null && val !== undefined && val !== '' || '请输入还款金额',
            val => val > 0 || '还款金额必须大于0'
          ]" prefix="¥" step="0.01" min="0" />

          <!-- 还款日期 -->
          <q-input v-model="formData.log_date" label="还款日期 *" outlined :rules="[val => !!val || '请选择还款日期']">
            <template v-slot:append>
              <q-icon name="event" class="cursor-pointer">
                <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                  <q-date v-model="formData.log_date" mask="YYYY-MM-DD">
                    <div class="row items-center justify-end">
                      <q-btn v-close-popup label="确定" color="primary" flat />
                    </div>
                  </q-date>
                </q-popup-proxy>
              </q-icon>
            </template>
          </q-input>

          <!-- 备注 -->
          <q-input v-model="formData.remark" type="textarea" label="备注" outlined rows="3" placeholder="请输入还款备注信息（可选）" />

          <!-- 文件上传 -->
          <q-file v-model="selectedFile" label="上传凭证文件（可选）" outlined accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
            max-file-size="10485760" @rejected="onFileRejected">
            <template v-slot:prepend>
              <q-icon name="attach_file" />
            </template>
          </q-file>
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn flat label="取消" color="grey" @click="closeDialog" />
        <q-btn push label="提交还款" color="primary" :loading="submitting" @click="submitRepayment" />
      </q-card-actions>

      <q-inner-loading :showing="submitting">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, reactive } from "vue";
import { Notify } from "quasar";
import { postFormDataAction } from "src/api/manage.ts";

const repaymentLogVisible = ref(false);
const repaymentData = ref({});
const selectedFile = ref(null);
const submitting = ref(false);

// 表单数据
const formData = reactive({
  log_value: null,
  log_date: '',
  remark: ''
});

// URL配置
const url = {
  repaymentLog: "/api/repayment_log"
};

// 显示弹窗
const show = (repayment) => {
  repaymentData.value = repayment;
  repaymentLogVisible.value = true;
  resetForm();
};

// 重置表单
const resetForm = () => {
  formData.log_value = null;
  formData.log_date = '';
  formData.remark = '';
  selectedFile.value = null;
};

// 关闭弹窗
const closeDialog = () => {
  repaymentLogVisible.value = false;
  resetForm();
};

// 提交还款记录
const submitRepayment = async () => {
  // 表单验证
  if (!formData.log_value || formData.log_value <= 0) {
    Notify.create({
      type: "warning",
      message: "请输入有效的还款金额",
      position: "top-right",
    });
    return;
  }

  if (!formData.log_date) {
    Notify.create({
      type: "warning",
      message: "请选择还款日期",
      position: "top-right",
    });
    return;
  }

  submitting.value = true;
  try {
    const formDataToSubmit = new FormData();

    // 添加文件（如果有）
    if (selectedFile.value) {
      formDataToSubmit.append("file", selectedFile.value);
    }

    // 构建 objectData
    const objectData = {
      parent_id: repaymentData.value.id,
      log_type: "REPAY",
      log_value: formData.log_value,
      log_date: formData.log_date,
      log_status: "new",
      remark: formData.remark || '',
    };

    formDataToSubmit.append("objectData", JSON.stringify(objectData));

    const { code, msg } = await postFormDataAction(url.repaymentLog, formDataToSubmit);

    if (code === 200) {
      Notify.create({
        type: "positive",
        message: msg || "还款记录添加成功",
        position: "top-right",
      });

      // 关闭弹窗并触发刷新事件
      closeDialog();
      emit('repaymentAdded');
    } else {
      Notify.create({
        type: "negative",
        message: msg || "还款记录添加失败",
        position: "top-right",
      });
    }
  } catch (error) {
    console.error("提交还款记录失败:", error);
    Notify.create({
      type: "negative",
      message: "提交还款记录失败，请重试",
      position: "top-right",
    });
  } finally {
    submitting.value = false;
  }
};

// 文件被拒绝处理
const onFileRejected = (rejectedEntries) => {
  const reasons = rejectedEntries.map(entry => {
    if (entry.failedPropValidation === 'max-file-size') {
      return '文件大小超过10MB限制';
    }
    return '文件格式不支持';
  });

  Notify.create({
    type: "warning",
    message: reasons.join(', '),
    position: "top-right",
  });
};

// 定义事件
const emit = defineEmits(['repaymentAdded']);

// 暴露方法
defineExpose({
  show,
});
</script>

<style scoped>
.q-form {
  max-width: 100%;
}
</style>
