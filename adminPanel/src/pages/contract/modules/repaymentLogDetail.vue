<template>
  <q-dialog v-model="showDialog" persistent full-height position="right">
    <q-card style="min-width: 800px; max-width: 1000px;">
      <q-card-section>
        <div class="text-h6">还款记录详情</div>
      </q-card-section>

      <q-separator />

      <q-card-section class="q-pt-none">
        <div v-if="logDetail.id" class="q-pa-md">
          <!-- 基本信息 -->
          <div class="row q-mb-md">
            <div class="col-6 q-pa-sm">
              <q-input
                v-model="logDetail.log_status"
                label="日志状态"
                readonly
                outlined
                dense
              />
            </div>
            <div class="col-6 q-pa-sm">
              <q-input
                v-model="logDetail.creater_name"
                label="操作人"
                readonly
                outlined
                dense
              />
            </div>
          </div>

          <div class="row q-mb-md">
            <div class="col-6 q-pa-sm">
              <q-input
                v-model="logDetail.log_value"
                label="记录值"
                readonly
                outlined
                dense
              />
            </div>
            <div class="col-6 q-pa-sm">
              <q-input
                v-model="logDetail.log_date"
                label="日期"
                readonly
                outlined
                dense
              />
            </div>
          </div>

          <div class="row q-mb-md">
            <div class="col-12 q-pa-sm">
              <q-input
                v-model="logDetail.remark"
                label="备注"
                type="textarea"
                readonly
                outlined
                rows="3"
              />
            </div>
          </div>

          <!-- 还款计划剩余金额信息 (仅当状态不是draft或new时显示) -->
          <div v-if="repaymentDetail.status && repaymentDetail.status !== 'draft' && repaymentDetail.status !== 'new'" class="q-mb-md">
            <div class="text-h6 q-mb-md text-primary">还款计划剩余金额</div>
            <div class="row q-gutter-md">
              <div class="col-4 q-pa-sm">
                <q-input
                  :model-value="formatAmount(repaymentDetail.profit_remain)"
                  label="剩余利润"
                  readonly
                  outlined
                  dense
                  prefix="¥"
                />
              </div>
              <div class="col-4 q-pa-sm">
                <q-input
                  :model-value="formatAmount(repaymentDetail.principal_remain)"
                  label="剩余本金"
                  readonly
                  outlined
                  dense
                  prefix="¥"
                />
              </div>
              <div class="col-4 q-pa-sm">
                <q-input
                  :model-value="formatAmount(repaymentDetail.total_remain)"
                  label="剩余总额"
                  readonly
                  outlined
                  dense
                  prefix="¥"
                />
              </div>
            </div>
          </div>

          <!-- 附件列表 -->
          <div class="q-mb-md">
            <div class="text-h6 q-mb-md">相关附件</div>
            <q-table
              :rows="attachments"
              :columns="attachmentColumns"
              :loading="attachmentsLoading"
              row-key="id"
              flat
              bordered
              hide-pagination
              :rows-per-page-options="[0]"
            >
              <!-- 文件链接列 -->
              <template v-slot:body-cell-file_link="props">
                <q-td :props="props">
                  <q-btn
                    v-if="props.value"
                    round
                    flat
                    color="primary"
                    size="sm"
                label="查看"
                icon="visibility"
                    @click="openFile(props.value)"
                  >
                    <q-tooltip>查看文件</q-tooltip>
                  </q-btn>
                  <span v-else class="text-grey-5">-</span>
                </q-td>
              </template>

              <!-- 创建时间列格式化 -->
              <template v-slot:body-cell-created_at="props">
                <q-td :props="props">
                  {{ formatDateTime(props.value) }}
                </q-td>
              </template>

              <!-- 无数据显示 -->
              <template v-slot:no-data>
                <div class="full-width row flex-center text-grey q-gutter-sm">
                  <q-icon size="2em" name="attach_file" />
                  <span>暂无相关附件</span>
                </div>
              </template>
            </q-table>
          </div>

          <!-- 审核按钮 -->
          <div v-if="logDetail.log_status === 'new' || logDetail.log_status === 'pending'" class="q-mt-md">
            <q-btn
              label="审核记录"
              color="secondary"
              icon="rate_review"
              @click="showReviewDialog"
            />
          </div>
        </div>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn flat label="关闭" color="primary" @click="hideDialog" />
      </q-card-actions>
    </q-card>

    <!-- 审核弹窗 -->
    <q-dialog v-model="showReview" persistent>
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">审核还款记录</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <div class="q-mb-md">
            <q-option-group
              v-model="reviewOption"
              :options="reviewOptions"
              color="primary"
              inline
            />
          </div>

          <div class="q-mb-md">
            <q-input
              v-model="reviewRemark"
              type="textarea"
              label="审核备注"
              placeholder="请输入审核备注（可选）"
              rows="3"
              outlined
            />
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="primary" @click="cancelReview" />
          <q-btn push label="确认" color="primary" @click="confirmReview" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-dialog>
</template>

<script setup>
import { ref, defineEmits, defineExpose } from 'vue';
import { Notify } from 'quasar';
import { postAction, putAction } from 'src/api/manage';
import { FormatDateTime } from 'src/utils/date';
import { transFileUrl } from "src/utils/urlTrans";

const emit = defineEmits(['reviewCompleted']);

// 弹窗控制
const showDialog = ref(false);
const showReview = ref(false);

// 数据
const logDetail = ref({});
const repaymentDetail = ref({});
const attachments = ref([]);
const attachmentsLoading = ref(false);

// 审核相关
const reviewOption = ref('approve');
const reviewRemark = ref('');
const reviewOptions = [
  { label: '审核通过', value: 'approve' },
  { label: '审核拒绝', value: 'reject' }
];

// 附件表格列定义
const attachmentColumns = [
  {
    name: 'title',
    label: '标题',
    field: 'title',
    align: 'left',
    sortable: false
  },
  {
    name: 'file_name',
    label: '文件名',
    field: 'file_name',
    align: 'left',
    sortable: false
  },
  {
    name: 'file_link',
    label: '文件链接',
    field: 'file_link',
    align: 'center',
    sortable: false
  },
  {
    name: 'created_at',
    label: '上传时间',
    field: 'created_at',
    align: 'center',
    sortable: false
  }
];

// 格式化日期时间
const formatDateTime = (timestamp) => {
  if (!timestamp) return '-';
  return FormatDateTime(timestamp);
};

// 格式化金额（保留两位小数）
const formatAmount = (amount) => {
  if (!amount) return "0.00";
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 获取附件列表
const getAttachments = async (logId) => {
  if (!logId) return;

  attachmentsLoading.value = true;
  try {
    const queryParams = {
      page: { page: 1, limit: 0 },
      options: { order_by: 'created_at', desc: true },
      params: [
        { var: "entity_id", val: logId },
        { var: "attachment_type", val: "repayment_log_attachment" },
      ],
    };

    const response = await postAction('/api/attachment/list', queryParams);
    console.log(response, "附件列表");

    if (response.code === 200) {
      attachments.value = response.data || [];
    } else {
      attachments.value = [];
      console.warn('获取附件列表失败:', response.msg);
    }
  } catch (error) {
    console.error('获取附件列表出错:', error);
    attachments.value = [];
  } finally {
    attachmentsLoading.value = false;
  }
};

// 打开文件
const openFile = (fileLink) => {
  if (fileLink) {
    window.open(transFileUrl(fileLink), '_blank');
  }
};

// 显示弹窗
const show = async (logData, repaymentData = {}) => {
  logDetail.value = { ...logData };
  repaymentDetail.value = { ...repaymentData };
  // 获取附件列表
  await getAttachments(logData.id);
  showDialog.value = true;
};

// 隐藏弹窗
const hideDialog = () => {
  showDialog.value = false;
  logDetail.value = {};
  repaymentDetail.value = {};
  attachments.value = [];
};

// 显示审核弹窗
const showReviewDialog = () => {
  showReview.value = true;
  reviewOption.value = 'approve';
  reviewRemark.value = '';
};

// 取消审核
const cancelReview = () => {
  showReview.value = false;
  reviewOption.value = 'approve';
  reviewRemark.value = '';
};

// 确认审核
const confirmReview = async () => {
  try {
    logDetail.value.log_status = reviewOption.value === 'approve' ? 'approved' : 'rejected';

    // 发送审核请求
    const response = await putAction("/api/repayment_log", logDetail.value);

    if (response.code === 200) {
      Notify.create({
        type: 'positive',
        message: `还款记录${reviewOption.value === 'approve' ? '审核通过' : '审核拒绝'}成功`,
        position: 'top-right'
      });

      // 关闭审核弹窗
      showReview.value = false;

      // 更新记录状态
      logDetail.value.log_status = reviewOption.value === 'approve' ? 'approved' : 'rejected';

      // 通知父组件刷新数据
      emit('reviewCompleted');

      // 关闭详情弹窗
      hideDialog();
    } else {
      Notify.create({
        type: 'negative',
        message: response.msg || '审核操作失败',
        position: 'top-right'
      });
    }
  } catch (error) {
    console.error('审核还款记录失败:', error);
    Notify.create({
      type: 'negative',
      message: '审核操作失败，请重试',
      position: 'top-right'
    });
  }
};

// 暴露方法
defineExpose({
  show,
  hideDialog
});
</script>

<style scoped lang="scss">
.q-card {
  border-radius: 8px;
}

.text-h6 {
  color: #1976d2;
  font-weight: 600;
}
</style>
