<template>
  <q-dialog v-model="attachmentListVisible" full-height position="right">
    <q-card style="width: 900px; max-width: 90vw; height: 100%">
      <q-card-section>
        <div class="text-h6">合同附件管理</div>
      </q-card-section>

      <q-separator />

      <q-card-section class="q-pt-none">
        <!-- 上传区域 -->
        <div v-if="canUpload" class="q-mb-md">
          <q-card flat bordered>
            <q-card-section>
              <div class="text-subtitle2 q-mb-sm">上传新附件</div>
              <div class="row q-gutter-md items-center">
                <div class="col-5">
                  <q-file v-model="selectedFile" label="选择文件" outlined accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
                    max-file-size="10485760" @rejected="onFileRejected">
                    <template v-slot:prepend>
                      <q-icon name="attach_file" />
                    </template>
                  </q-file>
                </div>
                <div class="col-5">
                  <q-input v-model="uploadTitle" label="附件标题" outlined style="min-width: 200px" />
                </div>
                <div class="col-auto">
                  <q-btn color="primary" icon="upload" label="上传" :disable="!selectedFile || uploading"
                    :loading="uploading" @click="uploadFile" />
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- 附件列表 -->
        <q-table :rows="attachmentList" :columns="columns" row-key="id" :loading="loading" :pagination="pagination"
          @request="onRequest" binary-state-sort>
          <template v-slot:body-cell-file_link="props">
            <q-td :props="props">
              <q-btn flat color="primary" label="查看" icon="visibility" @click="openFile(props.row.file_link)" />
            </q-td>
          </template>

          <template v-slot:body-cell-created_at="props">
            <q-td :props="props">
              {{ formatDateTime(props.row.created_at) }}
            </q-td>
          </template>

          <template v-slot:body-cell-actions="props">
            <q-td :props="props">
              <q-btn flat color="negative" icon="delete" size="sm" @click="deleteAttachment(props.row)"
                v-if="canUpload">
                <q-tooltip>删除附件</q-tooltip>
              </q-btn>
            </q-td>
          </template>
        </q-table>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn v-close-popup label="关闭" color="grey" />
      </q-card-actions>

      <q-inner-loading :showing="loading">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed } from "vue";
import { Notify } from "quasar";
import { postAction, postFormDataAction } from "src/api/manage.ts";
import { FormatTimeStamp } from "src/utils/date";
import { transFileUrl } from "src/utils/urlTrans";

const attachmentListVisible = ref(false);
const contractData = ref({});
const attachmentList = ref([]);
const loading = ref(false);
const uploading = ref(false);
const selectedFile = ref(null);
const uploadTitle = ref("");

// 表格配置
const columns = [
  {
    name: "title",
    required: true,
    label: "标题",
    align: "left",
    field: "title",
    sortable: true,
  },
  {
    name: "file_name",
    required: true,
    label: "文件名",
    align: "left",
    field: "file_name",
    sortable: true,
  },
  {
    name: "file_link",
    required: true,
    label: "操作",
    align: "center",
    field: "file_link",
  },
  {
    name: "created_at",
    required: true,
    label: "上传时间",
    align: "center",
    field: "created_at",
    sortable: true,
  },
  {
    name: "actions",
    required: true,
    label: "操作",
    align: "center",
    field: "actions",
  },
];

const pagination = ref({
  sortBy: "created_at",
  descending: true,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
});

// URL配置
const url = {
  attachment: "/api/attachment/list",
  upload: "/api/contract_log",
};

// 判断是否可以上传（根据合同状态）
const canUpload = computed(() => {
  return contractData.value.status === "draft" || contractData.value.status === "new";
});

// 显示弹窗
const show = (contract) => {
  contractData.value = contract;
  attachmentListVisible.value = true;
  loadAttachmentList();
};

// 加载附件列表
const loadAttachmentList = async () => {
  loading.value = true;
  try {
    const queryParams = {
      page: { page: pagination.value.page, limit: pagination.value.rowsPerPage },
      options: null,
      params: [
        { var: "entity_id", val: contractData.value.id },
        { var: "attachment_type", val: "financial_contract_attachment" },
      ],
    };

    const { code, data } = await postAction(url.attachment, queryParams);
    if (code === 200) {
      attachmentList.value = data || [];
      pagination.value.rowsNumber = data?.length || 0;
    } else {
      attachmentList.value = [];
      Notify.create({
        type: "warning",
        message: "附件列表加载失败",
        position: "top-right",
      });
    }
  } catch (error) {
    console.error("加载附件列表失败:", error);
    attachmentList.value = [];
    Notify.create({
      type: "negative",
      message: "附件列表加载失败",
      position: "top-right",
    });
  } finally {
    loading.value = false;
  }
};

// 表格请求处理
const onRequest = (props) => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;
  pagination.value.page = page;
  pagination.value.rowsPerPage = rowsPerPage;
  pagination.value.sortBy = sortBy;
  pagination.value.descending = descending;
  loadAttachmentList();
};

// 文件上传
const uploadFile = async () => {
  if (!selectedFile.value) {
    Notify.create({
      type: "warning",
      message: "请选择要上传的文件",
      position: "top-right",
    });
    return;
  }

  uploading.value = true;
  try {
    const formData = new FormData();
    formData.append("file", selectedFile.value);

    const objectData = {
      parent_id: contractData.value.id,
      log_type: "UPLOAD",
      log_value: uploadTitle.value || selectedFile.value.name,
      remark: selectedFile.value.name,
    };

    formData.append("objectData", JSON.stringify(objectData));

    const { code, msg } = await postFormDataAction(url.upload, formData);
    if (code === 200) {
      Notify.create({
        type: "positive",
        message: msg || "文件上传成功",
        position: "top-right",
      });

      // 重置表单
      selectedFile.value = null;
      uploadTitle.value = "";

      // 重新加载列表
      loadAttachmentList();
    } else {
      Notify.create({
        type: "negative",
        message: msg || "文件上传失败",
        position: "top-right",
      });
    }
  } catch (error) {
    console.error("文件上传失败:", error);
    Notify.create({
      type: "negative",
      message: "文件上传失败",
      position: "top-right",
    });
  } finally {
    uploading.value = false;
  }
};

// 文件被拒绝处理
const onFileRejected = (rejectedEntries) => {
  const reasons = rejectedEntries.map(entry => {
    if (entry.failedPropValidation === 'max-file-size') {
      return '文件大小超过10MB限制';
    }
    return '文件格式不支持';
  });

  Notify.create({
    type: "warning",
    message: reasons.join(', '),
    position: "top-right",
  });
};

// 打开文件
const openFile = (fileLink) => {
  if (fileLink) {
    window.open(transFileUrl(fileLink), "_blank");
  }
};

// 删除附件
const deleteAttachment = (attachment) => {
  // TODO: 实现删除附件功能
  Notify.create({
    type: "info",
    message: "删除功能待实现",
    position: "top-right",
  });
};

// 格式化时间
const formatDateTime = (timestamp) => {
  return FormatTimeStamp(timestamp);
};

// 暴露方法
defineExpose({
  show,
});
</script>

<style scoped>
.q-table {
  max-height: 60vh;
}
</style>
