<template>
  <BaseContent scrollable>
    <div class="row q-ma-md">
      <div class="col">
        <q-table
          v-model:pagination="pagination"
          row-key="id"
          separator="cell"
          :rows="tableData"
          :columns="columns"
          :rows-per-page-options="pageOptions"
          :loading="loading"
          @request="onRequest"
        >
          <template #top="props">
            <q-btn-group>
              <q-btn
                color="primary"
                label="导入订单"
                icon="add"
                @click="showAddForm"
              />
            </q-btn-group>
            <q-space />
            <q-btn
              flat
              round
              dense
              :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
              class="q-ml-md"
              @click="props.toggleFullscreen"
            />
          </template>

          <template #body-cell-status="props">
            <q-td :props="props">
              <q-chip
                v-if="props.row.status === 'success'"
                square
                outline
                size="sm"
                color="positive"
                label="成功"
              />
              <q-chip
                v-else-if="props.row.status === 'failed'"
                square
                outline
                size="sm"
                color="negative"
                label="失败"
              />
              <q-chip
                v-else-if="props.row.status === 'pending'"
                square
                outline
                size="sm"
                color="warning"
                label="待处理"
              />
              <q-chip
                v-else
                square
                outline
                size="sm"
                :label="props.row.status || '未知'"
              />
            </q-td>
          </template>

          <template #body-cell-actions="props">
            <q-td :props="props">
              <div class="q-gutter-xs">
                <q-btn-group>
                  <q-btn
                    color="secondary"
                    :label="t('admin.Detail')"
                    size="sm"
                    @click="showDetail(props.row)"
                  />
                  <q-btn
                    v-if="!props.row.stable"
                    color="negative"
                    :label="t('admin.Delete')"
                    size="sm"
                    @click="handleDelete(props.row)"
                  />
                </q-btn-group>
              </div>
            </q-td>
          </template>
        </q-table>
      </div>
    </div>
    <RecordImport ref="recordDetailDialog" @handleFinish="handleFinish" />
    <RecordDetail ref="recordDetailRef" @handleFinish="handleFinish" />
  </BaseContent>
</template>

<script setup>
import { useQuasar } from "quasar";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import useTableData from "src/composables/useTableData";
import { computed, onMounted, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import RecordImport from "./modules/recordImport.vue";
import RecordDetail from "./modules/recordDetail.vue";

import { FormatTimeStamp } from "src/utils/date";

const router = useRouter();
const $q = useQuasar();
const { t } = useI18n();
const url = {
  list: "/api/import_record/list",
  create: "/api/import_record",
  edit: "/api/import_record",
  delete: "/api/import_record",
};
const columns = computed(() => {
  return [
    {
      name: "created_at",
      align: "center",
      label: "导入时间",
      field: "created_at",
      format: (val) => FormatTimeStamp(val),
    },
    {
      name: "contract_id",
      align: "center",
      label: "项目名称",
      field: "contract_name",
    },
    {
      name: "source",
      required: true,
      align: "center",
      label: "数据来源",
      field: "source",
    },
    {
      name: "file_name",
      required: true,
      align: "center",
      label: "文件名",
      field: "file_name",
    },
    {
      name: "file_type",
      align: "center",
      label: "文件类型",
      field: "file_type",
    },
    {
      name: "total_count",
      required: true,
      align: "center",
      label: "总数量",
      field: "total_count",
    },
    {
      name: "success_count",
      required: true,
      align: "center",
      label: "成功数量",
      field: "success_count",
    },
    {
      name: "fail_count",
      required: true,
      align: "center",
      label: "失败数量",
      field: "fail_count",
    },
    {
      name: "new_count",
      align: "center",
      label: "新增数量",
      field: "new_count",
    },
    {
      name: "update_count",
      align: "center",
      label: "更新数量",
      field: "update_count",
    },
    {
      name: "status",
      align: "center",
      label: "导入状态",
      field: "status",
    },
    {
      name: "actions",
      required: true,
      align: "center",
      label: "操作",
      field: "actions",
    },
  ];
});
const {
  pagination,
  queryParams,
  pageOptions,
  loading,
  tableData,
  recordDetailDialog,
  onRequest,
  getTableData,
  showAddForm,
  showEditForm,
  handleDelete,
  handleFinish,
} = useTableData(url);


onMounted(() => {
  pagination.value.sortBy = "id";
  getTableData();
});

const handleDetail = (item) => {
  router.push({ name: "NavigatorDetail", query: { id: item.id } });
};

const recordDetailRef = ref(null);
const showDetail = async(item) => {
  recordDetailRef.value.show(item);
}
</script>
