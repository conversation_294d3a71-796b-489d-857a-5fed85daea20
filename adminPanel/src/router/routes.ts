import { Route } from "src/types";

declare module "vue-router" {
  interface RouteMeta {
    roles?: string[];
    title: string;
    icon?: string;
    itemLabel?: string;
    keepAlive?: boolean;
    isOpen?: boolean;
    hidden?: boolean;
  }
}

const MainLayoutFile = import.meta.glob("../layouts/MainLayout/index.vue");

const pagesFile = import.meta.glob("../pages/**/*.vue");
const layoutsFile = import.meta.glob("../layouts/**/*.vue");

const asyncRoutesChildren: Route[] = [
  {
    component: pagesFile["../pages/dashboard/Dashboard.vue"],
    path: "/",
    name: "dashboard",
    meta: {
      title: "Home",
      icon: "home",
    },
  },
];

const asyncRootRoute: Route[] = [
  {
    component: layoutsFile["../layouts/MainLayout.vue"],
    path: "/",
    name: "index",
    redirect: "/",
    children: asyncRoutesChildren,
  },
];

export { asyncRootRoute, asyncRoutesChildren };
