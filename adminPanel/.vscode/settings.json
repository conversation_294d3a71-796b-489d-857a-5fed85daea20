{"editor.bracketPairColorization.enabled": true, "editor.guides.bracketPairs": true, "typescript.tsdk": "node_modules/typescript/lib", "editor.defaultFormatter": "Vue.volar", "editor.formatOnSave": true, "[css]": {"editor.defaultFormatter": "vscode.css-language-features"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "marscode.chatLanguage": "cn", "i18n-ally.localesPaths": ["src/i18n", "public/tinymce/lang"]}