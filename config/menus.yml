# 系统菜单配置
menus:
  - name: "contractGroup"
    order: 1
    path: "/contract"
    component: ""
    redirect: ""
    active: "yes"
    title: "合同管理"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: ""
    remark: ""
  - name: "contractList"
    order: 1
    path: "/list"
    component: "views/contract/index"
    redirect: ""
    active: "yes"
    title: "合同列表"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "contractGroup"
    remark: ""
  - name: "contractDetail"
    order: 2
    path: "/detail"
    component: "views/contract/detail"
    redirect: ""
    active: "yes"
    title: "合同详情"
    icon: "group"
    keep_alive: "yes"
    hidden: "yes"
    is_link: "no"
    parent: "contractGroup"
    remark: "合约详情"
  - name: "contractCreate"
    order: 3
    path: "/create"
    component: "views/contract/create"
    redirect: ""
    active: "yes"
    title: "合同创建"
    icon: "note_add"
    keep_alive: "yes"
    hidden: "yes"
    is_link: "no"
    parent: "contractGroup"
    remark: ""
  - name: "contractEdit"
    order: 4
    path: "/edit"
    component: "views/contract/edit"
    redirect: ""
    active: "yes"
    title: "合同编辑"
    icon: "edit_note"
    keep_alive: "yes"
    hidden: "yes"
    is_link: "no"
    parent: "contractGroup"
    remark: ""
  - name: "repaymentDetail"
    order: 5
    path: "/repayment/detail"
    component: "views/contract/repaymentDetail"
    redirect: ""
    active: "yes"
    title: "还款详情"
    icon: "add"
    keep_alive: "yes"
    hidden: "yes"
    is_link: "no"
    parent: "contractGroup"
    remark: ""
  - name: "orderGroup"
    order: 2
    path: "/order"
    component: ""
    redirect: ""
    active: "yes"
    title: "订单管理"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: ""
    remark: ""
  - name: "orderList"
    order: 1
    path: "/list"
    component: "views/order/index"
    redirect: ""
    active: "yes"
    title: "订单列表"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "orderGroup"
    remark: ""
  - name: "orderCreate"
    order: 2
    path: "/create"
    component: "views/order/create"
    redirect: ""
    active: "yes"
    title: "订单创建"
    icon: "add"
    keep_alive: "yes"
    hidden: "yes"
    is_link: "no"
    parent: "orderGroup"
    remark: "创建订单"
  - name: "orderDetail"
    order: 3
    path: "/detail"
    component: "views/order/detail"
    redirect: ""
    active: "yes"
    title: "订单详情"
    icon: "group"
    keep_alive: "yes"
    hidden: "yes"
    is_link: "no"
    parent: "orderGroup"
    remark: "订单详情"
  - name: "categoryGroup"
    order: 3
    path: "/category"
    component: ""
    redirect: ""
    active: "yes"
    title: "分类管理"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: ""
    remark: ""
  - name: "categoryList"
    order: 1
    path: "/list"
    component: "views/category/index"
    redirect: ""
    active: "yes"
    title: "分类列表"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "categoryGroup"
    remark: "文章目录"
  - name: "productSkuGroup"
    order: 4
    path: "/productSku"
    component: ""
    redirect: ""
    active: "yes"
    title: "产品管理"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: ""
    remark: ""
  - name: "productSkuList"
    order: 1
    path: "/list"
    component: "views/productSku/index"
    redirect: ""
    active: "yes"
    title: "产品列表"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "productSkuGroup"
    remark: ""
  - name: "productSkuDetail"
    order: 2
    path: "/detail"
    component: "views/productSku/detail"
    redirect: ""
    active: "yes"
    title: "产品详情"
    icon: "group"
    keep_alive: "yes"
    hidden: "yes"
    is_link: "no"
    parent: "productSkuGroup"
    remark: "产品详情"
  - name: "supplierGroup"
    order: 5
    path: "/supplier"
    component: ""
    redirect: ""
    active: "yes"
    title: "供应商管理"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: ""
    remark: ""
  - name: "supplierList"
    order: 1
    path: "/list"
    component: "views/supplier/index"
    redirect: ""
    active: "yes"
    title: "供应商列表"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "supplierGroup"
    remark: ""
  - name: "warehouseGroup"
    order: 6
    path: "/warehouse"
    component: ""
    redirect: ""
    active: "yes"
    title: "仓库管理"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: ""
    remark: ""
  - name: "warehouseList"
    order: 1
    path: "/list"
    component: "views/warehouse/index"
    redirect: ""
    active: "yes"
    title: "仓库列表"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "warehouseGroup"
    remark: ""
  - name: "warehouseDetail"
    order: 2
    path: "/detail"
    component: "views/warehouse/detail"
    redirect: ""
    active: "yes"
    title: "仓库详情"
    icon: "group"
    keep_alive: "yes"
    hidden: "yes"
    is_link: "no"
    parent: "warehouseGroup"
    remark: "仓库详情"
  - name: "stockGroup"
    order: 7
    path: "/stock"
    component: ""
    redirect: ""
    active: "yes"
    title: "库存管理"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: ""
    remark: ""
  - name: "stockList"
    order: 1
    path: "/list"
    component: "views/stock/index"
    redirect: ""
    active: "yes"
    title: "库存列表"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "stockGroup"
    remark: ""
  - name: "stockDetail"
    order: 2
    path: "/detail"
    component: "views/stock/detail"
    redirect: ""
    active: "yes"
    title: "库存详情"
    icon: "group"
    keep_alive: "yes"
    hidden: "yes"
    is_link: "no"
    parent: "stockGroup"
    remark: "库存详情"
  - name: "importRecordGroup"
    order: 8
    path: "/import_record"
    component: ""
    redirect: ""
    active: "yes"
    title: "导入记录管理"
    icon: "import_export"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: ""
    remark: ""
  - name: "importRecord"
    order: 1
    path: "/list"
    component: "views/importRecord/index"
    redirect: ""
    active: "yes"
    title: "导入记录"
    icon: "import_export"
    keep_alive: "yes"
    hidden: "no"
    is_link: "no"
    parent: "importRecordGroup"
    remark: ""
  - name: "companyGroup"
    order: 95
    path: "/company"
    component: ""
    redirect: ""
    active: "yes"
    title: "企业管理"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: ""
    remark: ""
  - name: "companyList"
    order: 1
    path: "/list"
    component: "views/company/index"
    redirect: ""
    active: "yes"
    title: "企业列表"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "companyGroup"
    remark: ""
  - name: "companyCreate"
    order: 2
    path: "/create"
    component: "views/company/create"
    redirect: ""
    active: "yes"
    title: "企业创建"
    icon: "123"
    keep_alive: "yes"
    hidden: "yes"
    is_link: "no"
    parent: "companyGroup"
    remark: ""
  - name: "menuGroup"
    order: 96
    path: "/menu"
    component: ""
    redirect: ""
    active: "yes"
    title: "菜单管理"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: ""
    remark: ""
  - name: "menuList"
    order: 1
    path: "/list"
    component: "views/menu/index"
    redirect: ""
    active: "yes"
    title: "菜单列表"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "menuGroup"
    remark: ""
  - name: "permissionGroup"
    order: 97
    path: "/permission"
    component: ""
    redirect: ""
    active: "yes"
    title: "权限管理"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: ""
    remark: ""
  - name: "permissionList"
    order: 1
    path: "/list"
    component: "views/permission/index"
    redirect: ""
    active: "yes"
    title: "权限列表"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "permissionGroup"
    remark: ""
  - name: "roleGroup"
    order: 98
    path: "/role"
    component: ""
    redirect: ""
    active: "yes"
    title: "角色管理"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: ""
    remark: ""
  - name: "roleList"
    order: 1
    path: "/list"
    component: "views/role/index"
    redirect: ""
    active: "yes"
    title: "角色列表"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "roleGroup"
    remark: ""
  - name: "userGroup"
    order: 99
    path: "/user"
    component: ""
    redirect: ""
    active: "yes"
    title: "用户管理"
    icon: "123"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: ""
    remark: ""
  - name: "userList"
    order: 1
    path: "/list"
    component: "views/user/index"
    redirect: ""
    active: "yes"
    title: "用户列表"
    icon: "123"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "userGroup"
    remark: ""
  - name: "userDetail"
    order: 2
    path: "/detail"
    component: "views/user/detail"
    redirect: ""
    active: "yes"
    title: "用户详情"
    icon: "123"
    keep_alive: "yes"
    hidden: "yes"
    is_link: "no"
    parent: "userGroup"
    remark: "用户详情"
