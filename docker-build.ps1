# Fund Manager Docker 构建脚本
param(
    [string]$Tag = "fund-manager:latest",
    [switch]$Clean = $false,
    [switch]$ExtractOnly = $false
)

Write-Host "=== Fund Manager Docker 构建脚本 ===" -ForegroundColor Green
Write-Host "Docker 镜像标签: $Tag" -ForegroundColor Yellow

# 检查 Docker 是否可用
try {
    docker --version | Out-Null
    Write-Host "Docker 已就绪" -ForegroundColor Green
} catch {
    Write-Host "错误: Docker 未安装或不可用" -ForegroundColor Red
    exit 1
}

# 清理旧的构建产物（如果需要）
if ($Clean) {
    Write-Host "`n清理旧的 Docker 镜像..." -ForegroundColor Cyan
    docker rmi $Tag -f 2>$null
    docker system prune -f
}

# 构建 Docker 镜像
Write-Host "`n开始 Docker 构建..." -ForegroundColor Cyan
try {
    docker build -f Dockerfile.build -t $Tag .
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`nDocker 构建成功！" -ForegroundColor Green
        
        # 提取构建产物
        Write-Host "`n提取构建产物..." -ForegroundColor Cyan
        
        # 创建发布目录
        $releaseDir = "release"
        $dockerReleaseDir = Join-Path $releaseDir "docker-linux"
        
        if (Test-Path $dockerReleaseDir) {
            Remove-Item $dockerReleaseDir -Recurse -Force
        }
        New-Item -ItemType Directory -Path $dockerReleaseDir -Force | Out-Null
        
        # 创建临时容器并复制文件
        $containerId = docker create $Tag
        docker cp "${containerId}:/" $dockerReleaseDir
        docker rm $containerId | Out-Null
        
        Write-Host "构建产物已提取到: $dockerReleaseDir" -ForegroundColor Green
        
        # 显示文件信息
        $executablePath = Join-Path $dockerReleaseDir "fund_manager"
        if (Test-Path $executablePath) {
            $fileInfo = Get-Item $executablePath
            Write-Host "`n构建产物信息:" -ForegroundColor Cyan
            Write-Host "文件路径: $executablePath" -ForegroundColor White
            Write-Host "文件大小: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" -ForegroundColor White
            Write-Host "修改时间: $($fileInfo.LastWriteTime)" -ForegroundColor White
        }
        
        # 创建压缩包
        Write-Host "`n创建发布压缩包..." -ForegroundColor Cyan
        $zipPath = Join-Path $releaseDir "fund-manager-linux-x86_64.zip"
        if (Test-Path $zipPath) {
            Remove-Item $zipPath -Force
        }
        
        # 使用 PowerShell 压缩
        Compress-Archive -Path "$dockerReleaseDir\*" -DestinationPath $zipPath -Force
        Write-Host "发布包已创建: $zipPath" -ForegroundColor Green
        
        # 显示压缩包信息
        if (Test-Path $zipPath) {
            $zipInfo = Get-Item $zipPath
            Write-Host "压缩包大小: $([math]::Round($zipInfo.Length / 1MB, 2)) MB" -ForegroundColor White
        }
        
        if (-not $ExtractOnly) {
            Write-Host "`nDocker 镜像构建完成，标签: $Tag" -ForegroundColor Green
            Write-Host "可以使用以下命令运行:" -ForegroundColor Yellow
            Write-Host "docker run -p 6310:6310 -v `"`$(pwd)/config:/app/config`" $Tag" -ForegroundColor Gray
        }
        
    } else {
        Write-Host "`nDocker 构建失败！" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "`nDocker 构建过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n=== 构建完成 ===" -ForegroundColor Green
