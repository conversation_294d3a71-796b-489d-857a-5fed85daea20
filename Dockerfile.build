# Fund Manager 构建专用 Dockerfile
FROM rust:1.75-slim as builder

# 安装构建依赖
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制 Cargo 文件
COPY Cargo.toml Cargo.lock ./

# 复制源代码
COPY src ./src
COPY config ./config
COPY templates ./templates
COPY assets ./assets

# 构建应用
RUN cargo build --release

# 创建发布目录结构
RUN mkdir -p /release && \
    cp target/release/fund_manager /release/ && \
    cp -r config /release/ && \
    cp -r templates /release/ && \
    cp -r assets /release/

# 创建启动脚本
RUN cat > /release/start.sh << 'EOF' && \
#!/bin/bash
# Fund Manager 启动脚本

# 设置工作目录
cd $(dirname $0)

# 检查配置文件
if [ ! -f "config/config.yml" ]; then
    echo "错误: 未找到配置文件 config/config.yml"
    exit 1
fi

# 创建必要的目录
mkdir -p logs
mkdir -p tmp/upload
mkdir -p upload

# 启动应用
echo "启动 Fund Manager..."
./fund_manager
EOF
chmod +x /release/start.sh

# 创建 README
RUN cat > /release/README.txt << 'EOF'
# Fund Manager - Linux 版本

## 系统要求
- Linux x86_64
- glibc 2.17 或更高版本

## 安装说明
1. 解压文件到目标目录
2. 确保 fund_manager 有执行权限: chmod +x fund_manager
3. 修改 config/config.yml 配置文件
4. 运行: ./start.sh 或 ./fund_manager

## 目录结构
- fund_manager: 主程序
- config/: 配置文件目录
- templates/: HTML 模板文件
- assets/: 静态资源文件
- start.sh: 启动脚本

## 注意事项
- 首次运行前请检查配置文件
- 确保数据库连接配置正确
- 日志文件将保存在 logs/ 目录下
EOF

# 最终阶段 - 仅用于复制构建产物
FROM scratch as release
COPY --from=builder /release /
