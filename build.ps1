# Fund Manager 构建脚本
# 用于从 Windows 11 构建 Linux 版本

param(
    [string]$Target = "x86_64-unknown-linux-gnu",
    [string]$Profile = "release",
    [switch]$Clean = $false
)

Write-Host "=== Fund Manager 构建脚本 ===" -ForegroundColor Green
Write-Host "目标平台: $Target" -ForegroundColor Yellow
Write-Host "构建配置: $Profile" -ForegroundColor Yellow

# 检查 Rust 工具链
Write-Host "`n检查 Rust 工具链..." -ForegroundColor Cyan
rustc --version
cargo --version

# 检查目标是否已安装
Write-Host "`n检查构建目标..." -ForegroundColor Cyan
$installedTargets = rustup target list --installed
if ($installedTargets -notcontains $Target) {
    Write-Host "安装构建目标: $Target" -ForegroundColor Yellow
    rustup target add $Target
} else {
    Write-Host "构建目标 $Target 已安装" -ForegroundColor Green
}

# 清理构建产物（如果需要）
if ($Clean) {
    Write-Host "`n清理构建产物..." -ForegroundColor Cyan
    cargo clean
}

# 设置环境变量（针对 Linux 交叉编译）
if ($Target -like "*linux*") {
    Write-Host "`n设置 Linux 交叉编译环境..." -ForegroundColor Cyan
    
    # 检查是否安装了必要的链接器
    $linkerPath = Get-Command "x86_64-linux-gnu-gcc" -ErrorAction SilentlyContinue
    if (-not $linkerPath) {
        Write-Host "警告: 未找到 x86_64-linux-gnu-gcc 链接器" -ForegroundColor Yellow
        Write-Host "建议安装 mingw-w64 或使用 Docker 进行构建" -ForegroundColor Yellow
    }
}

# 执行构建
Write-Host "`n开始构建..." -ForegroundColor Cyan
$buildArgs = @("build", "--target", $Target)

if ($Profile -eq "release") {
    $buildArgs += "--release"
}

# 排除 adminPanel 目录（通过 Cargo.toml 配置）
Write-Host "构建命令: cargo $($buildArgs -join ' ')" -ForegroundColor Gray

try {
    & cargo @buildArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`n构建成功！" -ForegroundColor Green
        
        # 显示构建产物信息
        $targetDir = "target\$Target\$Profile"
        $executable = if ($Target -like "*windows*") { "fund_manager.exe" } else { "fund_manager" }
        $executablePath = Join-Path $targetDir $executable
        
        if (Test-Path $executablePath) {
            $fileInfo = Get-Item $executablePath
            Write-Host "`n构建产物信息:" -ForegroundColor Cyan
            Write-Host "文件路径: $executablePath" -ForegroundColor White
            Write-Host "文件大小: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" -ForegroundColor White
            Write-Host "修改时间: $($fileInfo.LastWriteTime)" -ForegroundColor White
        }
        
        # 创建发布目录
        $releaseDir = "release"
        if (-not (Test-Path $releaseDir)) {
            New-Item -ItemType Directory -Path $releaseDir | Out-Null
        }
        
        # 复制必要文件到发布目录
        Write-Host "`n准备发布文件..." -ForegroundColor Cyan
        $releaseTargetDir = Join-Path $releaseDir $Target
        if (Test-Path $releaseTargetDir) {
            Remove-Item $releaseTargetDir -Recurse -Force
        }
        New-Item -ItemType Directory -Path $releaseTargetDir | Out-Null
        
        # 复制可执行文件
        if (Test-Path $executablePath) {
            Copy-Item $executablePath $releaseTargetDir
            Write-Host "已复制: $executable" -ForegroundColor Green
        }
        
        # 复制配置文件
        if (Test-Path "config") {
            Copy-Item "config" $releaseTargetDir -Recurse
            Write-Host "已复制: config/" -ForegroundColor Green
        }
        
        # 复制模板文件
        if (Test-Path "templates") {
            Copy-Item "templates" $releaseTargetDir -Recurse
            Write-Host "已复制: templates/" -ForegroundColor Green
        }
        
        # 复制静态资源
        if (Test-Path "assets") {
            Copy-Item "assets" $releaseTargetDir -Recurse
            Write-Host "已复制: assets/" -ForegroundColor Green
        }
        
        # 创建启动脚本
        if ($Target -like "*linux*") {
            $startScript = @'
#!/bin/bash
# Fund Manager 启动脚本

# 设置工作目录
cd $(dirname $0)

# 检查配置文件
if [ ! -f "config/config.yml" ]; then
    echo "错误: 未找到配置文件 config/config.yml"
    exit 1
fi

# 创建必要的目录
mkdir -p logs
mkdir -p tmp/upload
mkdir -p upload

# 启动应用
echo "启动 Fund Manager..."
./fund_manager
'@
            $startScriptPath = Join-Path $releaseTargetDir "start.sh"
            $startScript | Out-File -FilePath $startScriptPath -Encoding UTF8
            Write-Host "已创建: start.sh" -ForegroundColor Green
        }

        # 创建 README
        $readme = @"
# Fund Manager - Linux 版本

## 系统要求
- Linux x86_64
- glibc 2.17 或更高版本

## 安装说明
1. 解压文件到目标目录
2. 确保 fund_manager 有执行权限: chmod +x fund_manager
3. 修改 config/config.yml 配置文件
4. 运行: ./start.sh 或 ./fund_manager

## 目录结构
- fund_manager: 主程序
- config/: 配置文件目录
- templates/: HTML 模板文件
- assets/: 静态资源文件
- start.sh: 启动脚本

## 注意事项
- 首次运行前请检查配置文件
- 确保数据库连接配置正确
- 日志文件将保存在 logs/ 目录下

构建时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
构建目标: $Target
"@
        $readmePath = Join-Path $releaseTargetDir "README.txt"
        $readme | Out-File -FilePath $readmePath -Encoding UTF8
        Write-Host "已创建: README.txt" -ForegroundColor Green
        
        Write-Host "`n发布文件已准备完成: $releaseTargetDir" -ForegroundColor Green
        
    } else {
        Write-Host "`n构建失败！退出代码: $LASTEXITCODE" -ForegroundColor Red
        exit $LASTEXITCODE
    }
} catch {
    Write-Host "`n构建过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n=== 构建完成 ===" -ForegroundColor Green
