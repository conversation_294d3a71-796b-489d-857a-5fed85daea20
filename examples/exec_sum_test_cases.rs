// exec_sum() 函数测试用例
// 用于验证函数的各种功能和边界情况

use crate::{
    db::{Database, WhereOptions},
    services::sales_order::SalesOrderService,
};

/// 测试字段类型验证功能
pub async fn test_field_type_validation() {
    println!("=== 测试字段类型验证 ===\n");

    // 测试有效的数值字段
    let valid_fields = vec![
        ("amount", "商品金额"),
        ("total_payment", "总支付金额"),
        ("express_fee", "运费"),
        ("platform_fee_total", "平台费用总额"),
        ("sales_price", "销售价格"),
        ("cost_price", "成本价格"),
        ("quantity", "数量"),
        ("created_at", "创建时间戳"),
        ("updated_at", "更新时间戳"),
    ];

    println!("✅ 测试有效字段:");
    for (field, description) in valid_fields {
        let params = vec![];
        match Database::exec_sum("sales_order", params, field).await {
            Ok(_) => println!("  ✓ {} ({}): 字段类型验证通过", field, description),
            Err(e) => println!("  ✗ {} ({}): 验证失败 - {}", field, description, e),
        }
    }

    // 测试无效的非数值字段
    let invalid_fields = vec![
        ("status", "订单状态"),
        ("customer", "客户姓名"),
        ("address", "地址"),
        ("platform_name", "平台名称"),
        ("serial", "订单编号"),
        ("pay_type", "支付方式"),
    ];

    println!("\n❌ 测试无效字段:");
    for (field, description) in invalid_fields {
        let params = vec![];
        match Database::exec_sum("sales_order", params, field).await {
            Ok(result) => println!("  ⚠ {} ({}): 意外通过 - {}", field, description, result),
            Err(_) => println!("  ✓ {} ({}): 正确拒绝非数值字段", field, description),
        }
    }
}

/// 测试查询条件功能
pub async fn test_query_conditions() {
    println!("\n=== 测试查询条件功能 ===\n");

    // 测试无条件查询
    println!("1. 无条件查询 - 统计所有订单总金额:");
    let params = vec![];
    match SalesOrderService::sum_field(params, "total_payment").await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }

    // 测试单条件查询
    println!("\n2. 单条件查询 - 统计已完成订单金额:");
    let params = vec![WhereOptions::new("status".to_string(), "completed".to_string())];
    match SalesOrderService::sum_field(params, "amount").await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }

    // 测试多条件查询
    println!("\n3. 多条件查询 - 统计特定状态和平台的订单:");
    let params = vec![
        WhereOptions::new("status".to_string(), "completed".to_string()),
        WhereOptions::new("platform_name".to_string(), "淘宝".to_string()),
    ];
    match SalesOrderService::sum_field(params, "total_payment").await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }

    // 测试时间范围查询
    println!("\n4. 时间范围查询 - 统计特定时间段订单:");
    let params = vec![
        WhereOptions::new("begin_date".to_string(), "2024-01-01".to_string()),
        WhereOptions::new("end_date".to_string(), "2024-12-31".to_string()),
    ];
    match SalesOrderService::sum_field(params, "amount").await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }

    // 测试ID列表查询
    println!("\n5. ID列表查询 - 统计指定订单ID的金额:");
    let params = vec![
        WhereOptions::new("ids".to_string(), "sales_order:1,sales_order:2".to_string()),
    ];
    match SalesOrderService::sum_field(params, "total_payment").await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }
}

/// 测试不同表的求和操作
pub async fn test_different_tables() {
    println!("\n=== 测试不同表的求和操作 ===\n");

    // 测试销售订单表
    println!("1. 销售订单表 (sales_order):");
    let params = vec![];
    match Database::exec_sum("sales_order", params, "total_payment").await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }

    // 测试销售订单信息表
    println!("\n2. 销售订单信息表 (sales_order_info):");
    let params = vec![];
    match Database::exec_sum("sales_order_info", params, "total_sales_price").await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }

    // 测试采购订单表
    println!("\n3. 采购订单表 (purchase_order):");
    let params = vec![];
    match Database::exec_sum("purchase_order", params, "total_payment").await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }
}

/// 测试边界情况和错误处理
pub async fn test_edge_cases() {
    println!("\n=== 测试边界情况和错误处理 ===\n");

    // 测试不存在的字段
    println!("1. 不存在的字段:");
    let params = vec![];
    match Database::exec_sum("sales_order", params, "non_existent_amount").await {
        Ok(result) => println!("   ⚠ 意外成功: {}", result),
        Err(e) => println!("   ✓ 正确处理: {}", e),
    }

    // 测试不存在的表
    println!("\n2. 不存在的表:");
    let params = vec![];
    match Database::exec_sum("non_existent_table", params, "amount").await {
        Ok(result) => println!("   ⚠ 意外成功: {}", result),
        Err(e) => println!("   ✓ 正确处理: {}", e),
    }

    // 测试空条件匹配
    println!("\n3. 无匹配数据的条件:");
    let params = vec![WhereOptions::new("status".to_string(), "impossible_status".to_string())];
    match Database::exec_sum("sales_order", params, "total_payment").await {
        Ok(result) => println!("   ✓ 正确处理空结果: {}", result),
        Err(e) => println!("   ✗ 处理失败: {}", e),
    }

    // 测试特殊字符
    println!("\n4. 包含特殊字符的条件:");
    let params = vec![WhereOptions::new("customer".to_string(), "张三's订单".to_string())];
    match Database::exec_sum("sales_order", params, "amount").await {
        Ok(result) => println!("   ✓ 正确处理特殊字符: {}", result),
        Err(e) => println!("   ✗ 处理失败: {}", e),
    }
}

/// 测试字段名模式匹配
pub async fn test_field_pattern_matching() {
    println!("\n=== 测试字段名模式匹配 ===\n");

    // 测试以关键词结尾的字段
    let pattern_fields = vec![
        ("custom_amount", "自定义金额字段"),
        ("shipping_fee", "运输费用字段"),
        ("service_price", "服务价格字段"),
        ("order_total", "订单总额字段"),
        ("item_count", "商品数量字段"),
        ("product_quantity", "产品数量字段"),
    ];

    println!("测试模式匹配字段:");
    for (field, description) in pattern_fields {
        let params = vec![];
        match Database::exec_sum("sales_order", params, field).await {
            Ok(_) => println!("  ✓ {} ({}): 模式匹配成功", field, description),
            Err(e) => {
                if e.to_string().contains("不是可汇总的数值类型字段") {
                    println!("  ✗ {} ({}): 模式匹配失败", field, description);
                } else {
                    println!("  ⚠ {} ({}): 其他错误 - {}", field, description, e);
                }
            }
        }
    }
}

/// 性能测试
pub async fn test_performance() {
    println!("\n=== 性能测试 ===\n");

    let test_cases = vec![
        ("无条件求和", vec![]),
        ("单条件求和", vec![WhereOptions::new("status".to_string(), "completed".to_string())]),
        ("多条件求和", vec![
            WhereOptions::new("status".to_string(), "completed".to_string()),
            WhereOptions::new("platform_name".to_string(), "淘宝".to_string()),
        ]),
    ];

    for (test_name, params) in test_cases {
        println!("测试: {}", test_name);
        let start = std::time::Instant::now();
        
        match Database::exec_sum("sales_order", params, "total_payment").await {
            Ok(result) => {
                let duration = start.elapsed();
                println!("  ✓ 执行时间: {:?}", duration);
                println!("  ✓ 结果: {}", result);
            }
            Err(e) => {
                let duration = start.elapsed();
                println!("  ✗ 执行时间: {:?}", duration);
                println!("  ✗ 错误: {}", e);
            }
        }
        println!();
    }
}

/// 运行所有测试用例
pub async fn run_all_tests() {
    println!("🧪 exec_sum() 函数测试套件\n");
    println!("=" .repeat(50));
    
    // 运行各种测试
    test_field_type_validation().await;
    test_query_conditions().await;
    test_different_tables().await;
    test_edge_cases().await;
    test_field_pattern_matching().await;
    test_performance().await;
    
    println!("=" .repeat(50));
    println!("✅ 所有测试用例执行完成！");
}

/// 快速验证测试
pub async fn quick_validation_test() {
    println!("🚀 快速验证测试\n");
    
    // 基本功能测试
    println!("1. 基本求和功能:");
    let params = vec![];
    match SalesOrderService::sum_field(params, "total_payment").await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }
    
    // 字段验证测试
    println!("\n2. 字段类型验证:");
    let params = vec![];
    match SalesOrderService::sum_field(params, "customer").await {
        Ok(result) => println!("   ⚠ 意外通过: {}", result),
        Err(_) => println!("   ✓ 正确拒绝非数值字段"),
    }
    
    println!("\n✅ 快速验证完成！");
}
