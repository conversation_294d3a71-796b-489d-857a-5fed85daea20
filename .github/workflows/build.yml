name: build

on:
  push:
    branches:
      - main
    tags:
      - v*
  pull_request:
    branches:
      - main

permissions:
  contents: write

jobs:
  build-rust:
    strategy:
      matrix:
        platform: [ubuntu-latest]
    runs-on: ${{ matrix.platform }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: recursive
      - name: Install Rust
        run: rustup toolchain install stable --component llvm-tools-preview
      - name: Install cargo-llvm-cov
        uses: taiki-e/install-action@cargo-llvm-cov
      - name: install nextest
        uses: taiki-e/install-action@nextest
      - uses: Swatinem/rust-cache@v2
      - name: Check code format
        run: cargo fmt -- --check
      - name: Check the package for errors
        run: cargo check --all
      - name: Lint rust sources
        run: cargo clippy --all-targets --all-features --tests --benches -- -D warnings
      - name: Execute rust tests
        run: cargo nextest run --all-features
      - name: Generate a changelog
        uses: orhun/git-cliff-action@v2
        id: git-cliff
        if: startsWith(github.ref, 'refs/tags/')
        with:
          config: cliff.toml
          args: -vv --latest --strip header
        env:
          OUTPUT: CHANGES.md
      - name: Release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          body: ${{ steps.git-cliff.outputs.content }}
