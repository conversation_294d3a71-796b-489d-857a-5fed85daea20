#!/bin/bash
# Fund Manager 构建脚本 - Linux 版本

set -e

TARGET=${1:-"x86_64-unknown-linux-gnu"}
PROFILE=${2:-"release"}
CLEAN=${3:-"false"}

echo "=== Fund Manager 构建脚本 ==="
echo "目标平台: $TARGET"
echo "构建配置: $PROFILE"

# 检查 Rust 工具链
echo -e "\n检查 Rust 工具链..."
rustc --version
cargo --version

# 检查目标是否已安装
echo -e "\n检查构建目标..."
if ! rustup target list --installed | grep -q "$TARGET"; then
    echo "安装构建目标: $TARGET"
    rustup target add "$TARGET"
else
    echo "构建目标 $TARGET 已安装"
fi

# 清理构建产物（如果需要）
if [ "$CLEAN" = "true" ]; then
    echo -e "\n清理构建产物..."
    cargo clean
fi

# 执行构建
echo -e "\n开始构建..."
BUILD_ARGS="build --target $TARGET"

if [ "$PROFILE" = "release" ]; then
    BUILD_ARGS="$BUILD_ARGS --release"
fi

echo "构建命令: cargo $BUILD_ARGS"

if cargo $BUILD_ARGS; then
    echo -e "\n构建成功！"
    
    # 显示构建产物信息
    TARGET_DIR="target/$TARGET/$PROFILE"
    EXECUTABLE="fund_manager"
    EXECUTABLE_PATH="$TARGET_DIR/$EXECUTABLE"
    
    if [ -f "$EXECUTABLE_PATH" ]; then
        echo -e "\n构建产物信息:"
        echo "文件路径: $EXECUTABLE_PATH"
        echo "文件大小: $(du -h "$EXECUTABLE_PATH" | cut -f1)"
        echo "修改时间: $(stat -c %y "$EXECUTABLE_PATH")"
    fi
    
    # 创建发布目录
    RELEASE_DIR="release"
    mkdir -p "$RELEASE_DIR"
    
    # 准备发布文件
    echo -e "\n准备发布文件..."
    RELEASE_TARGET_DIR="$RELEASE_DIR/$TARGET"
    rm -rf "$RELEASE_TARGET_DIR"
    mkdir -p "$RELEASE_TARGET_DIR"
    
    # 复制可执行文件
    if [ -f "$EXECUTABLE_PATH" ]; then
        cp "$EXECUTABLE_PATH" "$RELEASE_TARGET_DIR/"
        chmod +x "$RELEASE_TARGET_DIR/$EXECUTABLE"
        echo "已复制: $EXECUTABLE"
    fi
    
    # 复制配置文件
    if [ -d "config" ]; then
        cp -r "config" "$RELEASE_TARGET_DIR/"
        echo "已复制: config/"
    fi
    
    # 复制模板文件
    if [ -d "templates" ]; then
        cp -r "templates" "$RELEASE_TARGET_DIR/"
        echo "已复制: templates/"
    fi
    
    # 复制静态资源
    if [ -d "assets" ]; then
        cp -r "assets" "$RELEASE_TARGET_DIR/"
        echo "已复制: assets/"
    fi
    
    # 创建启动脚本
    cat > "$RELEASE_TARGET_DIR/start.sh" << 'EOF'
#!/bin/bash
# Fund Manager 启动脚本

# 设置工作目录
cd $(dirname $0)

# 检查配置文件
if [ ! -f "config/config.yml" ]; then
    echo "错误: 未找到配置文件 config/config.yml"
    exit 1
fi

# 创建必要的目录
mkdir -p logs
mkdir -p tmp/upload
mkdir -p upload

# 启动应用
echo "启动 Fund Manager..."
./fund_manager
EOF
    chmod +x "$RELEASE_TARGET_DIR/start.sh"
    echo "已创建: start.sh"
    
    # 创建 README
    cat > "$RELEASE_TARGET_DIR/README.txt" << EOF
# Fund Manager - Linux 版本

## 系统要求
- Linux x86_64
- glibc 2.17 或更高版本

## 安装说明
1. 解压文件到目标目录
2. 确保 fund_manager 有执行权限: chmod +x fund_manager
3. 修改 config/config.yml 配置文件
4. 运行: ./start.sh 或 ./fund_manager

## 目录结构
- fund_manager: 主程序
- config/: 配置文件目录
- templates/: HTML 模板文件
- assets/: 静态资源文件
- start.sh: 启动脚本

## 注意事项
- 首次运行前请检查配置文件
- 确保数据库连接配置正确
- 日志文件将保存在 logs/ 目录下

构建时间: $(date '+%Y-%m-%d %H:%M:%S')
构建目标: $TARGET
EOF
    echo "已创建: README.txt"
    
    echo -e "\n发布文件已准备完成: $RELEASE_TARGET_DIR"
    
else
    echo -e "\n构建失败！"
    exit 1
fi

echo -e "\n=== 构建完成 ==="
