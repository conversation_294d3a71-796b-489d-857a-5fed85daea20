# Fund Manager 部署指南

## 概述

Fund Manager 是一个基于 Rust 开发的资金管理系统，支持 Windows 和 Linux 平台。

## 构建说明

### Windows 构建

在 Windows 11 环境中已完成构建：

1. **已构建的版本**：
   - 位置：`release/fund-manager-windows-x86_64.zip`
   - 大小：约 13.9 MB
   - 可执行文件：`fund_manager.exe` (约 40 MB)

2. **包含内容**：
   - 主程序：`fund_manager.exe`
   - 配置文件：`config/` 目录
   - 模板文件：`templates/` 目录
   - 静态资源：`assets/` 目录
   - 启动脚本：`start.bat`
   - 说明文档：`README.txt`

### Linux 构建

由于交叉编译的复杂性，建议在 Linux 环境中进行构建：

1. **构建脚本**：`build-linux.sh`
2. **系统要求**：
   - Linux x86_64
   - Rust 工具链
   - OpenSSL 开发库
   - pkg-config

3. **构建步骤**：
   ```bash
   # 在 Linux 环境中执行
   chmod +x build-linux.sh
   ./build-linux.sh
   ```

## 部署方案

### 方案一：直接部署（推荐用于开发/测试）

#### Windows 部署
1. 解压 `fund-manager-windows-x86_64.zip`
2. 修改 `config/config.yml` 配置文件
3. 双击 `start.bat` 启动

#### Linux 部署
1. 在 Linux 环境中构建或获取构建产物
2. 解压 `fund-manager-linux-x86_64.tar.gz`
3. 修改 `config/config.yml` 配置文件
4. 运行 `./start.sh` 启动

### 方案二：系统服务部署（推荐用于生产环境）

#### Linux 系统服务
1. 使用构建脚本生成的安装包
2. 以 root 权限运行：`sudo ./install.sh`
3. 配置文件位于：`/opt/fund-manager/config/config.yml`
4. 服务管理：
   ```bash
   sudo systemctl start fund-manager
   sudo systemctl enable fund-manager
   sudo systemctl status fund-manager
   ```

#### Windows 服务
可以使用 NSSM (Non-Sucking Service Manager) 将程序注册为 Windows 服务：

1. 下载 NSSM：https://nssm.cc/
2. 安装服务：
   ```cmd
   nssm install FundManager "C:\path\to\fund_manager.exe"
   nssm set FundManager AppDirectory "C:\path\to\fund-manager"
   nssm start FundManager
   ```

### 方案三：Docker 部署

#### 使用提供的 Dockerfile
```bash
# 构建镜像
docker build -t fund-manager:latest .

# 运行容器
docker run -d \
  --name fund-manager \
  -p 6310:6310 \
  -v ./config:/app/config \
  -v ./logs:/app/logs \
  -v ./upload:/app/upload \
  fund-manager:latest
```

#### 使用 Docker Compose
```bash
# 启动完整环境（包括 Redis）
docker-compose up -d

# 仅启动应用（需要外部 Redis）
docker-compose up fund-manager
```

## 配置说明

### 核心配置文件：config/config.yml

```yaml
# 服务器配置
server:
  host: "0.0.0.0"
  port: 6310
  
# 数据库配置
database:
  host: "localhost"
  port: 3306
  username: "fund_manager"
  password: "your_password"
  database: "fund_manager"
  
# Redis 配置
redis:
  host: "localhost"
  port: 6379
  password: ""
  database: 0
  
# 日志配置
logging:
  level: "info"
  file: "logs/app.log"
  
# 文件上传配置
upload:
  path: "upload"
  max_size: 10485760  # 10MB
```

### 菜单配置：config/menus.yml
- 定义系统菜单结构
- 支持层级菜单
- 控制菜单显示和权限

### 权限配置：config/permissions.yml
- 定义系统权限列表
- 控制功能访问权限
- 支持角色权限管理

## 环境要求

### 系统依赖
- **数据库**：MySQL 5.7+ 或 MariaDB 10.3+
- **缓存**：Redis 5.0+
- **操作系统**：
  - Windows 10+ (x86_64)
  - Linux (x86_64, glibc 2.17+)

### 网络要求
- 默认端口：6310
- 数据库连接端口：3306
- Redis 连接端口：6379

## 安全建议

1. **配置文件安全**：
   - 设置适当的文件权限
   - 不要在版本控制中包含敏感配置

2. **网络安全**：
   - 使用防火墙限制访问
   - 配置 SSL/TLS 证书
   - 使用反向代理（如 Nginx）

3. **数据库安全**：
   - 使用专用数据库用户
   - 限制数据库访问权限
   - 定期备份数据

## 监控和维护

### 日志管理
- 应用日志：`logs/` 目录
- 系统服务日志：`journalctl -u fund-manager`
- 日志轮转：建议配置 logrotate

### 性能监控
- 监控 CPU 和内存使用
- 监控数据库连接数
- 监控 Redis 内存使用

### 备份策略
- 数据库定期备份
- 配置文件备份
- 上传文件备份

## 故障排除

### 常见问题

1. **启动失败**：
   - 检查配置文件语法
   - 验证数据库连接
   - 确认端口未被占用

2. **数据库连接失败**：
   - 检查数据库服务状态
   - 验证连接参数
   - 检查网络连通性

3. **Redis 连接失败**：
   - 检查 Redis 服务状态
   - 验证连接参数
   - 检查防火墙设置

### 调试模式
设置环境变量启用详细日志：
```bash
export RUST_LOG=debug
./fund_manager
```

## 版本信息

- **当前版本**：v1.0.0
- **构建时间**：2025-01-10
- **Rust 版本**：1.75+
- **支持平台**：Windows x86_64, Linux x86_64

## 联系支持

如遇到部署问题，请提供以下信息：
- 操作系统版本
- 错误日志
- 配置文件（去除敏感信息）
- 部署环境描述
