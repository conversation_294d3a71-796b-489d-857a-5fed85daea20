# Fund Manager - Linux 版本

## 系统要求
- Linux x86_64
- glibc 2.17 或更高版本

## 安装说明
1. 解压文件到目标目录
2. 确保 fund_manager 有执行权限: chmod +x fund_manager
3. 修改 config/config.yml 配置文件
4. 运行: ./start.sh 或 ./fund_manager

## 目录结构
- fund_manager: 主程序
- config/: 配置文件目录
- templates/: HTML 模板文件
- assets/: 静态资源文件
- start.sh: 启动脚本

## 注意事项
- 首次运行前请检查配置文件
- 确保数据库连接配置正确
- 日志文件将保存在 logs/ 目录下

构建时间: 2025-07-12 17:55:09
构建目标: x86_64-unknown-linux-gnu
