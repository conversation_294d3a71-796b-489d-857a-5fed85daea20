server:
  name: "fund_manager"
  address: "0.0.0.0:6310"
  ssl: false
  cors_allow_origin:
    - "http://localhost:8080" # 允许 localhost 的所有端口
    - "http://127.0.0.1:8080" # 允许 127.0.0.1 的所有端口
    - "http://localhost:8090" # 允许 localhost 的所有端口
    - "http://127.0.0.1:8090" # 允许 127.0.0.1 的所有端口
    - "http://127.0.0.1" # 允许无端口的访问
    - "http://localhost" # 允许无端口的访问

database:
  # url: "127.0.0.1"
  # url: "************"
  url: "************"
  port: "6789"
  # url: "************"
  # port: "8099"
  username: "root"
  password: "123456aB"
  db_ns: "fund_manager"
  db_name: "fund_manager"

jwt:
  jwt_secret: "04426d139@ef743a3a20b43377b4ae2e.3d2ffe2fc00109_4e44a7c64c877a8d"
  jwt_exp: 604800 # 60 * 60 * 24 * 7
  jwt_buf: 43200 # 60 * 60 * 12

cert:
  cert: "config/certs/cert.pem"
  key: "config/certs/key.pem"

log:
  filter_level: "info" # Available options: "debug", "info", "warn", "error"
  with_ansi: true
  to_stdout: true
  directory: "./logs"
  file_name: "my-service.log"
  rolling: "daily" # Available options: "minutely", "hourly", "daily", "never"

redis:
  # server: "************"
  server: "127.0.0.1"
  port: 6309

image:
  image_cut: false
  image_width: 1000
  image_height: 800

captcha:
  image_height: 80
  image_width: 240
  text_length: 4

oss:
  oss_type: "local"
  # oss_type: "aliyun"
  endpoint: "cn-chengdu"
  secret_id: "LTAI5tHZmyQMgdVcXmsh5vG3"
  secret_key: "******************************"
  region: ""
  bucket_name: "ipt-code"
  thumb_ext: "/thumb_360"
