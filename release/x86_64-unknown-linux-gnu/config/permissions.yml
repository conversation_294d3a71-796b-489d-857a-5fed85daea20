# 系统权限配置
permissions:
  - name: "附件列表"
    method: "POST"
    path: "/api/attachment/list"
    group: "attachment"
  - name: "附件详情"
    method: "GET"
    path: "/api/attachment"
    group: "attachment"
  - name: "附件创建"
    method: "POST"
    path: "/api/attachment"
    group: "attachment"
  - name: "附件更新"
    method: "PUT"
    path: "/api/attachment"
    group: "attachment"
  - name: "附件删除"
    method: "DELETE"
    path: "/api/attachment"
    group: "attachment"
  - name: "产品目录列表"
    method: "POST"
    path: "/api/category/list"
    group: "category"
  - name: "产品目录详情"
    method: "GET"
    path: "/api/category"
    group: "category"
  - name: "产品目录创建"
    method: "POST"
    path: "/api/category"
    group: "category"
  - name: "产品目录更新"
    method: "PUT"
    path: "/api/category"
    group: "category"
  - name: "产品目录删除"
    method: "DELETE"
    path: "/api/category"
    group: "category"
  - name: "系统字典列表"
    method: "POST"
    path: "/api/config_dict/list"
    group: "config_dict"
  - name: "系统字典详情"
    method: "GET"
    path: "/api/config_dict"
    group: "config_dict"
  - name: "系统字典创建"
    method: "POST"
    path: "/api/config_dict"
    group: "config_dict"
  - name: "系统字典更新"
    method: "PUT"
    path: "/api/config_dict"
    group: "config_dict"
  - name: "系统字典删除"
    method: "DELETE"
    path: "/api/config_dict"
    group: "config_dict"
  - name: "合同列表"
    method: "POST"
    path: "/api/financial_contract/list"
    group: "financial_contract"
  - name: "合同详情"
    method: "GET"
    path: "/api/financial_contract"
    group: "financial_contract"
  - name: "合同创建"
    method: "POST"
    path: "/api/financial_contract"
    group: "financial_contract"
  - name: "合同更新"
    method: "PUT"
    path: "/api/financial_contract"
    group: "financial_contract"
  - name: "合同删除"
    method: "DELETE"
    path: "/api/financial_contract"
    group: "financial_contract"
  - name: "企业列表"
    method: "POST"
    path: "/api/company/list"
    group: "company"
  - name: "企业详情"
    method: "GET"
    path: "/api/company"
    group: "company"
  - name: "企业创建"
    method: "POST"
    path: "/api/company"
    group: "company"
  - name: "企业更新"
    method: "PUT"
    path: "/api/company"
    group: "company"
  - name: "企业删除"
    method: "DELETE"
    path: "/api/company"
    group: "company"
  - name: "产品属性列表"
    method: "POST"
    path: "/api/product_attribute/list"
    group: "product_attribute"
  - name: "产品属性详情"
    method: "GET"
    path: "/api/product_attribute"
    group: "product_attribute"
  - name: "产品属性创建"
    method: "POST"
    path: "/api/product_attribute"
    group: "product_attribute"
  - name: "产品属性更新"
    method: "PUT"
    path: "/api/product_attribute"
    group: "product_attribute"
  - name: "产品属性删除"
    method: "DELETE"
    path: "/api/product_attribute"
    group: "product_attribute"
  - name: "产品SKU列表"
    method: "POST"
    path: "/api/product_sku/list"
    group: "product_sku"
  - name: "产品SKU详情"
    method: "GET"
    path: "/api/product_sku"
    group: "product_sku"
  - name: "产品SKU创建"
    method: "POST"
    path: "/api/product_sku"
    group: "product_sku"
  - name: "产品SKU更新"
    method: "PUT"
    path: "/api/product_sku"
    group: "product_sku"
  - name: "产品SKU删除"
    method: "DELETE"
    path: "/api/product_sku"
    group: "product_sku"
  - name: "采购订单列表"
    method: "POST"
    path: "/api/purchase_order/list"
    group: "purchase_order"
  - name: "采购订单详情"
    method: "GET"
    path: "/api/purchase_order"
    group: "purchase_order"
  - name: "采购订单创建"
    method: "POST"
    path: "/api/purchase_order"
    group: "purchase_order"
  - name: "采购订单更新"
    method: "PUT"
    path: "/api/purchase_order"
    group: "purchase_order"
  - name: "采购订单删除"
    method: "DELETE"
    path: "/api/purchase_order"
    group: "purchase_order"
  - name: "采购订单明细列表"
    method: "POST"
    path: "/api/purchase_order_info/list"
    group: "purchase_order_info"
  - name: "采购订单明细详情"
    method: "GET"
    path: "/api/purchase_order_info"
    group: "purchase_order_info"
  - name: "采购订单明细创建"
    method: "POST"
    path: "/api/purchase_order_info"
    group: "purchase_order_info"
  - name: "采购订单明细更新"
    method: "PUT"
    path: "/api/purchase_order_info"
    group: "purchase_order_info"
  - name: "采购订单明细删除"
    method: "DELETE"
    path: "/api/purchase_order_info"
    group: "purchase_order_info"
  - name: "销售订单列表"
    method: "POST"
    path: "/api/sales_order/list"
    group: "sales_order"
  - name: "销售订单详情"
    method: "GET"
    path: "/api/sales_order"
    group: "sales_order"
  - name: "销售订单创建"
    method: "POST"
    path: "/api/sales_order"
    group: "sales_order"
  - name: "销售订单更新"
    method: "PUT"
    path: "/api/sales_order"
    group: "sales_order"
  - name: "销售订单删除"
    method: "DELETE"
    path: "/api/sales_order"
    group: "sales_order"
  - name: "销售订单明细列表"
    method: "POST"
    path: "/api/sales_order_info/list"
    group: "sales_order_info"
  - name: "销售订单明细详情"
    method: "GET"
    path: "/api/sales_order_info"
    group: "sales_order_info"
  - name: "销售订单明细创建"
    method: "POST"
    path: "/api/sales_order_info"
    group: "sales_order_info"
  - name: "销售订单明细更新"
    method: "PUT"
    path: "/api/sales_order_info"
    group: "sales_order_info"
  - name: "销售订单明细删除"
    method: "DELETE"
    path: "/api/sales_order_info"
    group: "sales_order_info"
  - name: "合同额度列表"
    method: "POST"
    path: "/api/quota_contract/list"
    group: "quota_contract"
  - name: "合同额度详情"
    method: "GET"
    path: "/api/quota_contract"
    group: "quota_contract"
  - name: "合同额度创建"
    method: "POST"
    path: "/api/quota_contract"
    group: "quota_contract"
  - name: "合同额度更新"
    method: "PUT"
    path: "/api/quota_contract"
    group: "quota_contract"
  - name: "合同额度删除"
    method: "DELETE"
    path: "/api/quota_contract"
    group: "quota_contract"
  - name: "库存列表"
    method: "POST"
    path: "/api/stock/list"
    group: "stock"
  - name: "库存详情"
    method: "GET"
    path: "/api/stock"
    group: "stock"
  - name: "库存创建"
    method: "POST"
    path: "/api/stock"
    group: "stock"
  - name: "库存更新"
    method: "PUT"
    path: "/api/stock"
    group: "stock"
  - name: "库存删除"
    method: "DELETE"
    path: "/api/stock"
    group: "stock"
  - name: "供应商列表"
    method: "POST"
    path: "/api/supplier/list"
    group: "supplier"
  - name: "供应商详情"
    method: "GET"
    path: "/api/supplier"
    group: "supplier"
  - name: "供应商创建"
    method: "POST"
    path: "/api/supplier"
    group: "supplier"
  - name: "供应商更新"
    method: "PUT"
    path: "/api/supplier"
    group: "supplier"
  - name: "供应商删除"
    method: "DELETE"
    path: "/api/supplier"
    group: "supplier"
  - name: "仓库列表"
    method: "POST"
    path: "/api/warehouse/list"
    group: "warehouse"
  - name: "仓库详情"
    method: "GET"
    path: "/api/warehouse"
    group: "warehouse"
  - name: "仓库创建"
    method: "POST"
    path: "/api/warehouse"
    group: "warehouse"
  - name: "仓库更新"
    method: "PUT"
    path: "/api/warehouse"
    group: "warehouse"
  - name: "仓库删除"
    method: "DELETE"
    path: "/api/warehouse"
    group: "warehouse"
  - name: "仓库位置列表"
    method: "POST"
    path: "/api/warehouse_position/list"
    group: "warehouse_position"
  - name: "仓库位置详情"
    method: "GET"
    path: "/api/warehouse_position"
    group: "warehouse_position"
  - name: "仓库位置创建"
    method: "POST"
    path: "/api/warehouse_position"
    group: "warehouse_position"
  - name: "仓库位置更新"
    method: "PUT"
    path: "/api/warehouse_position"
    group: "warehouse_position"
  - name: "仓库位置删除"
    method: "DELETE"
    path: "/api/warehouse_position"
    group: "warehouse_position"
  - name: "上传单文件"
    method: "POST"
    path: "/api/upload"
    group: "upload"
  - name: "上传多文件"
    method: "POST"
    path: "/api/upload/multi"
    group: "upload"
  - name: "上传临时文件"
    method: "POST"
    path: "/api/upload/temp"
    group: "upload"
  - name: "更新临时文件"
    method: "PUT"
    path: "/api/upload"
    group: "upload"
  - name: "获取用户列表"
    method: "POST"
    path: "/api/user/list"
    group: "user"
  - name: "获取用户详情"
    method: "GET"
    path: "/api/user"
    group: "user"
  - name: "添加用户信息"
    method: "POST"
    path: "/api/user"
    group: "user"
  - name: "修改用户信息"
    method: "PUT"
    path: "/api/user"
    group: "user"
  - name: "删除用户信息"
    method: "DELETE"
    path: "/api/user"
    group: "user"
  - name: "获取当前用户"
    method: "GET"
    path: "/api/current/user"
    group: "current"
  - name: "更新当前用户"
    method: "PUT"
    path: "/api/current/user"
    group: "current"
  - name: "获取系统菜单"
    method: "POST"
    path: "/api/menu/list"
    group: "menu"
  - name: "获取菜单详情"
    method: "GET"
    path: "/api/menu"
    group: "menu"
  - name: "添加系统菜单"
    method: "POST"
    path: "/api/menu"
    group: "menu"
  - name: "修改系统菜单"
    method: "PUT"
    path: "/api/menu"
    group: "menu"
  - name: "删除系统菜单"
    method: "DELETE"
    path: "/api/menu"
    group: "menu"
  - name: "获取当前用户菜单"
    method: "GET"
    path: "/api/current/menu"
    group: "current"
  - name: "获取角色列表"
    method: "POST"
    path: "/api/role/list"
    group: "role"
  - name: "获取角色详情"
    method: "GET"
    path: "/api/role"
    group: "role"
  - name: "添加角色信息"
    method: "POST"
    path: "/api/role"
    group: "role"
  - name: "修改角色信息"
    method: "PUT"
    path: "/api/role"
    group: "role"
  - name: "删除角色信息"
    method: "DELETE"
    path: "/api/role"
    group: "role"
  - name: "获取权限列表"
    method: "POST"
    path: "/api/permission/list"
    group: "permission"
  - name: "获取权限详情"
    method: "GET"
    path: "/api/permission"
    group: "permission"
  - name: "添加权限信息"
    method: "POST"
    path: "/api/permission"
    group: "permission"
  - name: "修改权限信息"
    method: "PUT"
    path: "/api/permission"
    group: "permission"
  - name: "删除权限信息"
    method: "DELETE"
    path: "/api/permission"
    group: "permission"
