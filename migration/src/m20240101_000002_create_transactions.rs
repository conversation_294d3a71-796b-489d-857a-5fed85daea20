use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .create_table(
                Table::create()
                    .table(Transactions::Table)
                    .if_not_exists()
                    .col(
                        ColumnDef::new(Transactions::Id)
                            .uuid()
                            .not_null()
                            .primary_key(),
                    )
                    .col(ColumnDef::new(Transactions::Filename).string().not_null())
                    .col(ColumnDef::new(Transactions::RowNumber).integer().not_null())
                    .col(
                        ColumnDef::new(Transactions::RequestTime)
                            .timestamp_with_time_zone()
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::AccountingAmount)
                            .decimal_len(20, 2)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::Balance)
                            .decimal_len(20, 2)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::AccountingCurrency)
                            .string_len(3)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::AccountingType)
                            .string_len(32)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::AccountSubject)
                            .string_len(64)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::TxnCreateTime)
                            .timestamp_with_time_zone()
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::TxnCompleteTime)
                            .timestamp_with_time_zone()
                            .null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::MerchantTxnId)
                            .string_len(64)
                            .null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::TradeOrderId)
                            .string_len(64)
                            .null(),
                    )
                    .col(ColumnDef::new(Transactions::Country).string_len(2).null())
                    .col(
                        ColumnDef::new(Transactions::TxnAmount)
                            .decimal_len(20, 2)
                            .null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::TxnCurrency)
                            .string_len(3)
                            .null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::PayeeTxnFee)
                            .decimal_len(20, 2)
                            .null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::PayeeTxnFeeCurrency)
                            .string_len(3)
                            .null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::PayerTxnFee)
                            .decimal_len(20, 2)
                            .null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::PayerTxnFeeCurrency)
                            .string_len(3)
                            .null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::PayeeTax)
                            .decimal_len(20, 2)
                            .null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::PayeeTaxCurrency)
                            .string_len(3)
                            .null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::PayerTax)
                            .decimal_len(20, 2)
                            .null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::PayerTaxCurrency)
                            .string_len(3)
                            .null(),
                    )
                    .col(ColumnDef::new(Transactions::Remark).text().null())
                    .col(
                        ColumnDef::new(Transactions::CreatedAt)
                            .timestamp_with_time_zone()
                            .not_null()
                            .default(Expr::current_timestamp()),
                    )
                    // 暂时移除外键和索引，稍后单独添加
                    .to_owned(),
            )
            .await
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(Transactions::Table).to_owned())
            .await
    }
}

#[derive(DeriveIden)]
enum Transactions {
    Table,
    Id,
    Filename,
    FileIndex,
    ImportRecordId,
    RowNumber,
    RequestTime,
    AccountingAmount,
    Balance,
    AccountingCurrency,
    AccountingType,
    AccountSubject,
    TxnCreateTime,
    TxnCompleteTime,
    MerchantTxnId,
    TradeOrderId,
    Country,
    TxnAmount,
    TxnCurrency,
    PayeeTxnFee,
    PayeeTxnFeeCurrency,
    PayerTxnFee,
    PayerTxnFeeCurrency,
    PayeeTax,
    PayeeTaxCurrency,
    PayerTax,
    PayerTaxCurrency,
    Remark,
    CreatedAt,
}

// 外键和索引将在后续的migration中添加
