pub use sea_orm_migration::prelude::*;

mod m20240101_000001_create_import_records;
mod m20240101_000002_create_transactions;
mod m20240101_000003_create_partitioned_transactions;

pub struct Migrator;

#[async_trait::async_trait]
impl MigratorTrait for Migrator {
    fn migrations() -> Vec<Box<dyn MigrationTrait>> {
        vec![
            Box::new(m20240101_000001_create_import_records::Migration),
            Box::new(m20240101_000002_create_transactions::Migration),
            Box::new(m20240101_000003_create_partitioned_transactions::Migration),
        ]
    }
}
