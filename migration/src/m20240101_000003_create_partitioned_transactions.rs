use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // 首先删除原有的表（如果存在）
        manager
            .drop_table(
                Table::drop()
                    .table(Transactions::Table)
                    .if_exists()
                    .to_owned(),
            )
            .await?;

        // 创建分区主表
        manager
            .create_table(
                Table::create()
                    .table(Transactions::Table)
                    .if_not_exists()
                    .col(
                        ColumnDef::new(Transactions::Id)
                            .uuid()
                            .not_null()
                            .primary_key(),
                    )
                    .col(ColumnDef::new(Transactions::Filename).string().not_null())
                    .col(ColumnDef::new(Transactions::RowNumber).integer().not_null())
                    .col(
                        ColumnDef::new(Transactions::RequestTime)
                            .timestamp_with_time_zone()
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::AccountingAmount)
                            .decimal_len(20, 2)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::Balance)
                            .decimal_len(20, 2)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::AccountingCurrency)
                            .string_len(3)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::AccountingType)
                            .string_len(32)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::AccountSubject)
                            .string_len(64)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::TxnCreateTime)
                            .timestamp_with_time_zone()
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::TxnCompleteTime)
                            .timestamp_with_time_zone()
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::MerchantTxnId)
                            .string_len(64)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::TradeOrderId)
                            .string_len(64)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::Country)
                            .string_len(2)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::TxnAmount)
                            .decimal_len(20, 2)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::TxnCurrency)
                            .string_len(3)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::PayeeTxnFee)
                            .decimal_len(20, 2)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::PayeeTxnFeeCurrency)
                            .string_len(3)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::PayerTxnFee)
                            .decimal_len(20, 2)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::PayerTxnFeeCurrency)
                            .string_len(3)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::PayeeTax)
                            .decimal_len(20, 2)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::PayeeTaxCurrency)
                            .string_len(3)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::PayerTax)
                            .decimal_len(20, 2)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Transactions::PayerTaxCurrency)
                            .string_len(3)
                            .not_null(),
                    )
                    .col(ColumnDef::new(Transactions::Remark).text().null())
                    .col(
                        ColumnDef::new(Transactions::CreatedAt)
                            .timestamp_with_time_zone()
                            .not_null()
                            .default(Expr::current_timestamp()),
                    )
                    .to_owned(),
            )
            .await?;

        // 删除刚创建的表，重新创建为分区表
        manager
            .drop_table(Table::drop().table(Transactions::Table).to_owned())
            .await?;

        // 使用原生 SQL 创建分区表
        let sql = r#"
            CREATE TABLE transactions (
                id UUID NOT NULL,
                filename VARCHAR NOT NULL,
                row_number INTEGER NOT NULL,
                request_time TIMESTAMPTZ NOT NULL,
                accounting_amount DECIMAL(20,2) NOT NULL,
                balance DECIMAL(20,2) NOT NULL,
                accounting_currency VARCHAR(3) NOT NULL,
                accounting_type VARCHAR(32) NOT NULL,
                account_subject VARCHAR(64) NOT NULL,
                txn_create_time TIMESTAMPTZ NOT NULL,
                txn_complete_time TIMESTAMPTZ,
                merchant_txn_id VARCHAR(64),
                trade_order_id VARCHAR(64),
                country VARCHAR(2),
                txn_amount DECIMAL(20,2),
                txn_currency VARCHAR(3),
                payee_txn_fee DECIMAL(20,2),
                payee_txn_fee_currency VARCHAR(3),
                payer_txn_fee DECIMAL(20,2),
                payer_txn_fee_currency VARCHAR(3),
                payee_tax DECIMAL(20,2),
                payee_tax_currency VARCHAR(3),
                payer_tax DECIMAL(20,2),
                payer_tax_currency VARCHAR(3),
                remark TEXT,
                created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id, filename)
            ) PARTITION BY LIST (filename);
        "#;

        manager.get_connection().execute_unprepared(sql).await?;

        // 创建索引
        manager
            .create_index(
                Index::create()
                    .name("idx_transactions_filename")
                    .table(Transactions::Table)
                    .col(Transactions::Filename)
                    .to_owned(),
            )
            .await?;

        manager
            .create_index(
                Index::create()
                    .name("idx_transactions_request_time")
                    .table(Transactions::Table)
                    .col(Transactions::RequestTime)
                    .to_owned(),
            )
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(Transactions::Table).to_owned())
            .await
    }
}

#[derive(DeriveIden)]
enum Transactions {
    Table,
    Id,
    Filename,
    RowNumber,
    RequestTime,
    AccountingAmount,
    Balance,
    AccountingCurrency,
    AccountingType,
    AccountSubject,
    TxnCreateTime,
    TxnCompleteTime,
    MerchantTxnId,
    TradeOrderId,
    Country,
    TxnAmount,
    TxnCurrency,
    PayeeTxnFee,
    PayeeTxnFeeCurrency,
    PayerTxnFee,
    PayerTxnFeeCurrency,
    PayeeTax,
    PayeeTaxCurrency,
    PayerTax,
    PayerTaxCurrency,
    Remark,
    CreatedAt,
}
