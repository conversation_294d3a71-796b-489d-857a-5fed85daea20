#!/bin/bash

# 测试还款利润计算监控功能
# 此脚本用于验证数据检测点是否正常工作

echo "🧪 测试还款利润计算监控功能"
echo "=================================================="

# 检查日志目录
LOG_DIR="./logs"
if [ ! -d "$LOG_DIR" ]; then
    echo "📁 创建日志目录: $LOG_DIR"
    mkdir -p "$LOG_DIR"
fi

# 设置环境变量以启用详细日志
export RUST_LOG="info,repayment_calculation=info,repayment_calculation_internal=info,repayment_normal_profit=info,repayment_penalty_profit=info,repayment_approval_process=info"

echo "🔧 环境变量设置:"
echo "RUST_LOG=$RUST_LOG"
echo ""

# 检查配置文件
CONFIG_FILE="config/config.yml"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 配置文件不存在: $CONFIG_FILE"
    echo "请确保配置文件存在并正确配置"
    exit 1
fi

echo "📋 检查日志配置:"
grep -A 6 "^log:" "$CONFIG_FILE" || echo "未找到日志配置"
echo ""

# 编译项目
echo "🔨 编译项目..."
if ! cargo build --release; then
    echo "❌ 编译失败"
    exit 1
fi
echo "✅ 编译成功"
echo ""

# 提示用户如何测试
echo "🚀 测试步骤:"
echo "1. 启动应用程序:"
echo "   ./target/release/fund_manager"
echo ""
echo "2. 在另一个终端中监控日志:"
echo "   tail -f logs/my-service.log | grep -E '(repayment_calculation|repayment_normal_profit|repayment_penalty_profit|repayment_approval_process)'"
echo ""
echo "3. 执行还款相关操作来触发监控点:"
echo "   - 查询还款记录 (GET /api/repayment/{id})"
echo "   - 创建还款日志 (POST /api/repayment_log)"
echo "   - 更新还款日志状态 (PUT /api/repayment_log/{id})"
echo ""
echo "4. 运行监控分析脚本:"
echo "   ./scripts/monitor_repayment_calculation.sh"
echo ""

# 创建测试用的 curl 命令示例
echo "📝 测试命令示例 (需要先启动应用程序):"
echo ""
echo "# 获取还款记录列表"
echo "curl -X GET 'http://localhost:6310/api/repayment?page=1&limit=10' \\"
echo "  -H 'Authorization: Bearer YOUR_JWT_TOKEN'"
echo ""
echo "# 获取特定还款记录 (会触发利润计算)"
echo "curl -X GET 'http://localhost:6310/api/repayment/REPAYMENT_ID' \\"
echo "  -H 'Authorization: Bearer YOUR_JWT_TOKEN'"
echo ""
echo "# 创建还款日志"
echo "curl -X POST 'http://localhost:6310/api/repayment_log' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \\"
echo "  -d '{"
echo "    \"parent_id\": \"REPAYMENT_ID\","
echo "    \"log_type\": \"REPAY\","
echo "    \"log_value\": \"1000.00\","
echo "    \"log_status\": \"pending\","
echo "    \"remark\": \"测试还款\""
echo "  }'"
echo ""

# 创建简单的日志监控命令
echo "🔍 实时监控命令:"
echo ""
echo "# 监控所有还款计算日志"
echo "tail -f logs/my-service.log | grep --line-buffered -E '(repayment_calculation|repayment_normal_profit|repayment_penalty_profit|repayment_approval_process)' | while read line; do"
echo "  echo \"\$(date '+%H:%M:%S') \$line\""
echo "done"
echo ""

echo "# 监控错误和警告"
echo "tail -f logs/my-service.log | grep --line-buffered -E '(ERROR|WARN).*repayment_' | while read line; do"
echo "  echo \"⚠️  \$(date '+%H:%M:%S') \$line\""
echo "done"
echo ""

# 检查是否有现有的日志文件
LOG_FILE="logs/my-service.log"
if [ -f "$LOG_FILE" ]; then
    echo "📊 现有日志文件分析:"
    echo "文件大小: $(du -h "$LOG_FILE" | cut -f1)"
    echo "总行数: $(wc -l < "$LOG_FILE")"
    echo "还款计算相关日志: $(grep -c "repayment_" "$LOG_FILE" 2>/dev/null || echo "0")"
    echo ""
    
    echo "最近的还款相关日志 (最后5条):"
    grep "repayment_" "$LOG_FILE" | tail -5 || echo "无还款相关日志"
else
    echo "📝 日志文件尚不存在，启动应用程序后将自动创建"
fi

echo ""
echo "✅ 测试准备完成!"
echo "=================================================="
echo ""
echo "💡 提示:"
echo "- 确保数据库服务正在运行"
echo "- 确保 Redis 服务正在运行 (如果配置了)"
echo "- 检查配置文件中的数据库连接信息"
echo "- 使用有效的 JWT token 进行 API 调用"
echo ""
echo "📚 更多信息请参考:"
echo "- docs/REPAYMENT_MONITORING_GUIDE.md"
echo "- scripts/monitor_repayment_calculation.sh"
