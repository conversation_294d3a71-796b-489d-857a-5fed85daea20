#!/bin/bash

# 还款利润计算监控脚本
# 用于分析和监控还款利润计算逻辑的运行情况

LOG_DIR="./logs"
LOG_FILE="$LOG_DIR/my-service.log"
REPORT_DIR="./monitoring_reports"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 创建报告目录
mkdir -p "$REPORT_DIR"

echo "🔍 还款利润计算监控报告 - $TIMESTAMP"
echo "=================================================="

# 检查日志文件是否存在
if [ ! -f "$LOG_FILE" ]; then
    echo "❌ 日志文件不存在: $LOG_FILE"
    echo "请确保应用程序正在运行并生成日志"
    exit 1
fi

echo "📊 分析日志文件: $LOG_FILE"
echo ""

# 1. 统计各类计算的执行次数
echo "1. 计算执行统计"
echo "----------------------------------------"
echo "主要计算流程执行次数:"
grep -c "开始获取还款记录并执行利润计算" "$LOG_FILE" 2>/dev/null || echo "0"

echo "内部计算执行次数:"
grep -c "开始内部利润计算" "$LOG_FILE" 2>/dev/null || echo "0"

echo "正常利润计算次数:"
grep -c "开始计算正常利润" "$LOG_FILE" 2>/dev/null || echo "0"

echo "逾期利润计算次数:"
grep -c "开始计算逾期利润" "$LOG_FILE" 2>/dev/null || echo "0"

echo "还款审核处理次数:"
grep -c "开始处理还款审核通过逻辑" "$LOG_FILE" 2>/dev/null || echo "0"

echo ""

# 2. 检查错误和警告
echo "2. 错误和警告统计"
echo "----------------------------------------"
echo "错误总数:"
grep -c "ERROR.*repayment_" "$LOG_FILE" 2>/dev/null || echo "0"

echo "警告总数:"
grep -c "WARN.*repayment_" "$LOG_FILE" 2>/dev/null || echo "0"

echo ""
echo "最近的错误信息:"
grep "ERROR.*repayment_" "$LOG_FILE" | tail -5 2>/dev/null || echo "无错误"

echo ""

# 3. 分析计算结果
echo "3. 计算结果分析"
echo "----------------------------------------"

# 提取利润计算结果
echo "正常利润计算结果 (最近10次):"
grep "正常利润计算完成" "$LOG_FILE" | grep -o 'calculated_profit="[^"]*"' | tail -10 2>/dev/null || echo "无数据"

echo ""
echo "逾期利润计算结果 (最近10次):"
grep "逾期利润计算完成" "$LOG_FILE" | grep -o 'calculated_penalty_profit="[^"]*"' | tail -10 2>/dev/null || echo "无数据"

echo ""

# 4. 检查数据一致性问题
echo "4. 数据一致性检查"
echo "----------------------------------------"

# 检查零值计算
echo "零值计算次数:"
grep -c "返回零值" "$LOG_FILE" 2>/dev/null || echo "0"

# 检查负值情况
echo "检查是否有负值计算结果:"
if grep -q "calculated.*=\"-" "$LOG_FILE" 2>/dev/null; then
    echo "⚠️  发现负值计算结果:"
    grep "calculated.*=\"-" "$LOG_FILE" | tail -5
else
    echo "✅ 未发现负值计算结果"
fi

echo ""

# 5. 性能分析
echo "5. 性能分析"
echo "----------------------------------------"

# 分析最近的计算活动
echo "最近1小时的计算活动:"
HOUR_AGO=$(date -d '1 hour ago' '+%Y-%m-%d %H:%M:%S' 2>/dev/null || date -v-1H '+%Y-%m-%d %H:%M:%S' 2>/dev/null || echo "")
if [ -n "$HOUR_AGO" ]; then
    grep "开始内部利润计算" "$LOG_FILE" | grep "$HOUR_AGO" | wc -l 2>/dev/null || echo "0"
else
    echo "无法计算时间范围"
fi

echo ""

# 6. 生成详细报告
echo "6. 生成详细报告"
echo "----------------------------------------"

REPORT_FILE="$REPORT_DIR/repayment_calculation_report_$TIMESTAMP.txt"

cat > "$REPORT_FILE" << EOF
还款利润计算监控报告
生成时间: $(date)
日志文件: $LOG_FILE

=== 执行统计 ===
主要计算流程: $(grep -c "开始获取还款记录并执行利润计算" "$LOG_FILE" 2>/dev/null || echo "0")
内部计算: $(grep -c "开始内部利润计算" "$LOG_FILE" 2>/dev/null || echo "0")
正常利润计算: $(grep -c "开始计算正常利润" "$LOG_FILE" 2>/dev/null || echo "0")
逾期利润计算: $(grep -c "开始计算逾期利润" "$LOG_FILE" 2>/dev/null || echo "0")
还款审核处理: $(grep -c "开始处理还款审核通过逻辑" "$LOG_FILE" 2>/dev/null || echo "0")

=== 错误统计 ===
错误总数: $(grep -c "ERROR.*repayment_" "$LOG_FILE" 2>/dev/null || echo "0")
警告总数: $(grep -c "WARN.*repayment_" "$LOG_FILE" 2>/dev/null || echo "0")

=== 最近错误 ===
$(grep "ERROR.*repayment_" "$LOG_FILE" | tail -10 2>/dev/null || echo "无错误")

=== 计算结果样本 ===
正常利润计算结果:
$(grep "正常利润计算完成" "$LOG_FILE" | grep -o 'calculated_profit="[^"]*"' | tail -20 2>/dev/null || echo "无数据")

逾期利润计算结果:
$(grep "逾期利润计算完成" "$LOG_FILE" | grep -o 'calculated_penalty_profit="[^"]*"' | tail -20 2>/dev/null || echo "无数据")

=== 数据一致性 ===
零值计算次数: $(grep -c "返回零值" "$LOG_FILE" 2>/dev/null || echo "0")
负值计算检查: $(if grep -q "calculated.*=\"-" "$LOG_FILE" 2>/dev/null; then echo "发现负值"; else echo "正常"; fi)

EOF

echo "详细报告已保存到: $REPORT_FILE"

# 7. 实时监控模式
echo ""
echo "7. 实时监控选项"
echo "----------------------------------------"
echo "要启动实时监控，请运行:"
echo "tail -f $LOG_FILE | grep -E '(repayment_calculation|repayment_normal_profit|repayment_penalty_profit|repayment_approval_process)'"

echo ""
echo "要监控特定还款记录，请运行:"
echo "tail -f $LOG_FILE | grep 'repayment_id=\"repayment:YOUR_ID\"'"

echo ""
echo "要监控错误，请运行:"
echo "tail -f $LOG_FILE | grep -E '(ERROR|WARN).*repayment_'"

echo ""
echo "✅ 监控分析完成!"
echo "=================================================="
