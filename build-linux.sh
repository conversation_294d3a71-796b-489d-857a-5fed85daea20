#!/bin/bash
# Fund Manager Linux 构建脚本
# 在 Linux 环境中运行此脚本

set -e

echo "=== Fund Manager Linux 构建脚本 ==="

# 检查 Rust 工具链
echo "检查 Rust 工具链..."
if ! command -v rustc &> /dev/null; then
    echo "错误: 未找到 Rust 工具链"
    echo "请先安装 Rust: https://rustup.rs/"
    exit 1
fi

rustc --version
cargo --version

# 检查系统依赖
echo "检查系统依赖..."
if ! pkg-config --exists openssl; then
    echo "警告: 未找到 OpenSSL 开发库"
    echo "Ubuntu/Debian: sudo apt-get install libssl-dev pkg-config"
    echo "CentOS/RHEL: sudo yum install openssl-devel pkgconfig"
    echo "Fedora: sudo dnf install openssl-devel pkgconfig"
fi

# 清理旧的构建产物
echo "清理构建产物..."
cargo clean

# 执行构建
echo "开始构建..."
cargo build --release

if [ $? -eq 0 ]; then
    echo "构建成功！"
    
    # 显示构建产物信息
    EXECUTABLE_PATH="target/release/fund_manager"
    if [ -f "$EXECUTABLE_PATH" ]; then
        echo ""
        echo "构建产物信息:"
        echo "文件路径: $EXECUTABLE_PATH"
        echo "文件大小: $(du -h "$EXECUTABLE_PATH" | cut -f1)"
        echo "修改时间: $(stat -c %y "$EXECUTABLE_PATH")"
        
        # 检查依赖
        echo ""
        echo "动态库依赖:"
        ldd "$EXECUTABLE_PATH" | head -10
    fi
    
    # 创建发布目录
    RELEASE_DIR="release"
    LINUX_RELEASE_DIR="$RELEASE_DIR/linux-x86_64"
    
    echo ""
    echo "准备发布文件..."
    rm -rf "$LINUX_RELEASE_DIR"
    mkdir -p "$LINUX_RELEASE_DIR"
    
    # 复制可执行文件
    cp "$EXECUTABLE_PATH" "$LINUX_RELEASE_DIR/"
    chmod +x "$LINUX_RELEASE_DIR/fund_manager"
    echo "已复制: fund_manager"
    
    # 复制配置文件
    if [ -d "config" ]; then
        cp -r "config" "$LINUX_RELEASE_DIR/"
        echo "已复制: config/"
    fi
    
    # 复制模板文件
    if [ -d "templates" ]; then
        cp -r "templates" "$LINUX_RELEASE_DIR/"
        echo "已复制: templates/"
    fi
    
    # 复制静态资源
    if [ -d "assets" ]; then
        cp -r "assets" "$LINUX_RELEASE_DIR/"
        echo "已复制: assets/"
    fi
    
    # 创建启动脚本
    cat > "$LINUX_RELEASE_DIR/start.sh" << 'EOF'
#!/bin/bash
# Fund Manager 启动脚本

# 设置工作目录
cd $(dirname $0)

# 检查配置文件
if [ ! -f "config/config.yml" ]; then
    echo "错误: 未找到配置文件 config/config.yml"
    exit 1
fi

# 创建必要的目录
mkdir -p logs
mkdir -p tmp/upload
mkdir -p upload

# 启动应用
echo "启动 Fund Manager..."
./fund_manager
EOF
    chmod +x "$LINUX_RELEASE_DIR/start.sh"
    echo "已创建: start.sh"
    
    # 创建系统服务文件
    cat > "$LINUX_RELEASE_DIR/fund-manager.service" << EOF
[Unit]
Description=Fund Manager Service
After=network.target

[Service]
Type=simple
User=fund-manager
Group=fund-manager
WorkingDirectory=/opt/fund-manager
ExecStart=/opt/fund-manager/fund_manager
Restart=always
RestartSec=5
Environment=RUST_LOG=info

[Install]
WantedBy=multi-user.target
EOF
    echo "已创建: fund-manager.service"
    
    # 创建安装脚本
    cat > "$LINUX_RELEASE_DIR/install.sh" << 'EOF'
#!/bin/bash
# Fund Manager 安装脚本

set -e

echo "=== Fund Manager 安装脚本 ==="

# 检查是否以 root 权限运行
if [ "$EUID" -ne 0 ]; then
    echo "请以 root 权限运行此脚本"
    exit 1
fi

# 创建用户和组
if ! id "fund-manager" &>/dev/null; then
    echo "创建用户 fund-manager..."
    useradd -r -s /bin/false fund-manager
fi

# 创建安装目录
INSTALL_DIR="/opt/fund-manager"
echo "创建安装目录: $INSTALL_DIR"
mkdir -p "$INSTALL_DIR"

# 复制文件
echo "复制文件..."
cp -r * "$INSTALL_DIR/"
chown -R fund-manager:fund-manager "$INSTALL_DIR"
chmod +x "$INSTALL_DIR/fund_manager"
chmod +x "$INSTALL_DIR/start.sh"

# 安装系统服务
echo "安装系统服务..."
cp fund-manager.service /etc/systemd/system/
systemctl daemon-reload
systemctl enable fund-manager

echo ""
echo "安装完成！"
echo ""
echo "配置说明:"
echo "1. 编辑配置文件: $INSTALL_DIR/config/config.yml"
echo "2. 启动服务: systemctl start fund-manager"
echo "3. 查看状态: systemctl status fund-manager"
echo "4. 查看日志: journalctl -u fund-manager -f"
echo ""
EOF
    chmod +x "$LINUX_RELEASE_DIR/install.sh"
    echo "已创建: install.sh"
    
    # 创建 README
    cat > "$LINUX_RELEASE_DIR/README.txt" << EOF
# Fund Manager - Linux 版本

## 系统要求
- Linux x86_64
- glibc 2.17 或更高版本

## 快速安装（推荐）
以 root 权限运行安装脚本：
\`\`\`bash
sudo ./install.sh
\`\`\`

## 手动安装
1. 解压文件到目标目录
2. 确保 fund_manager 有执行权限: chmod +x fund_manager
3. 修改 config/config.yml 配置文件
4. 运行: ./start.sh 或 ./fund_manager

## 目录结构
- fund_manager: 主程序
- config/: 配置文件目录
  - config.yml: 主配置文件
  - menus.yml: 菜单配置
  - permissions.yml: 权限配置
  - certs/: SSL证书文件
- templates/: HTML 模板文件
- assets/: 静态资源文件
- start.sh: 启动脚本
- install.sh: 系统安装脚本
- fund-manager.service: systemd 服务文件

## 配置说明
在首次运行前，请修改 config/config.yml 文件中的以下配置：

### 数据库配置
\`\`\`yaml
database:
  host: "localhost"
  port: 3306
  username: "your_username"
  password: "your_password"
  database: "fund_manager"
\`\`\`

### Redis 配置
\`\`\`yaml
redis:
  host: "localhost"
  port: 6379
  password: ""
  database: 0
\`\`\`

### 服务器配置
\`\`\`yaml
server:
  host: "0.0.0.0"
  port: 6310
\`\`\`

## 系统服务管理
如果使用了安装脚本，可以使用以下命令管理服务：

\`\`\`bash
# 启动服务
sudo systemctl start fund-manager

# 停止服务
sudo systemctl stop fund-manager

# 重启服务
sudo systemctl restart fund-manager

# 查看状态
sudo systemctl status fund-manager

# 查看日志
sudo journalctl -u fund-manager -f

# 开机自启
sudo systemctl enable fund-manager

# 禁用开机自启
sudo systemctl disable fund-manager
\`\`\`

## 注意事项
- 首次运行前请检查配置文件
- 确保数据库连接配置正确
- 确保 Redis 服务正在运行
- 日志文件将保存在 logs/ 目录下
- 上传文件将保存在 upload/ 目录下

## 访问地址
启动成功后，可通过以下地址访问：
- HTTP: http://localhost:6310
- 如果配置了 SSL，则使用 HTTPS

## 故障排除
1. 如果启动失败，请检查：
   - 配置文件是否正确
   - 数据库是否可连接
   - Redis 是否正在运行
   - 端口是否被占用

2. 查看日志文件：
   - 日志文件位于 logs/ 目录下
   - 系统服务日志: journalctl -u fund-manager

构建时间: $(date '+%Y-%m-%d %H:%M:%S')
构建目标: Linux x86_64
版本: v1.0.0
EOF
    echo "已创建: README.txt"
    
    # 创建压缩包
    echo ""
    echo "创建发布压缩包..."
    cd "$RELEASE_DIR"
    tar -czf "fund-manager-linux-x86_64.tar.gz" linux-x86_64/
    cd ..
    
    echo "发布包已创建: $RELEASE_DIR/fund-manager-linux-x86_64.tar.gz"
    
    # 显示压缩包信息
    if [ -f "$RELEASE_DIR/fund-manager-linux-x86_64.tar.gz" ]; then
        echo "压缩包大小: $(du -h "$RELEASE_DIR/fund-manager-linux-x86_64.tar.gz" | cut -f1)"
    fi
    
    echo ""
    echo "发布文件已准备完成: $LINUX_RELEASE_DIR"
    
else
    echo "构建失败！"
    exit 1
fi

echo ""
echo "=== 构建完成 ==="
EOF
