//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.14

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq)]
#[sea_orm(table_name = "transactions")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub filename: String,
    pub row_number: i32,
    pub request_time: DateTimeWithTimeZone,
    #[sea_orm(column_type = "Decimal(Some((20, 2)))")]
    pub accounting_amount: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 2)))")]
    pub balance: Option<Decimal>,
    pub accounting_currency: String,
    pub accounting_type: String,
    pub account_subject: String,
    pub txn_create_time: DateTimeWithTimeZone,
    pub txn_complete_time: Option<DateTimeWithTimeZone>,
    pub merchant_txn_id: Option<String>,
    pub trade_order_id: Option<String>,
    pub country: Option<String>,
    #[sea_orm(column_type = "Decimal(Some((20, 2)))", nullable)]
    pub txn_amount: Option<Decimal>,
    pub txn_currency: Option<String>,
    #[sea_orm(column_type = "Decimal(Some((20, 2)))", nullable)]
    pub payee_txn_fee: Option<Decimal>,
    pub payee_txn_fee_currency: Option<String>,
    #[sea_orm(column_type = "Decimal(Some((20, 2)))", nullable)]
    pub payer_txn_fee: Option<Decimal>,
    pub payer_txn_fee_currency: Option<String>,
    #[sea_orm(column_type = "Decimal(Some((20, 2)))", nullable)]
    pub payee_tax: Option<Decimal>,
    pub payee_tax_currency: Option<String>,
    #[sea_orm(column_type = "Decimal(Some((20, 2)))", nullable)]
    pub payer_tax: Option<Decimal>,
    pub payer_tax_currency: Option<String>,
    #[sea_orm(column_type = "Text", nullable)]
    pub remark: Option<String>,
    pub created_at: DateTimeWithTimeZone,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
