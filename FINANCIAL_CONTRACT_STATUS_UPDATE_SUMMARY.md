# 金融合同状态更新功能实现总结

## 概述

成功在 `FinancialContractService` 中添加了 `update_status()` 函数，实现了与 `RepaymentService::update_status()` 相同的状态管理逻辑，支持金融合同的状态流转和审核功能。

## 实现的功能

### 1. 金融合同状态字典

在 `src/dtos/dict.rs` 中添加了新的状态字典：

```rust
/// 金融合同状态字典
/// 按照业务流程顺序排列，支持状态流转
pub const FINANCIAL_CONTRACT_STATUS: [(&str, &str); 5] = [
    ("draft", "草稿"),
    ("new", "待审核"),
    ("processing", "已审核"),
    ("done", "已完成"),
    ("expired", "已过期"),
];
```

**状态流转顺序**：

- `draft` (草稿) → `new` (待审核) → `processing` (已审核) → `done` (已完成)
- `expired` (已过期) 为特殊状态

### 2. 字典辅助方法

在 `DictHelper` 中添加了完整的金融合同状态管理方法：

#### 基础查询方法

```rust
// 根据值获取标签
DictHelper::get_financial_contract_status_label("draft") // 返回 Some("草稿")

// 根据值获取索引
DictHelper::get_financial_contract_status_index("draft") // 返回 Some(0)

// 验证状态是否有效
DictHelper::is_valid_financial_contract_status("draft") // 返回 true
```

#### 状态流转方法

```rust
// 获取下一个状态（用于审核通过）
DictHelper::get_next_financial_contract_status("draft") // 返回 Some(("new", "待审核"))

// 获取上一个状态（用于审核拒绝）
DictHelper::get_prev_financial_contract_status("new") // 返回 Some(("draft", "草稿"))
```

### 3. update_status 函数实现

#### 函数签名

```rust
pub async fn update_status(id: String, u_id: String, confirm: bool) -> AppResult<String>
```

#### 参数说明

- `id`: 金融合同 ID
- `u_id`: 操作用户 ID
- `confirm`: 是否确认（true: 审核通过，false: 审核拒绝）

#### 核心逻辑

##### 1. 权限验证

```rust
// 通过u_id获取User的信息，检查权限
let user = UserService::get_by_id(u_id.clone()).await?;
if !user.is_admin {
    return Err(anyhow!("权限不足，只有管理员可以审核金融合同").into());
}
```

**权限要求**：只有管理员（`is_admin = true`）才能执行状态更新操作。

##### 2. 状态验证

```rust
// 获取当前状态
let current_status = financial_contract.status.as_str();

// 验证当前状态是否有效
if !DictHelper::is_valid_financial_contract_status(current_status) {
    return Err(anyhow!("无效的当前状态: {}", current_status).into());
}
```

##### 3. 状态流转逻辑

```rust
let (new_status, new_status_label) = if confirm {
    // 审核通过，向后推一个状态
    match DictHelper::get_next_financial_contract_status(current_status) {
        Some((status, label)) => (status, label),
        None => {
            return Err(anyhow!("当前状态 {} 已是最终状态，无法继续推进", current_status).into());
        }
    }
} else {
    // 审核拒绝，向前推一个状态
    match DictHelper::get_prev_financial_contract_status(current_status) {
        Some((status, label)) => (status, label),
        None => {
            return Err(anyhow!("当前状态 {} 已是初始状态，无法回退", current_status).into());
        }
    }
};
```

**状态流转规则**：

- **审核通过** (`confirm = true`): 状态向前推进一步
- **审核拒绝** (`confirm = false`): 状态向后回退一步

##### 4. 数据更新

```rust
let update_data = FinancialContractUpdate {
    id: id.clone(),
    name: financial_contract.name.clone(),
    status: Some(new_status.to_string()),
    product_category: Vec::new(), // 使用空向量作为默认值
    ..Default::default()
};

FinancialContractBmc::update(update_data).await?;
```

##### 5. 审核日志记录

```rust
let log_data = RepaymentLogCreate {
    parent_id: id,
    log_type: "audit".to_string(),
    log_value: Some(log_value),
    log_date: None,
    log_status: Some(log_status.to_string()),
    remark: Some(format!(
        "管理员{}了金融合同状态变更：{} -> {}",
        if confirm { "审核通过" } else { "审核拒绝" },
        current_status_label,
        new_status_label
    )),
    creater_id: Some(u_id),
    creater_name: Some(user.username),
    ..Default::default()
};

RepaymentLogService::create(None, log_data).await?;
```

**日志内容**：

- `log_type`: "audit" (审核类型)
- `log_status`: "approved" 或 "rejected"
- `log_value`: JSON 格式的状态变更详情
- `remark`: 人性化的操作描述

## 使用示例

### 1. 审核通过示例

```rust
// 将金融合同从"草稿"状态推进到"待审核"状态
let result = FinancialContractService::update_status(
    "contract:123".to_string(),
    "user:456".to_string(),
    true  // 审核通过
).await?;

// 返回: "金融合同状态已更新：草稿 -> 待审核"
```

### 2. 审核拒绝示例

```rust
// 将金融合同从"待审核"状态回退到"草稿"状态
let result = FinancialContractService::update_status(
    "contract:123".to_string(),
    "user:456".to_string(),
    false  // 审核拒绝
).await?;

// 返回: "金融合同状态已更新：待审核 -> 草稿"
```

### 3. 状态流转示例

#### 正向流转（审核通过）

```
draft (草稿)
    ↓ confirm=true
new (待审核)
    ↓ confirm=true
processing (已审核)
    ↓ confirm=true
done (已完成)
```

#### 反向流转（审核拒绝）

```
done (已完成)
    ↓ confirm=false
processing (已审核)
    ↓ confirm=false
new (待审核)
    ↓ confirm=false
draft (草稿)
```

## 错误处理

### 1. 权限错误

```
权限不足，只有管理员可以审核金融合同
```

### 2. 状态验证错误

```
无效的当前状态: invalid_status
```

### 3. 状态流转错误

```
当前状态 done 已是最终状态，无法继续推进
当前状态 draft 已是初始状态，无法回退
```

### 4. 数据查询错误

```
FinancialContract not found
```

## 测试覆盖

### 1. 字典功能测试

```rust
#[test]
fn test_financial_contract_status_operations() {
    // 测试获取标签
    assert_eq!(
        DictHelper::get_financial_contract_status_label("draft"),
        Some("草稿")
    );

    // 测试状态流转
    assert_eq!(
        DictHelper::get_next_financial_contract_status("draft"),
        Some(("new", "待审核"))
    );

    // 测试边界条件
    assert_eq!(
        DictHelper::get_prev_financial_contract_status("draft"),
        None
    );
}
```

### 2. 业务逻辑测试建议

```rust
#[tokio::test]
async fn test_update_status_success() {
    // 测试正常的状态更新流程
}

#[tokio::test]
async fn test_update_status_permission_denied() {
    // 测试权限不足的情况
}

#[tokio::test]
async fn test_update_status_invalid_transition() {
    // 测试无效的状态流转
}
```

## 与 RepaymentService 的对比

### 相同点

1. **权限验证**：都要求管理员权限
2. **状态流转逻辑**：都支持双向状态流转
3. **审核日志**：都会记录详细的操作日志
4. **错误处理**：都有完善的错误处理机制

### 不同点

1. **状态字典**：使用不同的状态字典
2. **实体类型**：操作不同的业务实体
3. **日志描述**：使用不同的业务术语

## 依赖关系

### 新增导入

```rust
use crate::{
    dtos::{
        dict::DictHelper,
        repayment_log::RepaymentLogCreate,
    },
    services::{
        repayment_log::RepaymentLogService,
        user::UserService,
    },
};
```

### 功能依赖

- `DictHelper`: 状态字典管理
- `UserService`: 用户权限验证
- `RepaymentLogService`: 审核日志记录
- `FinancialContractBmc`: 数据库操作

## 编译验证

### 编译结果

```bash
cargo check
```

**结果**: ✅ 编译成功，无错误

### 警告处理

- `update_status` 函数被标记为未使用，这是正常的，因为还未在路由中调用
- 所有新增的字典辅助方法也被标记为未使用，待后续使用时会自动消除

## 后续扩展建议

### 1. 路由集成

```rust
// 在 financial_contract.rs 路由中添加状态更新端点
#[endpoint(tags("financial_contract"))]
async fn update_financial_contract_status(
    id: PathParam<String>,
    req: JsonBody<CommonParams>,
    depot: &mut Depot,
) -> AppWriter<String> {
    let u_id = depot.get::<String>("user_id").unwrap().to_owned();
    let confirm = req.0.confirm.unwrap_or(false);
    let result = FinancialContractService::update_status(id.0, u_id, confirm).await;
    AppWriter(result)
}
```

### 2. 前端集成

```javascript
// 审核通过
const approveContract = async (contractId) => {
  const response = await fetch(`/api/financial_contract/${contractId}`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ confirm: true }),
  });
  return response.json();
};

// 审核拒绝
const rejectContract = async (contractId) => {
  const response = await fetch(`/api/financial_contract/${contractId}`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ confirm: false }),
  });
  return response.json();
};
```

### 3. 状态机扩展

```rust
// 可以考虑添加更复杂的状态流转规则
impl FinancialContractService {
    pub async fn can_transition_to(
        current_status: &str,
        target_status: &str
    ) -> bool {
        // 实现复杂的状态流转验证逻辑
    }
}
```

## 总结

这次实现成功地为金融合同添加了完整的状态管理功能：

- ✅ **状态字典完整**：定义了 5 个业务状态和完整的流转逻辑
- ✅ **权限控制严格**：只有管理员才能执行状态更新
- ✅ **状态流转灵活**：支持审核通过和拒绝的双向流转
- ✅ **日志记录详细**：完整记录每次状态变更的操作信息
- ✅ **错误处理完善**：覆盖各种异常情况
- ✅ **代码复用性高**：与 RepaymentService 保持一致的设计模式

通过这次实现，金融合同现在具备了完整的状态管理和审核流程，为业务流程的自动化和规范化提供了强有力的支持。
