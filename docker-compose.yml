version: '3.8'

services:
  fund-manager:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: fund-manager
    restart: unless-stopped
    ports:
      - "6310:6310"
    volumes:
      - ./config:/app/config:ro
      - ./logs:/app/logs
      - ./upload:/app/upload
      - ./tmp:/app/tmp
    environment:
      - RUST_LOG=info
      - RUST_BACKTRACE=1
    networks:
      - fund-manager-network
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6310/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis:
    image: redis:7-alpine
    container_name: fund-manager-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    networks:
      - fund-manager-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 可选：Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: fund-manager-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - fund-manager-network
    depends_on:
      - fund-manager
    profiles:
      - with-nginx

volumes:
  redis-data:
    driver: local

networks:
  fund-manager-network:
    driver: bridge
