# Fund Manager Windows 构建脚本
param(
    [string]$Profile = "release",
    [switch]$Clean = $false
)

Write-Host "=== Fund Manager Windows 构建脚本 ===" -ForegroundColor Green
Write-Host "构建配置: $Profile" -ForegroundColor Yellow

# 检查 Rust 工具链
Write-Host "`n检查 Rust 工具链..." -ForegroundColor Cyan
rustc --version
cargo --version

# 清理构建产物（如果需要）
if ($Clean) {
    Write-Host "`n清理构建产物..." -ForegroundColor Cyan
    cargo clean
}

# 执行构建
Write-Host "`n开始构建..." -ForegroundColor Cyan
$buildArgs = @("build")

if ($Profile -eq "release") {
    $buildArgs += "--release"
}

Write-Host "构建命令: cargo $($buildArgs -join ' ')" -ForegroundColor Gray

try {
    & cargo @buildArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`n构建成功！" -ForegroundColor Green
        
        # 显示构建产物信息
        $targetDir = "target\$Profile"
        $executable = "fund_manager.exe"
        $executablePath = Join-Path $targetDir $executable
        
        if (Test-Path $executablePath) {
            $fileInfo = Get-Item $executablePath
            Write-Host "`n构建产物信息:" -ForegroundColor Cyan
            Write-Host "文件路径: $executablePath" -ForegroundColor White
            Write-Host "文件大小: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" -ForegroundColor White
            Write-Host "修改时间: $($fileInfo.LastWriteTime)" -ForegroundColor White
        }
        
        # 创建发布目录
        $releaseDir = "release"
        if (-not (Test-Path $releaseDir)) {
            New-Item -ItemType Directory -Path $releaseDir | Out-Null
        }
        
        # 准备发布文件
        Write-Host "`n准备发布文件..." -ForegroundColor Cyan
        $releaseTargetDir = Join-Path $releaseDir "windows-x86_64"
        if (Test-Path $releaseTargetDir) {
            Remove-Item $releaseTargetDir -Recurse -Force
        }
        New-Item -ItemType Directory -Path $releaseTargetDir | Out-Null
        
        # 复制可执行文件
        if (Test-Path $executablePath) {
            Copy-Item $executablePath $releaseTargetDir
            Write-Host "已复制: $executable" -ForegroundColor Green
        }
        
        # 复制配置文件
        if (Test-Path "config") {
            Copy-Item "config" $releaseTargetDir -Recurse
            Write-Host "已复制: config/" -ForegroundColor Green
        }
        
        # 复制模板文件
        if (Test-Path "templates") {
            Copy-Item "templates" $releaseTargetDir -Recurse
            Write-Host "已复制: templates/" -ForegroundColor Green
        }
        
        # 复制静态资源
        if (Test-Path "assets") {
            Copy-Item "assets" $releaseTargetDir -Recurse
            Write-Host "已复制: assets/" -ForegroundColor Green
        }
        
        # 创建启动脚本
        $startScript = @'
@echo off
REM Fund Manager 启动脚本

REM 设置工作目录
cd /d "%~dp0"

REM 检查配置文件
if not exist "config\config.yml" (
    echo 错误: 未找到配置文件 config\config.yml
    pause
    exit /b 1
)

REM 创建必要的目录
if not exist "logs" mkdir logs
if not exist "tmp\upload" mkdir tmp\upload
if not exist "upload" mkdir upload

REM 启动应用
echo 启动 Fund Manager...
fund_manager.exe
pause
'@
        $startScriptPath = Join-Path $releaseTargetDir "start.bat"
        $startScript | Out-File -FilePath $startScriptPath -Encoding UTF8
        Write-Host "已创建: start.bat" -ForegroundColor Green
        
        # 创建 README
        $readme = @"
# Fund Manager - Windows 版本

## 系统要求
- Windows 10 或更高版本
- x86_64 架构

## 安装说明
1. 解压文件到目标目录
2. 修改 config/config.yml 配置文件
3. 双击 start.bat 或直接运行 fund_manager.exe

## 目录结构
- fund_manager.exe: 主程序
- config/: 配置文件目录
- templates/: HTML 模板文件
- assets/: 静态资源文件
- start.bat: 启动脚本

## 注意事项
- 首次运行前请检查配置文件
- 确保数据库连接配置正确
- 日志文件将保存在 logs/ 目录下

构建时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
构建目标: Windows x86_64
"@
        $readmePath = Join-Path $releaseTargetDir "README.txt"
        $readme | Out-File -FilePath $readmePath -Encoding UTF8
        Write-Host "已创建: README.txt" -ForegroundColor Green
        
        # 创建压缩包
        Write-Host "`n创建发布压缩包..." -ForegroundColor Cyan
        $zipPath = Join-Path $releaseDir "fund-manager-windows-x86_64.zip"
        if (Test-Path $zipPath) {
            Remove-Item $zipPath -Force
        }
        
        Compress-Archive -Path "$releaseTargetDir\*" -DestinationPath $zipPath -Force
        Write-Host "发布包已创建: $zipPath" -ForegroundColor Green
        
        # 显示压缩包信息
        if (Test-Path $zipPath) {
            $zipInfo = Get-Item $zipPath
            Write-Host "压缩包大小: $([math]::Round($zipInfo.Length / 1MB, 2)) MB" -ForegroundColor White
        }
        
        Write-Host "`n发布文件已准备完成: $releaseTargetDir" -ForegroundColor Green
        
    } else {
        Write-Host "`n构建失败！退出代码: $LASTEXITCODE" -ForegroundColor Red
        exit $LASTEXITCODE
    }
} catch {
    Write-Host "`n构建过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n=== 构建完成 ===" -ForegroundColor Green
